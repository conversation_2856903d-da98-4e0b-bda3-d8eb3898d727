/**
 * AI智能赋能页面专用脚本
 * 提供AI功能演示和交互效果
 */

document.addEventListener('DOMContentLoaded', function() {
    initAIEnhancementPage();
});

/**
 * 初始化AI智能赋能页面
 */
function initAIEnhancementPage() {
    // 初始化AI大脑动画
    initAIBrainAnimation();
    
    // 初始化功能特性动画
    initFeatureAnimations();
    
    // 初始化AI演示
    initAIDemo();
    
    // 初始化效果展示
    initBenefitsAnimation();
    
    // 初始化交互功能
    initInteractiveFeatures();
}

/**
 * 初始化AI大脑动画
 */
function initAIBrainAnimation() {
    const aiBrain = document.querySelector('.ai-brain');
    
    if (!aiBrain) return;
    
    // 增强大脑动画效果
    aiBrain.addEventListener('mouseenter', function() {
        this.style.animation = 'brainPulse 1s ease-in-out infinite, brainGlow 2s ease-in-out infinite';
    });
    
    aiBrain.addEventListener('mouseleave', function() {
        this.style.animation = 'brainPulse 3s ease-in-out infinite';
    });
    
    // 添加点击效果
    aiBrain.addEventListener('click', function() {
        triggerAIThinking();
    });
    
    // 添加增强动画样式
    addBrainAnimationStyles();
}

/**
 * 添加大脑动画样式
 */
function addBrainAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes brainGlow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            }
            50% {
                box-shadow: 0 0 40px rgba(102, 126, 234, 0.6);
            }
        }
        
        @keyframes thinkingPulse {
            0%, 100% {
                transform: scale(1);
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }
            25% {
                transform: scale(1.1);
                background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            }
            50% {
                transform: scale(1.05);
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            }
            75% {
                transform: scale(1.15);
                background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            }
        }
        
        .ai-brain.thinking {
            animation: thinkingPulse 0.8s ease-in-out 3;
        }
        
        .connection.active {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
        }
    `;
    document.head.appendChild(style);
}

/**
 * 触发AI思考动画
 */
function triggerAIThinking() {
    const aiBrain = document.querySelector('.ai-brain');
    const connections = document.querySelectorAll('.connection');
    
    aiBrain.classList.add('thinking');
    
    // 激活连接线
    connections.forEach((connection, index) => {
        setTimeout(() => {
            connection.classList.add('active');
        }, index * 200);
    });
    
    // 显示思考提示
    showThinkingTooltip();
    
    // 重置动画
    setTimeout(() => {
        aiBrain.classList.remove('thinking');
        connections.forEach(connection => {
            connection.classList.remove('active');
        });
    }, 2400);
}

/**
 * 显示思考提示
 */
function showThinkingTooltip() {
    const aiBrain = document.querySelector('.ai-brain');
    
    const tooltip = document.createElement('div');
    tooltip.className = 'thinking-tooltip';
    tooltip.innerHTML = `
        <div class="tooltip-content">
            <i class="fas fa-lightbulb"></i>
            AI正在思考中...
        </div>
    `;
    
    aiBrain.appendChild(tooltip);
    
    setTimeout(() => {
        tooltip.remove();
    }, 2000);
    
    // 添加提示样式
    if (!document.querySelector('#thinking-tooltip-styles')) {
        const style = document.createElement('style');
        style.id = 'thinking-tooltip-styles';
        style.textContent = `
            .thinking-tooltip {
                position: absolute;
                top: -60px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.9rem;
                white-space: nowrap;
                animation: fadeInUp 0.3s ease, fadeOut 0.3s ease 1.7s forwards;
                z-index: 1000;
            }
            
            .tooltip-content {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
            
            @keyframes fadeOut {
                to {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-10px);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 初始化功能特性动画
 */
function initFeatureAnimations() {
    const featureItems = document.querySelectorAll('.feature-item');
    const operationEnhancements = document.querySelectorAll('.operation-enhancement');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                    addFeatureHoverEffect(entry.target);
                }, index * 100);
            }
        });
    }, {
        threshold: 0.2,
        rootMargin: '0px 0px -50px 0px'
    });
    
    [...featureItems, ...operationEnhancements].forEach(item => {
        observer.observe(item);
    });
}

/**
 * 添加功能悬停效果
 */
function addFeatureHoverEffect(element) {
    element.addEventListener('mouseenter', function() {
        const icon = this.querySelector('.feature-icon, .enhancement-icon');
        if (icon) {
            icon.style.transform = 'scale(1.1) rotate(5deg)';
            icon.style.transition = 'transform 0.3s ease';
        }
        
        this.style.transform = 'translateY(-5px) scale(1.02)';
        this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.15)';
    });
    
    element.addEventListener('mouseleave', function() {
        const icon = this.querySelector('.feature-icon, .enhancement-icon');
        if (icon) {
            icon.style.transform = '';
        }
        
        this.style.transform = '';
        this.style.boxShadow = '';
    });
}

/**
 * 初始化AI演示
 */
function initAIDemo() {
    createAIDemoInterface();
    bindDemoEvents();
}

/**
 * 创建AI演示界面
 */
function createAIDemoInterface() {
    const aiOverview = document.querySelector('.ai-overview .container');
    
    if (!aiOverview) return;
    
    const demoContainer = document.createElement('div');
    demoContainer.className = 'ai-demo-container';
    demoContainer.innerHTML = `
        <div class="demo-section">
            <h3>AI功能演示</h3>
            <div class="demo-tabs">
                <button class="demo-tab active" data-demo="voice">语音识别</button>
                <button class="demo-tab" data-demo="analysis">智能分析</button>
                <button class="demo-tab" data-demo="dispatch">自动分派</button>
                <button class="demo-tab" data-demo="assistant">智能助手</button>
            </div>
            <div class="demo-content">
                <div class="demo-panel active" id="voice-demo">
                    <div class="demo-interface">
                        <div class="voice-input">
                            <button class="record-btn" id="recordBtn">
                                <i class="fas fa-microphone"></i>
                                点击开始录音
                            </button>
                            <div class="voice-waves" style="display: none;">
                                <div class="wave"></div>
                                <div class="wave"></div>
                                <div class="wave"></div>
                                <div class="wave"></div>
                            </div>
                        </div>
                        <div class="transcription-result" id="transcriptionResult">
                            <p class="placeholder">语音转文字结果将在这里显示...</p>
                        </div>
                    </div>
                </div>
                
                <div class="demo-panel" id="analysis-demo">
                    <div class="demo-interface">
                        <div class="analysis-input">
                            <textarea placeholder="输入工单内容进行智能分析..." id="analysisInput"></textarea>
                            <button class="analyze-btn" id="analyzeBtn">开始分析</button>
                        </div>
                        <div class="analysis-result" id="analysisResult">
                            <p class="placeholder">分析结果将在这里显示...</p>
                        </div>
                    </div>
                </div>
                
                <div class="demo-panel" id="dispatch-demo">
                    <div class="demo-interface">
                        <div class="dispatch-scenario">
                            <h4>工单信息</h4>
                            <div class="scenario-card">
                                <p><strong>诉求：</strong>小区旁边工地半夜施工，噪音扰民</p>
                                <p><strong>地址：</strong>朝阳区建国路88号小区</p>
                                <p><strong>时间：</strong>2024年1月15日 23:30</p>
                            </div>
                            <button class="dispatch-btn" id="dispatchBtn">AI智能分派</button>
                        </div>
                        <div class="dispatch-result" id="dispatchResult">
                            <p class="placeholder">分派结果将在这里显示...</p>
                        </div>
                    </div>
                </div>
                
                <div class="demo-panel" id="assistant-demo">
                    <div class="demo-interface">
                        <div class="assistant-container">
                            <div class="assistant-header">
                                <h4><i class="fas fa-headset"></i> 智能助手面板</h4>
                                <div class="status-indicator active">
                                    <span class="status-dot"></span>
                                    实时辅助中
                                </div>
                            </div>
                            <div class="assistant-content">
                                <div class="assistance-section">
                                    <h5><i class="fas fa-lightbulb"></i> 实时建议</h5>
                                    <div class="suggestion-item">
                                        <div class="suggestion-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="suggestion-content">
                                            <strong>紧急程度建议：</strong>高优先级
                                            <p>检测到"停水"关键词，建议设为高优先级处理</p>
                                        </div>
                                    </div>
                                    <div class="suggestion-item">
                                        <div class="suggestion-icon">
                                            <i class="fas fa-route"></i>
                                        </div>
                                        <div class="suggestion-content">
                                            <strong>分派建议：</strong>市水务局
                                            <p>根据问题类型和地址，建议分派至市水务局处理</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="assistance-section">
                                    <h5><i class="fas fa-book"></i> 知识库推荐</h5>
                                    <div class="knowledge-item">
                                        <i class="fas fa-file-text"></i>
                                        <span>供水管道维修标准流程</span>
                                    </div>
                                    <div class="knowledge-item">
                                        <i class="fas fa-phone"></i>
                                        <span>停水问题标准话术</span>
                                    </div>
                                </div>
                                <div class="assistance-section">
                                    <h5><i class="fas fa-history"></i> 相似案例</h5>
                                    <div class="case-item">
                                        <div class="case-info">
                                            <strong>工单 #2024010156</strong>
                                            <span class="case-similarity">相似度: 95%</span>
                                        </div>
                                        <p>同一区域供水管道爆裂，处理时长: 4小时</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    aiOverview.appendChild(demoContainer);
    addDemoStyles();
}

/**
 * 添加演示样式
 */
function addDemoStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .ai-demo-container {
            margin-top: 3rem;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .demo-section h3 {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .demo-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .demo-tab {
            padding: 0.75rem 1.5rem;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }
        
        .demo-tab:hover {
            border-color: #667eea;
            color: #667eea;
        }
        
        .demo-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }
        
        .demo-content {
            min-height: 300px;
        }
        
        .demo-panel {
            display: none;
        }
        
        .demo-panel.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }
        
        .demo-interface {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
        }
        
        .voice-input {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .record-btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0 auto;
        }
        
        .record-btn:hover {
            transform: scale(1.05);
        }
        
        .record-btn.recording {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            animation: pulse 1s infinite;
        }
        
        .voice-waves {
            display: flex;
            justify-content: center;
            gap: 0.25rem;
            margin-top: 1rem;
        }
        
        .wave {
            width: 4px;
            height: 20px;
            background: #667eea;
            border-radius: 2px;
            animation: waveAnimation 1s ease-in-out infinite;
        }
        
        .wave:nth-child(2) { animation-delay: 0.1s; }
        .wave:nth-child(3) { animation-delay: 0.2s; }
        .wave:nth-child(4) { animation-delay: 0.3s; }
        
        @keyframes waveAnimation {
            0%, 100% { height: 20px; }
            50% { height: 40px; }
        }
        
        .transcription-result,
        .analysis-result,
        .dispatch-result {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            min-height: 100px;
            border: 1px solid #e9ecef;
        }
        
        .placeholder {
            color: #999;
            font-style: italic;
        }
        
        .analysis-input {
            margin-bottom: 1rem;
        }
        
        .analysis-input textarea {
            width: 100%;
            height: 100px;
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            resize: vertical;
            font-family: inherit;
        }
        
        .analyze-btn,
        .dispatch-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 20px;
            cursor: pointer;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }
        
        .analyze-btn:hover,
        .dispatch-btn:hover {
            transform: translateY(-2px);
        }
        
        .scenario-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }
        
        .scenario-card p {
            margin: 0.5rem 0;
        }
        
        .chat-container {
            height: 300px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: white;
            border-radius: 8px 8px 0 0;
            border: 1px solid #e9ecef;
        }
        
        .message {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 10px;
        }
        
        .message.assistant {
            background: #f8f9fa;
        }
        
        .message.user {
            background: #667eea;
            color: white;
            flex-direction: row-reverse;
        }
        
        .chat-input {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            background: white;
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 8px 8px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            outline: none;
        }
        
        .chat-input button {
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @media (max-width: 768px) {
            .demo-tabs {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-tab {
                width: 200px;
                text-align: center;
            }
        }

        .assistant-container {
            background: white;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            overflow: hidden;
            height: 400px;
            display: flex;
            flex-direction: column;
        }

        .assistant-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .assistant-header h4 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .assistant-content {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .assistance-section {
            margin-bottom: 1.5rem;
        }

        .assistance-section h5 {
            color: #333;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .suggestion-item {
            display: flex;
            gap: 0.75rem;
            padding: 0.75rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            border-left: 4px solid #667eea;
        }

        .suggestion-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .suggestion-content strong {
            color: #333;
            font-size: 0.9rem;
        }

        .suggestion-content p {
            margin: 0.25rem 0 0 0;
            color: #666;
            font-size: 0.8rem;
        }

        .knowledge-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: white;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.85rem;
        }

        .knowledge-item:hover {
            background: #e9ecef;
        }

        .knowledge-item i {
            color: #667eea;
        }

        .case-item {
            background: white;
            padding: 0.75rem;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .case-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .case-info strong {
            color: #333;
            font-size: 0.9rem;
        }

        .case-similarity {
            background: #28a745;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
        }

        .case-item p {
            margin: 0;
            color: #666;
            font-size: 0.8rem;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 绑定演示事件
 */
function bindDemoEvents() {
    // 标签切换
    const demoTabs = document.querySelectorAll('.demo-tab');
    demoTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const demoType = this.getAttribute('data-demo');
            switchDemo(demoType);
            
            demoTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // 录音按钮
    const recordBtn = document.getElementById('recordBtn');
    if (recordBtn) {
        recordBtn.addEventListener('click', toggleRecording);
    }
    
    // 分析按钮
    const analyzeBtn = document.getElementById('analyzeBtn');
    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', performAnalysis);
    }
    
    // 分派按钮
    const dispatchBtn = document.getElementById('dispatchBtn');
    if (dispatchBtn) {
        dispatchBtn.addEventListener('click', performDispatch);
    }
    
    // 智能坐席辅助功能已集成到界面中，无需额外事件绑定
}

/**
 * 切换演示
 */
function switchDemo(demoType) {
    const demoPanels = document.querySelectorAll('.demo-panel');
    demoPanels.forEach(panel => panel.classList.remove('active'));
    
    const targetPanel = document.getElementById(`${demoType}-demo`);
    if (targetPanel) {
        targetPanel.classList.add('active');
    }
}

/**
 * 切换录音状态
 */
function toggleRecording() {
    const recordBtn = document.getElementById('recordBtn');
    const voiceWaves = document.querySelector('.voice-waves');
    const transcriptionResult = document.getElementById('transcriptionResult');
    
    if (recordBtn.classList.contains('recording')) {
        // 停止录音
        recordBtn.classList.remove('recording');
        recordBtn.innerHTML = '<i class="fas fa-microphone"></i> 点击开始录音';
        voiceWaves.style.display = 'none';
        
        // 模拟转录结果
        setTimeout(() => {
            transcriptionResult.innerHTML = `
                <div class="transcription-text">
                    <h4>语音转文字结果：</h4>
                    <p>"您好，我要投诉小区旁边的工地半夜施工，噪音太大了，影响我们休息。地址是朝阳区建国路88号小区。"</p>
                </div>
                <div class="extracted-info">
                    <h4>提取的关键信息：</h4>
                    <div class="info-tags">
                        <span class="tag">投诉类型：噪音扰民</span>
                        <span class="tag">地址：朝阳区建国路88号小区</span>
                        <span class="tag">时间：夜间</span>
                        <span class="tag">来源：工地施工</span>
                    </div>
                </div>
            `;
        }, 1000);
    } else {
        // 开始录音
        recordBtn.classList.add('recording');
        recordBtn.innerHTML = '<i class="fas fa-stop"></i> 点击停止录音';
        voiceWaves.style.display = 'flex';
        transcriptionResult.innerHTML = '<p class="placeholder">正在录音中，请说话...</p>';
    }
}

/**
 * 执行分析
 */
function performAnalysis() {
    const analysisInput = document.getElementById('analysisInput');
    const analysisResult = document.getElementById('analysisResult');
    const inputText = analysisInput.value.trim();
    
    if (!inputText) {
        alert('请输入要分析的内容');
        return;
    }
    
    analysisResult.innerHTML = '<p class="placeholder">正在分析中...</p>';
    
    setTimeout(() => {
        analysisResult.innerHTML = `
            <div class="analysis-output">
                <h4>智能分析结果：</h4>
                <div class="analysis-item">
                    <strong>情感倾向：</strong>
                    <span class="sentiment negative">负面 (85%)</span>
                </div>
                <div class="analysis-item">
                    <strong>紧急程度：</strong>
                    <span class="urgency high">高 (8/10)</span>
                </div>
                <div class="analysis-item">
                    <strong>问题分类：</strong>
                    <span class="category">环境噪音 > 施工扰民</span>
                </div>
                <div class="analysis-item">
                    <strong>建议处理部门：</strong>
                    <span class="department">区城管局、街道办事处</span>
                </div>
                <div class="analysis-item">
                    <strong>预计处理时长：</strong>
                    <span class="duration">24小时内</span>
                </div>
            </div>
        `;
        
        addAnalysisStyles();
    }, 2000);
}

/**
 * 添加分析样式
 */
function addAnalysisStyles() {
    if (document.querySelector('#analysis-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'analysis-styles';
    style.textContent = `
        .analysis-output {
            line-height: 1.6;
        }
        
        .analysis-item {
            margin: 1rem 0;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .sentiment.negative {
            color: #dc3545;
            font-weight: bold;
        }
        
        .urgency.high {
            color: #fd7e14;
            font-weight: bold;
        }
        
        .category {
            color: #667eea;
            font-weight: bold;
        }
        
        .department {
            color: #28a745;
            font-weight: bold;
        }
        
        .duration {
            color: #6f42c1;
            font-weight: bold;
        }
        
        .info-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .tag {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 执行分派
 */
function performDispatch() {
    const dispatchResult = document.getElementById('dispatchResult');
    
    dispatchResult.innerHTML = '<p class="placeholder">AI正在分析最佳分派方案...</p>';
    
    setTimeout(() => {
        dispatchResult.innerHTML = `
            <div class="dispatch-output">
                <h4>AI智能分派结果：</h4>
                <div class="dispatch-plan">
                    <div class="main-handler">
                        <h5><i class="fas fa-star"></i> 主办方</h5>
                        <div class="handler-card primary">
                            <strong>朝阳区建国街道办事处</strong>
                            <p>负责总协调和现场管理</p>
                            <span class="reason">原因：属地管理责任</span>
                        </div>
                    </div>
                    <div class="co-handlers">
                        <h5><i class="fas fa-handshake"></i> 协办方</h5>
                        <div class="handler-card">
                            <strong>朝阳区住建局</strong>
                            <p>负责施工许可检查和违规处理</p>
                            <span class="reason">原因：施工监管职责</span>
                        </div>
                        <div class="handler-card">
                            <strong>朝阳区生态环境局</strong>
                            <p>负责噪音检测和环保执法</p>
                            <span class="reason">原因：环境噪音监管</span>
                        </div>
                    </div>
                    <div class="dispatch-timeline">
                        <h5><i class="fas fa-clock"></i> 处理时限</h5>
                        <p>要求在 <strong>24小时内</strong> 完成初步处理并反馈</p>
                    </div>
                </div>
            </div>
        `;
        
        addDispatchStyles();
    }, 3000);
}

/**
 * 添加分派样式
 */
function addDispatchStyles() {
    if (document.querySelector('#dispatch-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'dispatch-styles';
    style.textContent = `
        .dispatch-plan {
            margin-top: 1rem;
        }
        
        .dispatch-plan h5 {
            color: #333;
            margin: 1.5rem 0 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .handler-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .handler-card.primary {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fffbf0 0%, #ffffff 100%);
        }
        
        .handler-card strong {
            color: #333;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .handler-card p {
            color: #555;
            margin: 0.5rem 0;
        }
        
        .reason {
            color: #667eea;
            font-size: 0.85rem;
            font-style: italic;
        }
        
        .dispatch-timeline {
            background: #e8f4fd;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }
        
        .dispatch-timeline p {
            margin: 0;
            color: #555;
        }
    `;
    document.head.appendChild(style);
}

// 聊天功能已移除，智能坐席辅助采用实时推送模式

/**
 * 初始化效果展示动画
 */
function initBenefitsAnimation() {
    const benefitItems = document.querySelectorAll('.benefit-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                    animateBenefitIcon(entry.target);
                }, index * 200);
            }
        });
    }, {
        threshold: 0.3
    });
    
    benefitItems.forEach(item => {
        observer.observe(item);
    });
}

/**
 * 动画化效果图标
 */
function animateBenefitIcon(benefitItem) {
    const icon = benefitItem.querySelector('.benefit-icon');
    
    if (!icon) return;
    
    icon.style.animation = 'bounceIn 0.8s ease-out';
    
    benefitItem.addEventListener('mouseenter', function() {
        icon.style.animation = 'pulse 1s ease-in-out infinite';
    });
    
    benefitItem.addEventListener('mouseleave', function() {
        icon.style.animation = '';
    });
}

/**
 * 初始化交互功能
 */
function initInteractiveFeatures() {
    // 添加功能卡片点击效果
    const featureItems = document.querySelectorAll('.feature-item');
    
    featureItems.forEach(item => {
        item.addEventListener('click', function() {
            showFeatureDetails(this);
        });
        
        // 添加点击提示
        item.style.cursor = 'pointer';
        item.title = '点击查看详细信息';
    });
}

/**
 * 显示功能详情
 */
function showFeatureDetails(featureItem) {
    const featureName = featureItem.querySelector('h5').textContent;
    const featureDesc = featureItem.querySelector('p').textContent;
    
    // 创建详情弹窗
    const modal = document.createElement('div');
    modal.className = 'feature-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${featureName}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <p>${featureDesc}</p>
                <div class="feature-benefits">
                    <h4>主要优势：</h4>
                    <ul>
                        ${generateFeatureBenefits(featureName)}
                    </ul>
                </div>
                <div class="implementation-tips">
                    <h4>实施建议：</h4>
                    <p>${generateImplementationTips(featureName)}</p>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 绑定关闭事件
    const closeBtn = modal.querySelector('.close-btn');
    closeBtn.addEventListener('click', () => modal.remove());
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });
    
    addFeatureModalStyles();
}

/**
 * 生成功能优势
 */
function generateFeatureBenefits(featureName) {
    const benefits = {
        '智能语音实时转写与要素提取': [
            '大幅提升录入效率，减少人工输入时间',
            '自动提取关键信息，避免遗漏重要细节',
            '支持方言识别，提升服务覆盖面',
            '实时处理，无需等待转写完成'
        ],
        '智能知识库与辅助应答': [
            '快速检索海量政策信息',
            '提供标准化答复，确保信息准确性',
            '减少培训成本，新员工快速上手',
            '持续学习更新，保持知识库时效性'
        ],
        '工单智能解读与任务建议': [
            '自动生成处理摘要，节省阅读时间',
            '基于历史案例提供最佳实践建议',
            '智能推荐处理步骤，提升处理效率',
            '减少处理错误，提高工单质量'
        ]
    };
    
    const defaultBenefits = [
        '提升工作效率',
        '减少人工错误',
        '优化用户体验',
        '降低运营成本'
    ];
    
    const featureBenefits = benefits[featureName] || defaultBenefits;
    return featureBenefits.map(benefit => `<li>${benefit}</li>`).join('');
}

/**
 * 生成实施建议
 */
function generateImplementationTips(featureName) {
    const tips = {
        '智能语音实时转写与要素提取': '建议分阶段实施，先在试点部门测试语音识别准确率，根据实际效果调整模型参数，确保在正式环境中达到预期效果。',
        '智能知识库与辅助应答': '需要建立完善的知识库管理机制，定期更新政策信息，建立反馈机制收集使用效果，持续优化知识库内容和检索算法。',
        '工单智能解读与任务建议': '建议结合历史工单数据训练模型，建立标准化的处理流程模板，定期评估建议的准确性和实用性，不断优化算法模型。'
    };
    
    return tips[featureName] || '建议结合实际业务需求，制定详细的实施计划，分步骤推进功能上线，并建立完善的效果评估机制。';
}

/**
 * 添加功能模态框样式
 */
function addFeatureModalStyles() {
    if (document.querySelector('#feature-modal-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'feature-modal-styles';
    style.textContent = `
        .feature-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }
        
        .feature-modal .modal-content {
            background: white;
            border-radius: 15px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideInUp 0.3s ease;
        }
        
        .feature-modal .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .feature-modal .modal-body {
            padding: 1.5rem;
        }
        
        .feature-modal .modal-body h4 {
            margin: 1.5rem 0 1rem;
            color: #333;
        }
        
        .feature-modal .modal-body ul {
            color: #555;
            line-height: 1.6;
        }
        
        .feature-modal .modal-body li {
            margin-bottom: 0.5rem;
        }
        
        .implementation-tips {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
    `;
    document.head.appendChild(style);
}
