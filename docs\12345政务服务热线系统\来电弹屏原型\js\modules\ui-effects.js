/**
 * UI效果管理模块
 * 负责管理各种动画效果和交互效果
 */

class UIEffectsManager {
    constructor() {
        // 效果配置
        this.animationConfig = {
            duration: {
                fast: 300,
                normal: 500,
                slow: 1000
            },
            easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
        };
    }

    /**
     * 初始化UI效果
     */
    init() {
        this.addInteractiveEffects();
    }

    /**
     * 添加交互效果
     */
    addInteractiveEffects() {
        // 为卡片添加悬停效果
        document.querySelectorAll('.ticket-card, .kb-item, .script-item').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });

        // 为按钮添加点击波纹效果
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.createRippleEffect(e, btn);
            });
        });

        // 为输入框添加焦点效果
        document.querySelectorAll('input, textarea, select').forEach(input => {
            input.addEventListener('focus', () => {
                if (input.parentElement) {
                    input.parentElement.style.transform = 'scale(1.02)';
                }
            });
            
            input.addEventListener('blur', () => {
                if (input.parentElement) {
                    input.parentElement.style.transform = 'scale(1)';
                }
            });
        });
    }

    /**
     * 创建波纹效果
     * @param {Event} e - 点击事件
     * @param {Element} button - 按钮元素
     */
    createRippleEffect(e, button) {
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * 添加页面加载动画
     */
    addLoadingAnimations() {
        // 为所有面板添加进入动画
        const panels = document.querySelectorAll('.left-panel, .center-panel, .right-panel');
        panels.forEach((panel, index) => {
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(30px)';
            panel.style.transition = `all 0.6s ${this.animationConfig.easing}`;
            
            setTimeout(() => {
                panel.style.opacity = '1';
                panel.style.transform = 'translateY(0)';
            }, 200 + index * 150);
        });
    }

    /**
     * 添加打字机效果
     * @param {Element} element - 目标元素
     * @param {string} text - 要显示的文本
     * @param {number} speed - 打字速度
     */
    typeWriter(element, text, speed = 50) {
        if (!element) return;
        
        element.textContent = '';
        let i = 0;
        const timer = setInterval(() => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    /**
     * 添加成功动画效果
     * @param {Element} element - 目标元素
     */
    addSuccessAnimation(element) {
        if (!element) return;
        
        element.classList.add('success-animation');
        setTimeout(() => {
            element.classList.remove('success-animation');
        }, 600);
    }

    /**
     * 添加紧急闪烁效果
     * @param {Element} element - 目标元素
     */
    addUrgentBlink(element) {
        if (!element) return;
        
        element.classList.add('urgent-blink');
    }

    /**
     * 移除紧急闪烁效果
     * @param {Element} element - 目标元素
     */
    removeUrgentBlink(element) {
        if (!element) return;
        
        element.classList.remove('urgent-blink');
    }

    /**
     * 淡入动画
     * @param {Element} element - 目标元素
     * @param {number} duration - 动画持续时间
     */
    fadeIn(element, duration = this.animationConfig.duration.normal) {
        if (!element) return;
        
        element.style.opacity = '0';
        element.style.transition = `opacity ${duration}ms ease`;
        
        setTimeout(() => {
            element.style.opacity = '1';
        }, 10);
    }

    /**
     * 淡出动画
     * @param {Element} element - 目标元素
     * @param {number} duration - 动画持续时间
     */
    fadeOut(element, duration = this.animationConfig.duration.normal) {
        if (!element) return;
        
        element.style.transition = `opacity ${duration}ms ease`;
        element.style.opacity = '0';
        
        setTimeout(() => {
            element.style.display = 'none';
        }, duration);
    }

    /**
     * 滑入动画
     * @param {Element} element - 目标元素
     * @param {string} direction - 滑入方向 (left, right, up, down)
     * @param {number} duration - 动画持续时间
     */
    slideIn(element, direction = 'up', duration = this.animationConfig.duration.normal) {
        if (!element) return;
        
        const transforms = {
            up: 'translateY(30px)',
            down: 'translateY(-30px)',
            left: 'translateX(30px)',
            right: 'translateX(-30px)'
        };
        
        element.style.opacity = '0';
        element.style.transform = transforms[direction] || transforms.up;
        element.style.transition = `all ${duration}ms ${this.animationConfig.easing}`;
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translate(0, 0)';
        }, 10);
    }

    /**
     * 滑出动画
     * @param {Element} element - 目标元素
     * @param {string} direction - 滑出方向 (left, right, up, down)
     * @param {number} duration - 动画持续时间
     */
    slideOut(element, direction = 'up', duration = this.animationConfig.duration.normal) {
        if (!element) return;
        
        const transforms = {
            up: 'translateY(-30px)',
            down: 'translateY(30px)',
            left: 'translateX(-30px)',
            right: 'translateX(30px)'
        };
        
        element.style.transition = `all ${duration}ms ${this.animationConfig.easing}`;
        element.style.opacity = '0';
        element.style.transform = transforms[direction] || transforms.up;
        
        setTimeout(() => {
            element.style.display = 'none';
        }, duration);
    }

    /**
     * 缩放动画
     * @param {Element} element - 目标元素
     * @param {number} scale - 缩放比例
     * @param {number} duration - 动画持续时间
     */
    scale(element, scale = 1.05, duration = this.animationConfig.duration.fast) {
        if (!element) return;
        
        element.style.transition = `transform ${duration}ms ${this.animationConfig.easing}`;
        element.style.transform = `scale(${scale})`;
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, duration);
    }

    /**
     * 脉冲动画
     * @param {Element} element - 目标元素
     * @param {number} duration - 动画持续时间
     */
    pulse(element, duration = 1000) {
        if (!element) return;
        
        element.style.animation = `pulse ${duration}ms ease-in-out`;
        
        setTimeout(() => {
            element.style.animation = '';
        }, duration);
    }

    /**
     * 摇摆动画
     * @param {Element} element - 目标元素
     * @param {number} duration - 动画持续时间
     */
    shake(element, duration = 500) {
        if (!element) return;
        
        element.style.animation = `shake ${duration}ms ease-in-out`;
        
        setTimeout(() => {
            element.style.animation = '';
        }, duration);
    }

    /**
     * 弹跳动画
     * @param {Element} element - 目标元素
     * @param {number} duration - 动画持续时间
     */
    bounce(element, duration = 600) {
        if (!element) return;
        
        element.style.animation = `bounceIn ${duration}ms ease-out`;
        
        setTimeout(() => {
            element.style.animation = '';
        }, duration);
    }

    /**
     * 高亮效果
     * @param {Element} element - 目标元素
     * @param {string} color - 高亮颜色
     * @param {number} duration - 持续时间
     */
    highlight(element, color = '#fff3cd', duration = 1000) {
        if (!element) return;
        
        const originalBackground = element.style.background;
        element.style.transition = `background ${duration}ms ease`;
        element.style.background = color;
        
        setTimeout(() => {
            element.style.background = originalBackground;
        }, duration);
    }

    /**
     * 添加加载状态
     * @param {Element} element - 目标元素
     */
    addLoadingState(element) {
        if (!element) return;
        
        element.classList.add('loading-shimmer');
        element.style.pointerEvents = 'none';
    }

    /**
     * 移除加载状态
     * @param {Element} element - 目标元素
     */
    removeLoadingState(element) {
        if (!element) return;
        
        element.classList.remove('loading-shimmer');
        element.style.pointerEvents = '';
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIEffectsManager;
}
