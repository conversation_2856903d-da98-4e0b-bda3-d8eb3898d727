
---

### **“系统日志”页面内容详解**

#### **一、 页面核心目标**

1.  **安全审计 (Security Audit)**: 追踪谁在什么时间、从哪个IP地址登录了系统，以及谁对权限、角色等敏感信息进行了修改。
2.  **变更追溯 (Change Tracking)**: 记录所有重要的配置变更历史，如谁修改了自动化规则、SLA策略等，便于在出现问题时追溯源头。
3.  **问题排查 (Troubleshooting)**: 在系统出现异常时，通过日志分析，找到可能导致问题的操作记录。
4.  **合规性要求 (Compliance)**: 满足某些行业（如金融、医疗）对操作日志必须完整记录和保存的合规性要求。

---

### **二、 页面内容与布局**

页面设计以**强大的筛选和清晰的列表展示**为核心，因为日志数据量通常非常巨大。

#### **1. 高级筛选与搜索区 (Advanced Filters & Search)**

这是快速从海量日志中定位信息的关键。

*   **时间范围**: **[最重要的筛选器]** 必须提供精确到时分秒的日期时间范围选择器。
*   **操作用户 (Actor)**: 一个用户搜索框，可以筛选特定员工的所有操作日志。
*   **IP地址 (IP Address)**: 筛选来自特定IP地址的操作。
*   **事件类型/模块 (Event Type / Module)**: **[核心筛选]** 一个树形多选框或分组下拉菜单，用于筛选特定类型的日志。例如：
    *   **登录与会话 (Authentication)**:
        *   `登录成功`
        *   `登录失败` (可以进一步看失败原因，如密码错误、账号锁定)
        *   `登出`
        *   `密码修改`
    *   **用户与权限 (User & Permission)**:
        *   `创建用户`
        *   `修改用户角色`
        *   `停用/启用用户`
        *   `修改角色权限`
    *   **配置变更 (Configuration Changes)**:
        *   `工单模板已修改`
        *   `自动化规则已启用/停用`
        *   `SLA策略已创建`
    *   **数据操作 (Data Operations - 高敏感)**:
        *   `工单已删除` (记录被删除的工单ID)
        *   `数据已导出`
*   **关键词搜索**: 在日志的详细描述中搜索关键词。

#### **2. 日志列表区 (Log List)**

以信息密度极高的表格（Data Grid）形式展示筛选出的日志记录，按时间倒序排列。

*   **列表表头 (Table Header) - [为审计和排查而优化]**:
    *   `**时间戳 (Timestamp)**`: **[核心字段]** 精确到毫秒的操作发生时间。
    *   `**操作用户 (Actor)**`: **[核心字段]** 执行操作的用户名或系统（如果是系统自动执行）。
    *   `**IP地址 (IP Address)**`: **[核心字段]** 操作者当时使用的IP地址。
    *   `**事件类型 (Event Type)**`: **[核心字段]** 清晰地标明操作的类型，如“登录失败”、“修改SLA策略”。
    *   `**事件描述 (Description)**`: **[核心字段]** 用一句自然语言描述具体发生了什么，这是最关键的可读信息。例如：“用户‘张三’修改了SLA策略‘VIP客户服务标准’”。
    *   `操作 (Action)`: 查看更详细的日志信息。

*   **列表行交互**:
    *   点击行首的“+”或箭头，可以**展开行**，在不跳转页面的情况下，看到更详细的日志负载(Payload)。

#### **3. 日志详情区 (Log Details View)**

当点击“查看详情”或展开行时，会展示该条日志的全部信息。

*   **基础信息**: 重复列表中的核心信息（时间、用户、IP、事件类型、描述）。
*   **变更前数据 (Before Change)**:
    *   **[变更追溯的关键]** 对于所有“修改”类型的操作，这里会以`JSON`或键值对的形式，清晰地展示被修改对象在**修改前**的完整数据。
*   **变更后数据 (After Change)**:
    *   以`JSON`或键值对的形式，清晰地展示被修改对象在**修改后**的完整数据。
    *   系统会自动**高亮显示**发生变化的字段，让管理员一眼就能看出具体修改了什么。
*   **附加信息 (Metadata)**:
    *   `User-Agent`: 操作者使用的浏览器和操作系统信息。
    *   `请求ID (Request ID)`: 用于与后端服务器日志进行关联排查。

#### **4. 页面顶部操作区**

*   **`[ 导出日志 ]`**:
    *   提供将当前筛选结果导出为CSV或JSON文件的功能，用于离线审计或长期归档。
    *   出于安全考虑，导出功能需要严格的权限控制。
*   **`[ 日志归档/轮转策略 ]`**:
    *   一个链接，跳转到另一个页面，用于配置日志的保存时长（如在线保存90天，之后自动归档到冷存储）和存储空间管理。

---