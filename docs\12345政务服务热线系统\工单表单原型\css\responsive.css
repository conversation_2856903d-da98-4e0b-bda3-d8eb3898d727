/**
 * 响应式样式
 * 包含不同屏幕尺寸下的适配样式
 */

/* 断点定义 */
/* xs: 0-575px (手机) */
/* sm: 576-767px (大手机) */
/* md: 768-991px (平板) */
/* lg: 992-1199px (小桌面) */
/* xl: 1200px+ (大桌面) */

/* 大桌面 (1200px+) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
        padding: var(--spacing-xl);
    }
    
    .form-row {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
    
    .form-row.three-columns {
        grid-template-columns: 1fr 1fr 1fr;
    }
    
    .form-row.four-columns {
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }
}

/* 小桌面 (992-1199px) */
@media (max-width: 1199px) {
    .container {
        max-width: 1000px;
    }
    
    .form-row {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .form-row.three-columns {
        grid-template-columns: 1fr 1fr;
    }
    
    .form-row.four-columns {
        grid-template-columns: 1fr 1fr;
    }
}

/* 平板 (768-991px) */
@media (max-width: 991px) {
    .container {
        padding: var(--spacing-md);
    }
    
    .form-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .form-header h1 {
        font-size: var(--font-size-xl);
    }
    
    .form-section {
        padding: var(--spacing-lg);
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .form-row.two-columns,
    .form-row.three-columns,
    .form-row.four-columns {
        grid-template-columns: 1fr;
    }
    
    .radio-button-group {
        flex-direction: column;
    }
    
    .radio-button {
        justify-content: flex-start;
    }
    
    .checkbox-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
    
    .modal-content {
        width: 95vw;
        margin: var(--spacing-md);
    }
    
    .attachment-list {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .tag-cloud {
        min-height: 80px;
    }
    
    .address-input-container {
        flex-direction: column;
    }
    
    .tag-input-container {
        flex-direction: column;
    }
}

/* 大手机 (576-767px) */
@media (max-width: 767px) {
    .container {
        padding: var(--spacing-sm);
    }
    
    .form-header {
        padding: var(--spacing-md) 0;
        margin-bottom: var(--spacing-lg);
    }
    
    .form-header h1 {
        font-size: var(--font-size-lg);
    }
    
    .form-status {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .form-section {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .section-title {
        font-size: var(--font-size-md);
    }
    
    .subsection-title {
        font-size: var(--font-size-sm);
    }
    
    .form-group {
        margin-bottom: var(--spacing-sm);
    }
    
    .form-group-section {
        margin-bottom: var(--spacing-lg);
    }
    
    .radio-button-group {
        gap: var(--spacing-xs);
    }
    
    .radio-button {
        padding: var(--spacing-xs) var(--spacing-sm);
        min-height: 36px;
    }
    
    .checkbox-group {
        gap: var(--spacing-xs);
    }
    
    .btn {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .btn-small {
        min-height: 36px;
    }
    
    .form-input,
    .form-select {
        height: 44px;
        font-size: var(--font-size-md);
    }
    
    .form-textarea {
        font-size: var(--font-size-md);
    }
    
    .attachment-list {
        grid-template-columns: 1fr;
    }
    
    .attachment-card {
        padding: var(--spacing-sm);
    }
    
    .attachment-preview {
        height: 100px;
    }
    
    .upload-area {
        padding: var(--spacing-lg);
    }
    
    .upload-icon {
        font-size: 36px;
    }
    
    .modal-content {
        width: 100vw;
        height: 100vh;
        max-width: none;
        max-height: none;
        margin: 0;
        border-radius: 0;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }
    
    .preview-field {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .preview-label {
        min-width: auto;
        margin-right: 0;
        font-size: var(--font-size-xs);
    }
    
    .steps {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .step {
        flex-direction: row;
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }
    
    .step:not(:last-child)::after {
        display: none;
    }
    
    .breadcrumb {
        flex-wrap: wrap;
    }
}

/* 小手机 (0-575px) */
@media (max-width: 575px) {
    html {
        font-size: 16px; /* 提高基础字体大小以改善可读性 */
    }
    
    .container {
        padding: var(--spacing-xs);
    }
    
    .form-header {
        padding: var(--spacing-sm) 0;
        margin-bottom: var(--spacing-md);
    }
    
    .form-section {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .section-title {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .subsection-title {
        font-size: var(--font-size-xs);
        margin-bottom: var(--spacing-xs);
    }
    
    .form-group {
        margin-bottom: var(--spacing-xs);
    }
    
    .form-group-section {
        margin-bottom: var(--spacing-md);
    }
    
    .form-label {
        font-size: var(--font-size-xs);
    }
    
    .radio-button {
        padding: var(--spacing-xs);
        min-height: 40px;
        font-size: var(--font-size-xs);
    }
    
    .radio-text {
        font-size: var(--font-size-xs);
    }
    
    .checkbox-label {
        padding: var(--spacing-xs);
    }
    
    .checkbox-text {
        font-size: var(--font-size-xs);
    }
    
    .btn {
        min-height: 48px;
        font-size: var(--font-size-sm);
    }
    
    .form-input,
    .form-select {
        height: 48px;
        padding: var(--spacing-sm);
    }
    
    .form-textarea {
        padding: var(--spacing-sm);
    }
    
    .tag-cloud {
        padding: var(--spacing-xs);
        min-height: 60px;
    }
    
    .tag-item {
        font-size: 10px;
        padding: 2px var(--spacing-xs);
    }
    
    .custom-tag {
        font-size: 10px;
        padding: 2px var(--spacing-xs);
    }
    
    .upload-area {
        padding: var(--spacing-md);
    }
    
    .upload-text p {
        font-size: var(--font-size-sm);
    }
    
    .upload-tips {
        font-size: 10px;
    }
    
    .attachment-preview {
        height: 80px;
    }
    
    .attachment-name {
        font-size: var(--font-size-xs);
    }
    
    .attachment-size,
    .attachment-description {
        font-size: 10px;
    }
    
    .form-actions {
        padding: var(--spacing-sm);
        position: static; /* 移除粘性定位 */
    }
    
    .action-buttons {
        gap: var(--spacing-xs);
    }
    
    .char-counter {
        font-size: 10px;
    }
    
    .form-help {
        font-size: 10px;
    }
    
    .form-error {
        font-size: 10px;
    }
    
    .status-indicator {
        font-size: 10px;
        padding: 2px var(--spacing-xs);
    }
    
    .auto-save-status {
        font-size: 10px;
    }
}

/* 横屏手机优化 */
@media (max-width: 767px) and (orientation: landscape) {
    .modal-content {
        width: 95vw;
        height: 95vh;
        border-radius: var(--border-radius-lg);
        margin: auto;
    }
    
    .form-actions {
        position: static;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .form-input,
    .form-select,
    .form-textarea,
    .btn {
        border-width: 0.5px;
    }
}

/* 打印样式 */
@media print {
    .container {
        max-width: none;
        padding: 0;
        background-color: white;
    }
    
    .form-header,
    .form-actions,
    .btn,
    .upload-area,
    .modal {
        display: none !important;
    }
    
    .form-section {
        border: none;
        box-shadow: none;
        padding: var(--spacing-md) 0;
        page-break-inside: avoid;
    }
    
    .section-title {
        border-bottom: 2px solid black;
        color: black;
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        border: 1px solid black;
        background-color: white;
        color: black;
    }
    
    .form-input[readonly] {
        background-color: #f5f5f5;
    }
    
    .radio-button:has(input:checked) {
        background-color: #e6f7ff;
        border-color: black;
    }
    
    .checkbox-label input[type="checkbox"]:checked {
        background-color: black;
    }
    
    .tag-item.selected {
        background-color: black;
        color: white;
    }
    
    .attachment-card {
        border: 1px solid black;
        page-break-inside: avoid;
    }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-color: #000000;
        --bg-white: #ffffff;
        --primary-color: #0000ff;
        --error-color: #ff0000;
        --success-color: #008000;
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        border-width: 2px;
    }
    
    .btn {
        border-width: 2px;
    }
    
    .radio-button,
    .checkbox-label {
        border-width: 2px;
    }
}

/* 移除暗色主题，保持明亮主题 */
