# 工单合并和工单拆分功能设计

## 1. 功能概述

### 1.1 工单合并
将多个相关的工单合并为一个主工单进行统一处理，避免重复处理和资源浪费。

### 1.2 工单拆分
将一个复杂的工单拆分为多个独立的子工单，分别派发给不同部门处理。

## 2. 权限控制

### 2.1 操作权限
- **工单合并**：仅限市级话务员操作
- **工单拆分**：仅限市级话务员操作
- **审核权限**：需要班长或主管审核确认

### 2.2 状态限制
- 只能对"待派单"、"已派单"状态的工单进行合并/拆分
- 已办结、已关闭的工单不允许合并/拆分操作

## 3. 工单合并功能

### 3.1 使用场景

#### 3.1.1 重复投诉场景
- **场景描述**：同一市民多次投诉同一问题
- **示例**：张三连续3天投诉小区噪音问题，产生3个工单
- **合并理由**：避免重复处理，提高处理效率

#### 3.1.2 相同问题多人投诉
- **场景描述**：多个市民投诉同一问题
- **示例**：某路段路灯损坏，10个市民分别投诉
- **合并理由**：统一处理，避免资源浪费

#### 3.1.3 关联问题投诉
- **场景描述**：不同投诉实际指向同一根本问题
- **示例**：小区停水投诉和水压不足投诉实际是同一供水问题
- **合并理由**：从根本上解决问题

### 3.2 操作流程

#### 3.2.1 发起合并
1. **选择主工单**
   - 在工单列表中选择一个工单作为主工单
   - 点击"工单合并"按钮

2. **搜索待合并工单**
   - 系统弹出合并工单选择界面
   - 支持多种搜索条件：
     - 工单编号
     - 市民姓名/电话
     - 问题关键词
     - 涉及地点
     - 时间范围

3. **智能推荐**
   - 系统自动推荐可能相关的工单
   - 推荐依据：
     - 相同市民信息
     - 相似问题描述
     - 相近地理位置
     - 相同问题分类

#### 3.2.2 合并确认
1. **工单对比**
   - 并排显示主工单和待合并工单信息
   - 高亮显示相同和差异部分
   - 提供详细对比报告

2. **合并规则设置**
   - 选择主工单信息（保留哪个工单的基本信息）
   - 设置合并后的工单标题
   - 合并诉求描述（自动整合或手动编辑）
   - 选择承办单位（如有冲突需重新指定）

3. **填写合并说明**
   - 必填项：合并原因
   - 可选项：特殊处理要求
   - 预估影响：合并后的处理时限调整

### 3.3 按钮点击后的反应

#### 3.3.1 点击"工单合并"按钮
```
1. 权限验证
   - 检查用户是否为市级话务员
   - 检查工单状态是否允许合并

2. 界面响应
   - 按钮变为加载状态，显示"正在加载..."
   - 弹出工单合并对话框
   - 对话框标题："工单合并 - 主工单：WD20241201000001"

3. 数据加载
   - 加载当前工单详细信息
   - 搜索可能相关的工单（智能推荐）
   - 显示搜索和筛选界面
```

#### 3.3.2 选择待合并工单后
```
1. 工单对比界面
   - 左侧：主工单信息
   - 右侧：待合并工单信息
   - 底部：合并配置选项

2. 验证检查
   - 检查工单是否可以合并
   - 显示潜在冲突和风险提示
   - 计算合并后的影响

3. 操作按钮
   - "确认合并"：执行合并操作
   - "取消"：关闭对话框
   - "重新选择"：返回工单选择界面
```

#### 3.3.3 确认合并后
```
1. 数据处理
   - 创建合并记录
   - 更新主工单信息
   - 将被合并工单状态改为"已合并"
   - 建立工单关联关系

2. 通知机制
   - 向相关承办单位发送通知
   - 向市民发送合并处理通知（如需要）
   - 记录操作日志

3. 界面更新
   - 显示合并成功提示
   - 刷新工单详情页面
   - 在工单历史中添加合并记录
```

### 3.4 合并后的工单特征

#### 3.4.1 工单信息整合
- **工单编号**：保持主工单编号不变
- **关联编号**：显示所有被合并的工单编号
- **诉求标题**：整合后的标题，体现合并性质
- **详细描述**：包含所有相关投诉的描述
- **市民信息**：主要联系人+其他相关市民列表

#### 3.4.2 处理流程调整
- **时限计算**：以最早的工单创建时间为准
- **紧急程度**：取最高紧急程度
- **承办单位**：可能需要重新指定或增加协办单位
- **回访要求**：需要对所有相关市民进行回访

## 4. 工单拆分功能

### 4.1 使用场景

#### 4.1.1 跨部门复杂问题
- **场景描述**：一个投诉涉及多个部门职责
- **示例**：小区环境问题涉及城管（垃圾清运）、住建（物业管理）、环保（噪音治理）
- **拆分理由**：不同问题需要不同部门专业处理

#### 4.1.2 多地点同类问题
- **场景描述**：投诉涉及多个不同地点的相同问题
- **示例**：市民投诉多个路段的路灯损坏问题
- **拆分理由**：不同地点由不同辖区部门负责

#### 4.1.3 阶段性处理问题
- **场景描述**：问题需要分阶段、分步骤处理
- **示例**：违建拆除需要先调查取证、再下达整改通知、最后执行拆除
- **拆分理由**：不同阶段有不同的处理要求和时限

### 4.2 操作流程

#### 4.2.1 发起拆分
1. **拆分分析**
   - 分析工单内容，识别可拆分的问题点
   - 系统智能建议拆分方案
   - 话务员确认拆分必要性

2. **拆分方案设计**
   - 确定拆分数量（2-5个子工单）
   - 为每个子工单分配问题内容
   - 指定对应的承办单位
   - 设置处理优先级和时限

#### 4.2.2 子工单创建
1. **信息分配**
   - 基础信息：继承原工单的市民信息
   - 问题描述：根据拆分方案分配具体问题
   - 附件资料：相关附件分配给对应子工单
   - 地点信息：精确到具体涉及地点

2. **关联关系建立**
   - 设置父子工单关系
   - 建立子工单间的协调关系
   - 指定主责子工单（如需要）

### 4.3 按钮点击后的反应

#### 4.3.1 点击"工单拆分"按钮
```
1. 权限和状态验证
   - 验证用户权限
   - 检查工单是否可拆分
   - 分析工单复杂度

2. 智能分析
   - AI分析工单内容
   - 识别可拆分的问题点
   - 生成拆分建议方案

3. 界面展示
   - 弹出工单拆分向导
   - 显示原工单内容分析
   - 展示建议的拆分方案
```

#### 4.3.2 配置拆分方案
```
1. 拆分配置界面
   - 左侧：原工单内容
   - 右侧：拆分方案配置
   - 底部：子工单预览

2. 交互操作
   - 拖拽分配问题内容
   - 选择承办单位
   - 设置处理时限
   - 配置协调关系

3. 验证检查
   - 检查拆分方案完整性
   - 验证承办单位可用性
   - 计算处理时限合理性
```

#### 4.3.3 确认拆分后
```
1. 数据创建
   - 创建多个子工单
   - 设置父子关联关系
   - 更新原工单状态为"已拆分"

2. 派单处理
   - 向各承办单位派发子工单
   - 发送拆分处理通知
   - 建立协调沟通机制

3. 界面更新
   - 显示拆分成功信息
   - 展示子工单列表
   - 更新工单状态和历史
```

### 4.4 拆分后的管理

#### 4.4.1 协调机制
- **主责单位**：指定一个主要负责协调的单位
- **进度同步**：定期同步各子工单处理进度
- **统一回访**：由主责单位统一进行市民回访

#### 4.4.2 办结条件
- **全部办结**：所有子工单都办结后，父工单才能办结
- **关键办结**：关键子工单办结后，可申请提前办结
- **部分办结**：允许部分子工单先行办结