
---

### **“自定义报表”页面内容详解**

#### **一、 页面核心目标**

1.  **无限的灵活性**: 允许用户自由组合系统中的任何数据维度和指标。
2.  **丰富的可视化**: 提供多种图表类型，让用户选择最合适的方式来呈现数据。
3.  **易用的构建体验**: 即使非技术人员也能通过拖拽、点选等方式，快速构建出复杂的报表。
4.  **保存与分享**: 允许用户将精心制作的报表保存下来，并分享给团队其他成员。

---

### **二、 页面内容与布局**

这个页面的设计通常分为两个主要模式/视图：
1.  **报表管理列表视图 (Report List View)**: 这是入口页面，用于管理所有已创建的自定义报表。
2.  **报表构建器视图 (Report Builder View)**: 这是核心功能区，用于创建或编辑一个具体的报表。

#### **模式一：报表管理列表视图**

*   **页面顶部**: 一个醒目的 `[ + 新建自定义报表 ]` 按钮。
*   **搜索框**: 按报表名称搜索。
*   **报表列表**: 以表格形式展示所有已保存的自定义报表。
    *   **表头**: `报表名称`, `创建人`, `创建时间`, `最后修改时间`, `描述`, `操作`。
    *   **行末操作**: `[ 查看 ]`, `[ 编辑 ]` (仅限创建人或有权限者), `[ 复制 ]`, `[ 分享 ]`, `[ 删除 ]`。

---

#### **模式二：报表构建器视图**

点击“新建”或“编辑”后，会进入这个功能强大的构建界面。界面通常分为左右两栏或上中下三部分。

**1. 数据源与字段选择区 (Data Source & Field Selector) - [通常在左侧栏]**

*   **数据模型选择**: 首先，可能需要选择一个主数据模型，如“工单数据”、“SLA数据”、“评价数据”。
*   **字段列表**: 一个可搜索、可折叠的树形列表，展示了所选数据模型下的**所有可用字段**。这些字段被清晰地分为两类：
    *   **维度 (Dimensions)**: 用于**分组和分类**的字段，通常是文本或日期类型。例如：`处理人`、`工单类型`、`创建日期(按月)`、`客户城市`。
    *   **指标 (Metrics)**: 用于**计算和衡量**的字段，通常是数字类型。例如：`工单数量 (计数)`、`平均解决时长 (平均值)`、`工单积分 (求和)`。

**2. 报表配置与构建区 (Report Configuration & Canvas) - [通常在中间主区域]**

这是用户进行拖拽和配置的核心区域。

*   **图表类型选择器**: 一排图标，让用户选择最终报表的呈现形式，如：
    *   `表格 (Table)`
    *   `指标卡 (KPI Card)`
    *   `折线图 (Line Chart)`
    *   `柱状图 (Bar Chart)`
    *   `饼图 (Pie Chart)`
    *   `面积图 (Area Chart)`
    *   `散点图 (Scatter Plot)`

*   **配置“货架” (Configuration Shelves)**:
    *   这是最核心的交互区域。根据所选的图表类型，会出现不同的“货架”（配置槽）。用户需要从左侧的字段列表中，**将“维度”和“指标”字段拖拽到对应的货架上**。
    *   **对于表格**:
        *   `行 (Rows)` 货架: 拖入维度字段，如`处理人`。
        *   `列 (Columns)` 货架: 拖入维度字段，如`工单类型`。
        *   `值 (Values)` 货架: 拖入指标字段，如`工单数量 (计数)`。
    *   **对于折线图**:
        *   `X轴 (X-Axis)` 货架: 拖入时间维度，如`创建日期(按月)`。
        *   `Y轴 (Y-Axis)` 货架: 拖入指标，如`平均解决时长`。
        *   `分解/图例 (Breakdown/Legend)` 货架: 拖入一个维度，如`工单类型`，即可在同一张图上画出多条不同类型的折线。

*   **筛选器配置**:
    *   一个专门的区域，让用户为这个**特定的报表**添加筛选条件（与“工单明细报表”的条件构建器类似）。这些筛选器将成为最终查看报表时可用的交互选项。

**3. 实时预览区 (Live Preview)**

*   **即时反馈**: 每当用户在配置区完成一次拖拽或修改配置，这个区域会**立即（或点击“刷新”后）**根据当前的配置，实时渲染出报表的预览效果。
*   **价值**: 这种所见即所得的体验，让用户可以不断尝试和调整，直到构建出满意的报表为止。

**4. 报表操作区 (Report Actions) - [通常在页面顶部]**

*   **报表名称输入框**: 为正在创建的报表命名。
*   **`[ 保 存 ]`**: 保存当前的报表配置。
*   **`[ 另存为 ]`**: 将当前报表保存为一个新的副本。
*   **`[ 导出 ]`**: 将预览的图表或数据导出为图片、PDF或CSV。

---