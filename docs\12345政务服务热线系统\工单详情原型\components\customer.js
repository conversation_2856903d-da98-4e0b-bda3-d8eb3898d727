/**
 * 12345政务服务热线系统 - 客户信息组件
 * @description 客户信息展示和管理组件
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 客户信息组件
 */
const CustomerComponent = {
    /**
     * 客户信息容器
     */
    container: null,
    
    /**
     * 客户数据
     */
    customerData: null,
    
    /**
     * 初始化客户信息组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'customerInfo') {
        console.log('初始化客户信息组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到客户信息容器元素:', containerId);
            return;
        }
        
        // 加载客户数据
        this.loadCustomerData();
        
        // 渲染客户信息
        this.render();
    },
    
    /**
     * 加载客户数据
     */
    loadCustomerData() {
        // 使用统一的Mock数据
        let defaultCustomerData;

        if (window.MockData && window.MockData.customer) {
            const customer = window.MockData.customer;
            defaultCustomerData = {
                name: customer.maskedName,
                phone: customer.maskedPhone,
                idCard: customer.maskedIdCard,
                address: customer.address,
                type: customer.type,
                vipLevel: null,
                historyTickets: {
                    total: customer.historyStats.totalTickets,
                    completed: customer.historyStats.completedTickets,
                    processing: customer.historyStats.processingTickets,
                    satisfaction: customer.historyStats.averageSatisfaction
                },
                commonIssues: customer.commonIssues,
                lastContact: customer.lastContact,
                notes: customer.notes
            };
        } else {
            // 备用数据（与mock数据保持一致）
            defaultCustomerData = {
                name: '李**',
                phone: '138****3456',
                idCard: '3301**********4567',
                address: '阳光区阳光街道阳光花园小区15幢2单元302室',
                type: '个人',
                vipLevel: null,
                historyTickets: {
                    total: 3,
                    completed: 2,
                    processing: 1,
                    satisfaction: 4.5
                },
                commonIssues: ['物业管理', '环境卫生', '交通出行'],
                lastContact: '2024-12-20 09:15:30',
                notes: '该市民为阳光花园小区业主，较为关注小区环境和物业服务问题，沟通配合度高，反映问题具体详实。'
            };
        }

        // 安全地获取客户数据
        if (window.AppState && AppState.currentTicket && AppState.currentTicket.customer) {
            this.customerData = { ...defaultCustomerData, ...AppState.currentTicket.customer };
        } else {
            this.customerData = defaultCustomerData;
        }
    },
    
    /**
     * 渲染客户信息
     */
    render() {
        if (!this.container || !this.customerData) return;
        
        // 清空容器
        this.container.innerHTML = '';
        
        // 创建客户基本信息区域
        const basicInfoSection = this.createBasicInfoSection();
        this.container.appendChild(basicInfoSection);
        
        // 创建历史记录区域
        const historySection = this.createHistorySection();
        this.container.appendChild(historySection);
        
        // 创建常见问题区域
        const commonIssuesSection = this.createCommonIssuesSection();
        this.container.appendChild(commonIssuesSection);
        
        // 创建备注区域
        const notesSection = this.createNotesSection();
        this.container.appendChild(notesSection);
    },
    
    /**
     * 创建基本信息区域
     * @returns {HTMLElement} 基本信息元素
     */
    createBasicInfoSection() {
        const section = document.createElement('div');
        section.className = 'customer-basic-info';
        
        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-user"></i> 基本信息</h4>
            ${this.customerData.vipLevel ? `<span class="vip-badge">${this.customerData.vipLevel}</span>` : ''}
        `;
        
        const content = document.createElement('div');
        content.className = 'customer-info-grid';
        content.innerHTML = `
            <div class="info-row">
                <span class="info-label">姓名</span>
                <span class="info-value">${this.customerData.name}</span>
            </div>
            <div class="info-row">
                <span class="info-label">联系电话</span>
                <div class="info-value">
                    <span>${this.customerData.phone}</span>
                    <button class="btn-action" onclick="CustomerComponent.callCustomer()" title="拨打电话">
                        <i class="fas fa-phone"></i>
                    </button>
                </div>
            </div>
            <div class="info-row">
                <span class="info-label">身份证号</span>
                <span class="info-value">${this.customerData.idCard}</span>
            </div>
            <div class="info-row">
                <span class="info-label">地址信息</span>
                <span class="info-value">${this.customerData.address}</span>
            </div>
            <div class="info-row">
                <span class="info-label">客户类型</span>
                <span class="info-value">${this.customerData.type}</span>
            </div>
            <div class="info-row">
                <span class="info-label">最后联系</span>
                <span class="info-value">${this.customerData.lastContact}</span>
            </div>
        `;
        
        section.appendChild(header);
        section.appendChild(content);
        
        return section;
    },
    
    /**
     * 创建历史记录区域
     * @returns {HTMLElement} 历史记录元素
     */
    createHistorySection() {
        const section = document.createElement('div');
        section.className = 'customer-history';

        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-history"></i> 历史记录</h4>
            <button class="btn-link" onclick="CustomerComponent.viewFullHistory()">查看全部</button>
        `;

        const content = document.createElement('div');
        content.className = 'history-tickets';

        // 获取历史工单数据
        const previousTickets = window.MockData?.customer?.previousTickets || [];

        if (previousTickets.length === 0) {
            content.innerHTML = `
                <div class="no-history">
                    <i class="fas fa-inbox"></i>
                    <p>暂无历史工单</p>
                </div>
            `;
        } else {
            // 显示最近的2-3个工单
            const recentTickets = previousTickets.slice(0, 3);

            recentTickets.forEach(ticket => {
                const ticketElement = document.createElement('div');
                ticketElement.className = 'history-ticket-item';
                ticketElement.innerHTML = `
                    <div class="ticket-header">
                        <div class="ticket-number">${ticket.number}</div>
                        <div class="ticket-status ${this.getStatusClass(ticket.status)}">
                            ${this.getStatusText(ticket.status)}
                        </div>
                    </div>
                    <div class="ticket-title">${ticket.title}</div>
                    <div class="ticket-footer">
                        <div class="ticket-time">
                            <i class="fas fa-calendar-alt"></i>
                            ${ticket.createTime}
                        </div>
                        ${ticket.satisfaction ? `
                            <div class="ticket-satisfaction">
                                <i class="fas fa-star"></i>
                                ${ticket.satisfaction}分
                            </div>
                        ` : ''}
                    </div>
                `;

                // 添加点击事件
                ticketElement.addEventListener('click', () => {
                    this.viewTicketDetail(ticket.number);
                });

                content.appendChild(ticketElement);
            });

            // 如果有更多工单，显示提示
            if (previousTickets.length > 3) {
                const moreElement = document.createElement('div');
                moreElement.className = 'more-tickets';
                moreElement.innerHTML = `
                    <button class="btn-more" onclick="CustomerComponent.viewFullHistory()">
                        还有 ${previousTickets.length - 3} 个历史工单，点击查看全部
                    </button>
                `;
                content.appendChild(moreElement);
            }
        }

        // 添加平均满意度显示
        if (previousTickets.length > 0) {
            const satisfactionElement = document.createElement('div');
            satisfactionElement.className = 'satisfaction-rating';

            // 计算平均满意度
            const totalSatisfaction = previousTickets.reduce((sum, ticket) => sum + (ticket.satisfaction || 0), 0);
            const avgSatisfaction = (totalSatisfaction / previousTickets.length).toFixed(1);

            satisfactionElement.innerHTML = `
                <div class="rating-label">平均满意度</div>
                <div class="rating-value">
                    <span class="rating-score">${avgSatisfaction}</span>
                    <div class="rating-stars">
                        ${this.generateStarRating(parseFloat(avgSatisfaction))}
                    </div>
                </div>
            `;
            content.appendChild(satisfactionElement);
        }

        section.appendChild(header);
        section.appendChild(content);

        return section;
    },

    /**
     * 获取工单状态对应的CSS类名
     */
    getStatusClass(status) {
        const statusMap = {
            'closed': 'status-closed',
            'completed': 'status-completed',
            'processing': 'status-processing',
            'pending': 'status-pending',
            'cancelled': 'status-cancelled'
        };
        return statusMap[status] || 'status-unknown';
    },

    /**
     * 获取工单状态的中文文本
     */
    getStatusText(status) {
        const statusMap = {
            'closed': '已关闭',
            'completed': '已完成',
            'processing': '处理中',
            'pending': '待处理',
            'cancelled': '已取消'
        };
        return statusMap[status] || '未知状态';
    },

    /**
     * 查看工单详情
     */
    viewTicketDetail(ticketNumber) {
        Utils.showToast(`正在跳转到工单 ${ticketNumber}...`, 'info');
        // 这里可以实现跳转到具体工单的逻辑
        console.log('查看工单详情:', ticketNumber);
    },

    /**
     * 创建常见问题区域
     * @returns {HTMLElement} 常见问题元素
     */
    createCommonIssuesSection() {
        const section = document.createElement('div');
        section.className = 'customer-common-issues';
        
        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-tags"></i> 常见问题类型</h4>
        `;
        
        const content = document.createElement('div');
        content.className = 'common-issues-list';
        
        this.customerData.commonIssues.forEach(issue => {
            const issueElement = document.createElement('div');
            issueElement.className = 'issue-tag';
            issueElement.innerHTML = `
                <i class="fas fa-tag"></i>
                <span>${issue}</span>
            `;
            content.appendChild(issueElement);
        });
        
        section.appendChild(header);
        section.appendChild(content);
        
        return section;
    },
    
    /**
     * 创建备注区域
     * @returns {HTMLElement} 备注元素
     */
    createNotesSection() {
        const section = document.createElement('div');
        section.className = 'customer-notes';
        
        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-sticky-note"></i> 客户备注</h4>
            <button class="btn-link" onclick="CustomerComponent.editNotes()">编辑</button>
        `;
        
        const content = document.createElement('div');
        content.className = 'notes-content';
        content.innerHTML = `
            <div class="notes-text" id="customerNotes">${this.customerData.notes}</div>
        `;
        
        section.appendChild(header);
        section.appendChild(content);
        
        return section;
    },
    
    /**
     * 生成星级评分
     * @param {number} rating - 评分值
     * @returns {string} 星级HTML
     */
    generateStarRating(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        
        let starsHtml = '';
        
        // 实心星
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star"></i>';
        }
        
        // 半星
        if (hasHalfStar) {
            starsHtml += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // 空心星
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star"></i>';
        }
        
        return starsHtml;
    },
    
    /**
     * 拨打客户电话
     */
    callCustomer() {
        console.log('拨打客户电话:', this.customerData.phone);
        Utils.showToast('正在拨打客户电话...', 'info');
        
        // 这里应该集成电话系统
        setTimeout(() => {
            Utils.showToast('电话已接通', 'success');
        }, 2000);
    },
    
    /**
     * 查看完整历史记录
     */
    viewFullHistory() {
        console.log('查看客户完整历史记录');
        Utils.showToast('正在加载客户历史记录...', 'info');
        
        // 这里应该打开客户历史记录的模态框或新页面
        this.showHistoryModal();
    },
    
    /**
     * 显示历史记录模态框
     */
    showHistoryModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>客户历史记录</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="history-timeline">
                        <div class="history-item">
                            <div class="history-date">2024-12-20</div>
                            <div class="history-content">
                                <div class="history-title">小区噪音扰民投诉</div>
                                <div class="history-status">已办结</div>
                                <div class="history-satisfaction">满意度: 5.0</div>
                            </div>
                        </div>
                        <div class="history-item">
                            <div class="history-date">2024-11-15</div>
                            <div class="history-content">
                                <div class="history-title">路面积水问题反映</div>
                                <div class="history-status">已办结</div>
                                <div class="history-satisfaction">满意度: 4.5</div>
                            </div>
                        </div>
                        <div class="history-item">
                            <div class="history-date">2024-10-08</div>
                            <div class="history-content">
                                <div class="history-title">垃圾清理不及时</div>
                                <div class="history-status">已办结</div>
                                <div class="history-satisfaction">满意度: 4.0</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 显示模态框
        setTimeout(() => {
            modal.style.display = 'flex';
        }, 100);
    },
    
    /**
     * 编辑客户备注
     */
    editNotes() {
        console.log('编辑客户备注');
        
        const notesElement = document.getElementById('customerNotes');
        const currentNotes = notesElement.textContent;
        
        const newNotes = prompt('编辑客户备注:', currentNotes);
        if (newNotes !== null && newNotes !== currentNotes) {
            notesElement.textContent = newNotes;
            this.customerData.notes = newNotes;
            Utils.showToast('客户备注已更新', 'success');
        }
    },
    
    /**
     * 更新客户信息
     * @param {Object} newData - 新的客户数据
     */
    updateCustomerData(newData) {
        this.customerData = { ...this.customerData, ...newData };
        this.render();
    },
    
    /**
     * 获取客户数据
     * @returns {Object} 客户数据
     */
    getCustomerData() {
        return this.customerData;
    }
};

// 将CustomerComponent暴露到全局作用域
window.CustomerComponent = CustomerComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    CustomerComponent.initialize();
});
