/**
 * 筛选功能模块
 * 处理搜索、筛选、排序等功能
 */

window.FilterManager = {
    // 当前筛选条件
    currentFilters: {
        search: '',
        status: '',
        urgency: '',
        mode: '',
        startDate: '',
        endDate: '',
        quickFilter: 'all'
    },
    
    // 当前排序条件
    currentSort: {
        field: 'createTime',
        order: 'desc'
    },
    
    // 筛选历史
    filterHistory: [],
    
    /**
     * 初始化筛选功能
     */
    init: function() {
        this.bindEvents();
        this.loadFilterHistory();
        this.updateQuickFilterCounts();
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 快速筛选标签
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const filter = e.currentTarget.dataset.filter;
                this.setQuickFilter(filter);
            });
        });
        
        // 搜索输入框
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.setFilter('search', e.target.value);
                this.applyFilters();
            }, 300));
        }
        
        // 搜索按钮
        const searchBtn = document.querySelector('.search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performSearch();
            });
        }
        
        // 高级筛选按钮
        const advancedBtn = document.querySelector('.advanced-search-btn');
        if (advancedBtn) {
            advancedBtn.addEventListener('click', () => {
                this.toggleAdvancedSearch();
            });
        }
        
        // 高级筛选表单
        this.bindAdvancedFilterEvents();
        
        // 表格排序
        this.bindSortEvents();
        
        // 回车搜索
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.target.id === 'searchInput') {
                this.performSearch();
            }
        });
    },
    
    /**
     * 绑定高级筛选事件
     */
    bindAdvancedFilterEvents: function() {
        const filterSelects = ['statusFilter', 'urgencyFilter', 'modeFilter'];
        const filterInputs = ['startDate', 'endDate'];
        
        filterSelects.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    const filterKey = id.replace('Filter', '');
                    this.setFilter(filterKey, e.target.value);
                });
            }
        });
        
        filterInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    this.setFilter(id, e.target.value);
                });
            }
        });
        
        // 应用筛选按钮
        const applyBtn = document.querySelector('.apply-filter-btn');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyAdvancedFilter();
            });
        }
        
        // 重置筛选按钮
        const resetBtn = document.querySelector('.reset-filter-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetAdvancedFilter();
            });
        }
    },
    
    /**
     * 绑定排序事件
     */
    bindSortEvents: function() {
        document.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', (e) => {
                const field = e.currentTarget.dataset.sort;
                this.toggleSort(field);
            });
        });
    },
    
    /**
     * 设置快速筛选
     * @param {string} filter 筛选类型
     */
    setQuickFilter: function(filter) {
        // 更新激活状态
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        const activeTab = document.querySelector(`[data-filter="${filter}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }
        
        // 设置筛选条件
        this.currentFilters.quickFilter = filter;
        
        // 根据快速筛选设置具体条件
        this.applyQuickFilter(filter);
        
        // 应用筛选
        this.applyFilters();
    },
    
    /**
     * 应用快速筛选逻辑
     * @param {string} filter 筛选类型
     */
    applyQuickFilter: function(filter) {
        // 重置其他筛选条件
        this.resetFilters(false);
        
        switch (filter) {
            case 'all':
                // 显示所有工单
                break;
            case 'my-todo':
                // 我的待办 - 这里需要根据当前用户角色设置
                this.setFilter('assignedToMe', true);
                break;
            case 'urgent':
                // 紧急工单
                this.setFilter('urgency', 'urgent,critical');
                break;
            case 'overtime':
                // 超时工单
                this.setFilter('isOvertime', true);
                break;
            case 'supervise':
                // 督办工单
                this.setFilter('supervise', 'general,important,leader');
                break;
        }
    },
    
    /**
     * 设置筛选条件
     * @param {string} key 筛选键
     * @param {*} value 筛选值
     */
    setFilter: function(key, value) {
        this.currentFilters[key] = value;
    },
    
    /**
     * 获取筛选条件
     * @param {string} key 筛选键
     * @returns {*} 筛选值
     */
    getFilter: function(key) {
        return this.currentFilters[key];
    },
    
    /**
     * 重置筛选条件
     * @param {boolean} applyImmediately 是否立即应用
     */
    resetFilters: function(applyImmediately = true) {
        this.currentFilters = {
            search: '',
            status: '',
            urgency: '',
            mode: '',
            startDate: '',
            endDate: '',
            quickFilter: this.currentFilters.quickFilter || 'all'
        };
        
        // 重置表单
        this.resetFilterForm();
        
        if (applyImmediately) {
            this.applyFilters();
        }
    },
    
    /**
     * 重置筛选表单
     */
    resetFilterForm: function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';
        
        const selects = ['statusFilter', 'urgencyFilter', 'modeFilter'];
        selects.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });
        
        const dateInputs = ['startDate', 'endDate'];
        dateInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });
    },
    
    /**
     * 执行搜索
     */
    performSearch: function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            this.setFilter('search', searchInput.value);
        }
        this.applyFilters();
        this.saveToHistory();
    },
    
    /**
     * 切换高级搜索面板
     */
    toggleAdvancedSearch: function() {
        const panel = document.getElementById('advancedFilterPanel');
        if (panel) {
            panel.classList.toggle('show');
            
            const btn = document.querySelector('.advanced-search-btn');
            if (btn) {
                const isShow = panel.classList.contains('show');
                btn.innerHTML = `
                    <i class="fas fa-filter"></i>
                    ${isShow ? '收起筛选' : '高级筛选'}
                `;
            }
        }
    },
    
    /**
     * 应用高级筛选
     */
    applyAdvancedFilter: function() {
        // 收集所有筛选条件
        const statusFilter = document.getElementById('statusFilter');
        const urgencyFilter = document.getElementById('urgencyFilter');
        const modeFilter = document.getElementById('modeFilter');
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        
        if (statusFilter) this.setFilter('status', statusFilter.value);
        if (urgencyFilter) this.setFilter('urgency', urgencyFilter.value);
        if (modeFilter) this.setFilter('mode', modeFilter.value);
        if (startDate) this.setFilter('startDate', startDate.value);
        if (endDate) this.setFilter('endDate', endDate.value);
        
        // 应用筛选
        this.applyFilters();
        this.saveToHistory();
        
        // 显示成功消息
        Utils.showMessage('筛选条件已应用', 'success');
    },
    
    /**
     * 重置高级筛选
     */
    resetAdvancedFilter: function() {
        this.resetFilters();
        Utils.showMessage('筛选条件已重置', 'info');
    },
    
    /**
     * 切换排序
     * @param {string} field 排序字段
     */
    toggleSort: function(field) {
        if (this.currentSort.field === field) {
            // 切换排序方向
            this.currentSort.order = this.currentSort.order === 'asc' ? 'desc' : 'asc';
        } else {
            // 设置新的排序字段
            this.currentSort.field = field;
            this.currentSort.order = 'desc';
        }
        
        // 更新排序图标
        this.updateSortIcons();
        
        // 应用排序
        this.applyFilters();
    },
    
    /**
     * 更新排序图标
     */
    updateSortIcons: function() {
        // 重置所有排序图标
        document.querySelectorAll('.sortable').forEach(th => {
            th.classList.remove('sorted-asc', 'sorted-desc');
        });
        
        // 设置当前排序图标
        const currentTh = document.querySelector(`[data-sort="${this.currentSort.field}"]`);
        if (currentTh) {
            currentTh.classList.add(`sorted-${this.currentSort.order}`);
        }
    },
    
    /**
     * 应用筛选和排序
     */
    applyFilters: function() {
        // 构建查询参数
        const params = {
            ...this.currentFilters,
            sortBy: this.currentSort.field,
            sortOrder: this.currentSort.order,
            page: 1, // 重置到第一页
            pageSize: window.TableManager ? window.TableManager.pageSize : 50
        };
        
        // 清理空值
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });
        
        // 显示加载状态
        this.showLoading();
        
        // 调用API获取数据
        if (window.mockAPI) {
            window.mockAPI.getTickets(params).then(result => {
                this.hideLoading();
                if (window.TableManager) {
                    window.TableManager.updateTable(result);
                }
                this.updateFilterCounts();
            }).catch(error => {
                this.hideLoading();
                Utils.showMessage('加载数据失败', 'error');
                console.error('Filter error:', error);
            });
        }
    },
    
    /**
     * 显示加载状态
     */
    showLoading: function() {
        const loadingState = document.getElementById('loadingState');
        const tableBody = document.getElementById('ticketTableBody');
        
        if (loadingState) loadingState.style.display = 'flex';
        if (tableBody) tableBody.style.opacity = '0.5';
    },
    
    /**
     * 隐藏加载状态
     */
    hideLoading: function() {
        const loadingState = document.getElementById('loadingState');
        const tableBody = document.getElementById('ticketTableBody');
        
        if (loadingState) loadingState.style.display = 'none';
        if (tableBody) tableBody.style.opacity = '1';
    },
    
    /**
     * 更新快速筛选计数
     */
    updateQuickFilterCounts: function() {
        if (window.mockAPI) {
            // 获取各种筛选条件的数量
            const countPromises = [
                window.mockAPI.getTickets({}), // 全部
                window.mockAPI.getTickets({ assignedToMe: true }), // 我的待办
                window.mockAPI.getTickets({ urgency: 'urgent,critical' }), // 紧急
                window.mockAPI.getTickets({ isOvertime: true }), // 超时
                window.mockAPI.getTickets({ supervise: 'general,important,leader' }) // 督办
            ];
            
            Promise.all(countPromises).then(results => {
                const counts = results.map(result => result.total);
                this.updateFilterTabCounts(counts);
            });
        }
    },
    
    /**
     * 更新筛选标签计数
     * @param {Array} counts 计数数组
     */
    updateFilterTabCounts: function(counts) {
        const filters = ['all', 'my-todo', 'urgent', 'overtime', 'supervise'];
        filters.forEach((filter, index) => {
            const tab = document.querySelector(`[data-filter="${filter}"] .count`);
            if (tab && counts[index] !== undefined) {
                tab.textContent = counts[index];
            }
        });
    },
    
    /**
     * 更新筛选计数
     */
    updateFilterCounts: function() {
        // 这里可以根据当前筛选结果更新计数
        // 实际项目中可能需要额外的API调用
    },
    
    /**
     * 保存筛选历史
     */
    saveToHistory: function() {
        const historyItem = {
            filters: Utils.deepClone(this.currentFilters),
            sort: Utils.deepClone(this.currentSort),
            timestamp: new Date().toISOString()
        };
        
        this.filterHistory.unshift(historyItem);
        
        // 限制历史记录数量
        if (this.filterHistory.length > 10) {
            this.filterHistory = this.filterHistory.slice(0, 10);
        }
        
        // 保存到本地存储
        try {
            localStorage.setItem('ticketFilterHistory', JSON.stringify(this.filterHistory));
        } catch (e) {
            console.warn('无法保存筛选历史到本地存储');
        }
    },
    
    /**
     * 加载筛选历史
     */
    loadFilterHistory: function() {
        try {
            const saved = localStorage.getItem('ticketFilterHistory');
            if (saved) {
                this.filterHistory = JSON.parse(saved);
            }
        } catch (e) {
            console.warn('无法从本地存储加载筛选历史');
            this.filterHistory = [];
        }
    },
    
    /**
     * 获取当前筛选参数
     * @returns {Object} 筛选参数
     */
    getCurrentParams: function() {
        return {
            ...this.currentFilters,
            sortBy: this.currentSort.field,
            sortOrder: this.currentSort.order
        };
    }
};
