# 12345热线来电弹屏系统

## 项目结构

本项目已经重构为模块化架构，将CSS和JavaScript文件按功能拆分，便于维护和开发。

### 文件结构

```
来电弹屏原型/
├── incoming-call-popup.html          # 主HTML文件
├── README.md                          # 项目说明文档
├── css/                              # CSS样式文件目录
│   ├── base.css                      # 基础样式和重置样式
│   ├── layout.css                    # 布局相关样式
│   ├── animations.css                # 动画效果样式
│   └── components/                   # 组件样式目录
│       ├── agent-status.css          # 坐席状态栏样式
│       ├── softphone.css             # 软电话工具栏样式
│       ├── left-panel.css            # 左栏客户信息样式
│       ├── center-panel.css          # 中栏交互历史样式
│       ├── right-panel.css           # 右栏智能辅助样式
│       └── buttons.css               # 通用按钮样式
├── js/                               # JavaScript文件目录
│   ├── main.js                       # 主应用文件
│   └── modules/                      # 功能模块目录
│       ├── agent-status.js           # 坐席状态管理
│       ├── softphone.js              # 软电话功能
│       ├── dual-info.js              # 双核信息显示
│       ├── ticket-manager.js         # 工单管理
│       ├── ui-effects.js             # UI效果管理
│       ├── notification.js           # 通知系统
│       └── event-handlers.js         # 事件处理
├── styles.css                        # 原始样式文件（已拆分）
└── script.js                         # 原始脚本文件（已拆分）
```

## 模块说明

### CSS模块

#### 基础模块
- **base.css**: 包含全局重置样式、字体设置、滚动条美化等基础样式
- **layout.css**: 负责页面布局、响应式设计、面板排列等
- **animations.css**: 包含各种动画效果和过渡效果

#### 组件模块
- **agent-status.css**: 坐席状态栏、状态指示器、计时器等样式
- **softphone.css**: 软电话控制按钮、通话计时器等样式
- **left-panel.css**: 客户信息卡片、地址信息、统计数据等样式
- **center-panel.css**: 历史工单、双核信息显示、智能工单草稿等样式
- **right-panel.css**: AI辅助、知识库、话术推荐等样式
- **buttons.css**: 通用按钮样式和状态

### JavaScript模块

#### 主应用
- **main.js**: 主应用类，负责初始化和协调各个模块

#### 功能模块
- **agent-status.js**: 管理坐席状态、工作时长计时等功能
- **softphone.js**: 管理软电话状态、通话控制等功能
- **dual-info.js**: 管理实时语音转写和对话摘要功能
- **ticket-manager.js**: 管理工单创建、提交、历史记录等功能
- **ui-effects.js**: 管理各种UI动画效果和交互效果
- **notification.js**: 管理通知消息的显示和管理
- **event-handlers.js**: 统一管理页面事件绑定和处理

## 功能特性

### 坐席状态管理
- 实时状态显示（空闲、忙碌、通话中、小休）
- 状态时长计时
- 连续工作时长统计
- 状态切换控制

### 软电话功能
- 来电接听/挂断
- 通话保持/恢复
- 静音/取消静音
- 通话录音
- 通话转接
- 通话时长计时

### 双核信息显示
- 实时语音转写
- 智能对话摘要
- AI关键词提取
- 意图识别
- 决策建议生成

### 智能工单系统
- 基于对话内容智能填单
- 工单草稿保存
- 即时办结功能
- 历史工单管理
- 相似工单推荐

### 智能辅助
- 知识库智能匹配
- 标准话术推荐
- 客户标签管理
- 地址地图定位

## 开发指南

### 添加新功能

1. **CSS样式**: 在对应的组件CSS文件中添加样式，或创建新的组件文件
2. **JavaScript功能**: 在对应的模块JS文件中添加方法，或创建新的模块文件
3. **事件处理**: 在event-handlers.js中添加新的事件绑定

### 模块通信

各模块通过主应用实例进行通信：
```javascript
// 获取其他模块实例
const notification = window.incomingCallApp.getNotification();
const uiEffects = window.incomingCallApp.getUIEffects();

// 调用其他模块方法
notification.show('消息内容', 'success');
uiEffects.fadeIn(element);
```

### 样式规范

- 使用BEM命名规范
- 组件样式独立，避免全局污染
- 使用CSS变量定义主题色彩
- 响应式设计优先

### 代码规范

- 使用JSDoc注释
- 模块化设计，单一职责
- 错误处理和边界检查
- 事件监听器的正确绑定和解绑

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 性能优化

- CSS文件按需加载
- JavaScript模块化加载
- 图片资源优化
- 动画性能优化
- 内存泄漏防护

## 拆分完成状态

✅ **CSS文件拆分完成**
- 原始 `styles.css` (2113行) 已拆分为9个模块文件
- 按功能模块组织，便于维护和开发
- 保持了所有原有样式和动画效果

✅ **JavaScript文件拆分完成**
- 原始 `script.js` (1351行) 已拆分为8个模块文件
- 采用模块化架构，单一职责原则
- 保持了所有原有功能，包括来电模拟

✅ **功能验证**
- 来电模拟功能正常工作
- 所有交互效果保持一致
- 通知系统正常运行
- 双核信息显示功能完整

## 测试说明

### 功能测试
1. 打开 `incoming-call-popup.html` - 主要功能页面
2. 打开 `test.html` - 模块化测试页面
3. 验证来电模拟、接听、挂断等功能
4. 检查通知系统和UI效果

### 来电模拟流程
1. 页面加载后2秒自动模拟来电
2. 显示来电指示器和通知消息
3. 软电话按钮状态正确更新
4. 可以正常接听和挂断电话

## 维护说明

### 文件修改指南
1. **样式修改**: 找到对应的CSS组件文件进行修改
2. **功能修改**: 找到对应的JavaScript模块文件进行修改
3. **新增功能**: 创建新的模块文件并在main.js中引入

### 调试技巧
1. 使用浏览器开发者工具
2. 检查控制台错误信息
3. 使用断点调试JavaScript
4. 检查网络请求状态
5. 使用 `test.html` 进行模块测试

### 部署注意事项
1. 确保所有CSS和JS文件路径正确
2. 检查文件加载顺序
3. 验证所有功能正常工作
4. 测试不同浏览器兼容性

## 问题排查

如果来电模拟不工作，请检查：
1. 浏览器控制台是否有JavaScript错误
2. 所有模块文件是否正确加载
3. HTML元素ID是否正确匹配
4. CSS样式文件是否正确引用
