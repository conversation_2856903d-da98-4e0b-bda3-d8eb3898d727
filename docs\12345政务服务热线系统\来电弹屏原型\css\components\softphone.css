/**
 * 软电话工具栏样式
 * 包含通话控制按钮、计时器等
 */

/* 软电话工具栏样式 */
.softphone-tools {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 20px;
}

.call-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-call {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 60px;
}

.btn-call:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.btn-call .icon {
    font-size: 16px;
    line-height: 1;
}

.btn-call .label {
    font-size: 10px;
    line-height: 1;
    white-space: nowrap;
}

/* 不同状态的按钮样式 */
.btn-answer {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
}

.btn-answer:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-hangup {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-color: #dc3545;
}

.btn-hangup:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-mute.active {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border-color: #ffc107;
}

.btn-hold.active {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    border-color: #6f42c1;
}

.btn-record.active {
    background: linear-gradient(135deg, #fd7e14 0%, #e8650e 100%);
    border-color: #fd7e14;
    animation: recordPulse 2s infinite;
}

.call-timer {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(0, 0, 0, 0.3);
    padding: 6px 12px;
    border-radius: 20px;
    color: #ffd700;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

.timer-icon {
    font-size: 14px;
}

.timer-text {
    font-size: 14px;
    min-width: 40px;
}

@keyframes recordPulse {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(253, 126, 20, 0.6);
    }
}
