<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12345政务服务热线系统业务流程可视化</title>
    
    <!-- 外部依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>12345政务服务热线系统</h1>
            <p>完整业务流程可视化展示</p>
        </div>

        <div class="content">
            <div class="stage-selector">
                <button class="stage-btn active" onclick="showStage('overview', this)">总体流程</button>
                <button class="stage-btn" onclick="showStage('stage1', this)">阶段一：市级受理分派</button>
                <button class="stage-btn" onclick="showStage('stage2', this)">阶段二：轨道流转</button>
                <button class="stage-btn" onclick="showStage('stage3', this)">阶段三：逐级审核反馈</button>
                <button class="stage-btn" onclick="showStage('stage4', this)">阶段四：统一汇入回访</button>
                <button class="stage-btn" onclick="showStage('stage5', this)">阶段五：回访关闭</button>
            </div>

            <div class="collaborative-selector">
                <h3>主协办协同流程详解</h3>
                <div class="collaborative-buttons">
                    <button class="collab-btn" onclick="showStage('municipal-collab', this)">市级主协办协同</button>
                    <button class="collab-btn" onclick="showStage('district-collab', this)">区级主协办协同</button>
                    <button class="collab-btn" onclick="showStage('street-collab', this)">街镇级主协办协同</button>
                    <button class="collab-btn" onclick="showStage('collab-review', this)">主协办审核流程</button>
                </div>
            </div>

            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #27ae60;"></div>
                    <span>即时办结</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12;"></div>
                    <span>市级部门处理</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #3498db;"></div>
                    <span>区县级处理</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c;"></div>
                    <span>协同处理</span>
                </div>
            </div>

            <div class="mermaid-container" id="diagram-container">
                <div class="diagram-controls">
                    <button class="control-btn" onclick="zoomIn()" title="放大 (Ctrl/Cmd + +)">
                        <span>🔍+</span>
                    </button>
                    <button class="control-btn" onclick="zoomOut()" title="缩小 (Ctrl/Cmd + -)">
                        <span>🔍-</span>
                    </button>
                    <button class="control-btn" onclick="resetZoom()" title="重置缩放 (Ctrl/Cmd + 0)">
                        <span>⟲</span>
                    </button>
                    <button class="control-btn" onclick="toggleFullscreen()" title="全屏/退出全屏 (F11)" id="fullscreen-btn">
                        <span>⛶</span>
                    </button>
                    <button class="control-btn" onclick="showExportOptions()" title="导出图片">
                        <span>📷</span>
                    </button>
                    <button class="control-btn" onclick="showHelp()" title="查看操作帮助">
                        <span>❓</span>
                    </button>
                </div>
                <div class="zoom-info" id="zoom-info">100%</div>
                <div class="diagram-wrapper" id="diagram-wrapper">
                    <div id="mermaid-diagram"></div>
                </div>
            </div>

            <!-- 帮助提示弹窗 -->
            <div id="help-modal" class="help-modal" style="display: none;">
                <div class="help-content">
                    <div class="help-header">
                        <h3>流程图操作帮助</h3>
                        <button class="close-help" onclick="hideHelp()">×</button>
                    </div>
                    <div class="help-body">
                        <h4>🖱️ 鼠标操作</h4>
                        <ul>
                            <li><strong>拖拽浏览：</strong>按住鼠标左键拖动查看图表不同区域</li>
                            <li><strong>滚轮缩放：</strong>按住 Ctrl/Cmd + 滚轮 进行精确缩放</li>
                            <li><strong>智能拖拽：</strong>放大后可以拖拽查看图表的任意部分</li>
                        </ul>

                        <h4>⌨️ 键盘快捷键</h4>
                        <ul>
                            <li><strong>Ctrl/Cmd + +：</strong>放大</li>
                            <li><strong>Ctrl/Cmd + -：</strong>缩小</li>
                            <li><strong>Ctrl/Cmd + 0：</strong>重置缩放</li>
                            <li><strong>F11：</strong>全屏/退出全屏</li>
                            <li><strong>ESC：</strong>退出全屏</li>
                        </ul>

                        <h4>🔧 控制按钮</h4>
                        <ul>
                            <li><strong>🔍+：</strong>放大图表</li>
                            <li><strong>🔍-：</strong>缩小图表</li>
                            <li><strong>⟲：</strong>重置缩放和位置</li>
                            <li><strong>⛶：</strong>切换全屏模式</li>
                            <li><strong>📷：</strong>导出图片（支持PNG/JPG格式，多种质量选项）</li>
                        </ul>

                        <h4>📷 导出功能</h4>
                        <ul>
                            <li><strong>格式选择：</strong>支持PNG（推荐）和JPG格式</li>
                            <li><strong>质量选项：</strong>标准(1x)、高质量(2x)、超高质量(3x)</li>
                            <li><strong>背景选择：</strong>白色、透明或当前背景色</li>
                            <li><strong>自动命名：</strong>文件名包含阶段名称和时间戳</li>
                        </ul>

                        <h4>📱 触摸操作（移动端）</h4>
                        <ul>
                            <li><strong>单指拖拽：</strong>移动查看图表不同区域</li>
                            <li><strong>按钮操作：</strong>点击控制按钮进行缩放和导出</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 导出选项弹窗 -->
            <div id="export-modal" class="help-modal" style="display: none;">
                <div class="help-content">
                    <div class="help-header">
                        <h3>导出图片选项</h3>
                        <button class="close-help" onclick="hideExportOptions()">×</button>
                    </div>
                    <div class="help-body">
                        <div class="export-option">
                            <h4>📷 导出格式</h4>
                            <div class="export-format-group">
                                <label class="export-radio">
                                    <input type="radio" name="exportFormat" value="png" checked>
                                    <span>PNG (推荐)</span>
                                    <small>高质量，支持透明背景</small>
                                </label>
                                <label class="export-radio">
                                    <input type="radio" name="exportFormat" value="jpg">
                                    <span>JPG</span>
                                    <small>文件较小，白色背景</small>
                                </label>
                            </div>
                        </div>

                        <div class="export-option">
                            <h4>📐 图片质量</h4>
                            <div class="export-quality-group">
                                <label class="export-radio">
                                    <input type="radio" name="exportQuality" value="1" checked>
                                    <span>标准质量 (1x)</span>
                                    <small>适合网页显示</small>
                                </label>
                                <label class="export-radio">
                                    <input type="radio" name="exportQuality" value="2">
                                    <span>高质量 (2x)</span>
                                    <small>适合打印，文件较大</small>
                                </label>
                                <label class="export-radio">
                                    <input type="radio" name="exportQuality" value="3">
                                    <span>超高质量 (3x)</span>
                                    <small>最佳质量，文件很大</small>
                                </label>
                            </div>
                        </div>

                        <div class="export-option">
                            <h4>🎨 背景选项</h4>
                            <div class="export-bg-group">
                                <label class="export-radio">
                                    <input type="radio" name="exportBackground" value="white" checked>
                                    <span>白色背景</span>
                                </label>
                                <label class="export-radio">
                                    <input type="radio" name="exportBackground" value="transparent">
                                    <span>透明背景</span>
                                    <small>仅PNG格式支持</small>
                                </label>
                                <label class="export-radio">
                                    <input type="radio" name="exportBackground" value="current">
                                    <span>当前背景</span>
                                    <small>保持页面背景色</small>
                                </label>
                            </div>
                        </div>

                        <div class="export-actions">
                            <button class="export-btn export-btn-primary" onclick="executeExport()">
                                📷 开始导出
                            </button>
                            <button class="export-btn export-btn-secondary" onclick="hideExportOptions()">
                                取消
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="stage-details" class="stage-details">
                <!-- 详细信息将通过JavaScript动态加载 -->
            </div>
        </div>

        <div class="footer">
            <p>© 2024 12345政务服务热线系统 - 业务流程可视化</p>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/diagrams.js"></script>
    <script src="js/stage-details.js"></script>
    <script src="js/diagram-controls.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
