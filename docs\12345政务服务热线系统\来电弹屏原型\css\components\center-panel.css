/**
 * 中栏交互历史与工作区样式
 * 包含历史工单、双核信息显示、智能工单草稿等
 */

/* 紧凑的历史工单概览样式 */
.history-overview {
    flex-shrink: 0;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.history-header h3 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.expand-history {
    font-size: 12px;
    color: #007bff;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.expand-history:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 历史工单占位符样式 */
.history-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    text-align: center;
}

.history-placeholder .placeholder-content {
    color: #6c757d;
}

.history-placeholder .placeholder-icon {
    font-size: 36px;
    margin-bottom: 12px;
    opacity: 0.6;
}

.history-placeholder .placeholder-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 6px;
    color: #495057;
}

.history-placeholder .placeholder-desc {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

.ticket-summary {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.ticket-item {
    display: grid;
    grid-template-columns: 120px 1fr 100px auto;
    gap: 12px;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 8px;
    border-left: 3px solid #28a745;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
}

.ticket-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ticket-item.processing {
    border-left-color: #ffc107;
}

.ticket-item.closed {
    border-left-color: #28a745;
}

.ticket-item .ticket-id {
    font-weight: 600;
    color: #495057;
    font-size: 12px;
}

.ticket-item .ticket-title {
    font-weight: 500;
    color: #212529;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ticket-item .ticket-status {
    font-weight: 600;
    font-size: 11px;
    text-align: center;
    position: relative;
    padding-left: 16px;
}

.ticket-status::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

.ticket-item.closed .ticket-status {
    color: #28a745;
}

.ticket-item.processing .ticket-status {
    color: #ffc107;
}

.ticket-card.processing .ticket-status::before {
    animation: pulse-indicator 2s infinite;
}

.ticket-actions-mini {
    display: flex;
    gap: 4px;
}

.btn-mini {
    padding: 2px 6px;
    font-size: 10px;
    border: 1px solid #dee2e6;
    background: white;
    color: #6c757d;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-mini:hover {
    background: #f8f9fa;
    color: #495057;
    border-color: #adb5bd;
}

.ticket-card {
    border: 1px solid rgba(222, 226, 230, 0.5);
    border-radius: 12px;
    padding: 18px;
    margin-bottom: 12px;
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 4px 16px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ticket-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ticket-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.ticket-card:hover::before {
    opacity: 1;
}

.ticket-card.processing {
    border-left-color: #ffc107;
}

.ticket-card.closed {
    border-left-color: #28a745;
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.ticket-id {
    font-weight: bold;
    color: #495057;
}

.ticket-status {
    font-weight: bold;
}

.ticket-card.closed .ticket-status {
    color: #28a745;
}

.ticket-card.processing .ticket-status {
    color: #ffc107;
}

.ticket-category {
    color: #6c757d;
    font-size: 12px;
    margin-bottom: 5px;
}

.ticket-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.ticket-time {
    color: #6c757d;
    font-size: 12px;
    margin-bottom: 8px;
}

.ticket-actions {
    display: flex;
    gap: 10px;
}

/* 动态工作区样式 - 允许内容滚动 */
.dynamic-workspace {
    flex: 1;
    padding-top: 16px;
    overflow-y: auto;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

.incoming-call {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    border-radius: 16px;
    border: 2px dashed #28a745;
}

.incoming-call h3 {
    font-size: 24px;
    font-weight: 700;
    color: #28a745;
    text-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
    margin-bottom: 0;
}

.pulse-animation {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 50%;
    margin: 0 auto 24px;
    animation: pulse 1.5s infinite;
    position: relative;
    box-shadow: 0 8px 24px rgba(40, 167, 69, 0.3);
}

.pulse-animation::before {
    content: '📞';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 32px;
    animation: bounce 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 8px 24px rgba(40, 167, 69, 0.3);
    }
    50% {
        transform: scale(1.15);
        opacity: 0.8;
        box-shadow: 0 12px 32px rgba(40, 167, 69, 0.5);
    }
    100% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 8px 24px rgba(40, 167, 69, 0.3);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translate(-50%, -50%) translateY(0);
    }
    40% {
        transform: translate(-50%, -50%) translateY(-4px);
    }
    60% {
        transform: translate(-50%, -50%) translateY(-2px);
    }
}

@keyframes pulse-indicator {
    0% {
        box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
    }
    50% {
        box-shadow: 0 0 16px rgba(40, 167, 69, 0.8);
    }
    100% {
        box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
    }
}

/* 双核信息呈现区域样式 - 更宽敞的布局 */
.dual-info-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    flex: 1;
    min-height: 0;
}

/* 实时语音转写框样式 - 更高更宽敞 */
.voice-transcript-box {
    border: 2px solid #6f42c1;
    border-radius: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);
    min-height: 400px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 24px rgba(111, 66, 193, 0.15);
    position: relative;
    overflow: hidden;
}

.voice-transcript-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6f42c1 0%, #9c88ff 100%);
    /* animation: loading 2s ease-in-out infinite; */
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

.transcript-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.transcript-header h4 {
    margin: 0;
    color: #6f42c1;
    font-size: 14px;
    font-weight: bold;
}

.transcript-status {
    display: flex;
    align-items: center;
    gap: 6px;
}

.transcript-content {
    flex: 1;
    overflow-y: auto;
    padding: 5px 0;
}

.transcript-message {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
}

.transcript-message.agent {
    background: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.transcript-message.citizen {
    background: #fff3e0;
    border-left: 3px solid #ff9800;
}

.transcript-message .speaker {
    font-weight: bold;
    color: #495057;
}

.transcript-message .message {
    margin: 0 8px;
    color: #212529;
}

.transcript-message .timestamp {
    font-size: 11px;
    color: #6c757d;
    float: right;
}

/* 实时对话摘要框样式 - 更高更宽敞 */
.conversation-summary-box {
    border: 2px solid #17a2b8;
    border-radius: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    min-height: 400px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 24px rgba(23, 162, 184, 0.15);
    position: relative;
    overflow: hidden;
}

.conversation-summary-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #17a2b8 0%, #20c997 100%);
    /* animation: loading 2s ease-in-out infinite reverse; */
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.summary-header h4 {
    margin: 0;
    color: #17a2b8;
    font-size: 14px;
    font-weight: bold;
}

.summary-status {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-text {
    font-size: 11px;
    color: #6c757d;
    font-weight: bold;
}

.summary-content {
    flex: 1;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 5px 0;
    max-height: 450px; /* 增加最大高度以容纳8个字段 */
    min-height: 0; /* 移除最小高度限制 */
}

/* 当有实际内容时才显示滚动条 */
.summary-content.has-content {
    overflow-y: auto;
}

/* 自定义滚动条样式 */
.summary-content.has-content::-webkit-scrollbar {
    width: 6px;
}

.summary-content.has-content::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 3px;
}

.summary-content.has-content::-webkit-scrollbar-thumb {
    background: rgba(23, 162, 184, 0.5);
    border-radius: 3px;
}

.summary-content.has-content::-webkit-scrollbar-thumb:hover {
    background: rgba(23, 162, 184, 0.7);
}

.summary-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 12px;
}

.summary-label {
    font-weight: 800;
    color: #2c3e50;
    min-width: 80px;
    flex-shrink: 0;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.3px;
}

.summary-value {
    color: #2c3e50;
    flex: 1;
    line-height: 1.3;
    transition: all 0.3s ease;
    font-weight: 500;
}

.summary-value.emotion-normal {
    color: #28a745;
    font-weight: bold;
}

.summary-value.emotion-angry {
    color: #dc3545;
    font-weight: bold;
}

.summary-value.priority-low {
    color: #6c757d;
}

.summary-value.priority-medium {
    color: #ffc107;
    font-weight: bold;
}

.summary-value.priority-high {
    color: #fd7e14;
    font-weight: bold;
}

.summary-value.priority-urgent {
    color: #dc3545;
    font-weight: bold;
}

.summary-value.decision-suggestion {
    background: linear-gradient(135deg, #fff3cd 0%, #d4edda 100%);
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #28a745;
    font-weight: bold;
    color: #155724;
    line-height: 1.4;
}

/* 空数据状态样式 */
.summary-value[data-empty="true"] {
    color: #6c757d;
    font-style: italic;
}

/* 已填充数据状态 */
.summary-value[data-filled="true"] {
    color: #2c3e50 !important;
    font-weight: 600 !important;
    text-shadow: none !important;
    background: rgba(255, 255, 255, 0.95);
    padding: 3px 8px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 智能工单草稿样式 - 支持滚动 */
.smart-ticket-draft {
    border: 2px solid #28a745;
    border-radius: 16px;
    padding: 24px;
    background: linear-gradient(135deg, #d4edda 0%, #d1ecf1 100%);
    margin-bottom: 20px;
    box-shadow: 0 8px 24px rgba(40, 167, 69, 0.15);
    position: relative;
    overflow-y: auto;
    max-height: 600px;
    display: flex;
    flex-direction: column;
}

/* 自定义滚动条样式 */
.smart-ticket-draft::-webkit-scrollbar {
    width: 6px;
}

.smart-ticket-draft::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 3px;
}

.smart-ticket-draft::-webkit-scrollbar-thumb {
    background: rgba(40, 167, 69, 0.6);
    border-radius: 3px;
}

.smart-ticket-draft::-webkit-scrollbar-thumb:hover {
    background: rgba(40, 167, 69, 0.8);
}

.smart-ticket-draft::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.draft-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.draft-header h4 {
    margin: 0;
    color: #28a745;
    font-size: 16px;
}

.draft-form {
    display: grid;
    gap: 12px;
    flex: 1;
    min-height: 0;
}

.form-group {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #495057;
    position: relative;
    display: inline-block;
}

.form-group label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.form-group:focus-within label::after {
    width: 100%;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0,123,255,0.25);
}

.form-group textarea {
    height: 80px;
    resize: vertical;
}

.auto-filled {
    background-color: #e3f2fd !important;
    border-color: #2196f3 !important;
    position: relative;
    overflow: visible;
}

.auto-filled::after {
    content: 'AI';
    position: absolute;
    top: -12px;
    right: 8px;
    background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
    color: white;
    font-size: 10px;
    padding: 3px 6px;
    border-radius: 6px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    animation: aiGlow 2s ease-in-out infinite alternate;
}

@keyframes aiGlow {
    from { box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3); }
    to { box-shadow: 0 4px 16px rgba(33, 150, 243, 0.6); }
}

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding-top: 12px;
    border-top: 1px solid #dee2e6;
    flex-shrink: 0;
    flex-wrap: wrap;
}
