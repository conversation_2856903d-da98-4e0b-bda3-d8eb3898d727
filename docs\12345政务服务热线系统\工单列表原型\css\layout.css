/* 布局样式 */

/* 页面头部 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-white);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.page-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin: 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.breadcrumb .separator {
    color: var(--text-disabled);
}

.breadcrumb .current {
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-role {
    padding: 2px var(--spacing-xs);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-radius: var(--border-radius-sm);
}

.user-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    color: #ffffff;
    background-color: var(--primary-color);
    border-radius: 50%;
}

/* 主要内容区域 */
.main-content {
    padding: var(--spacing-lg);
    max-width: 1600px;
    margin: 0 auto;
}

/* 统计面板 */
.dashboard-panel {
    margin-bottom: var(--spacing-lg);
}

.stat-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background-color: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.stat-card:hover {
    box-shadow: var(--card-hover-shadow);
    transform: translateY(-2px);
}

.stat-card.urgent {
    background-color: var(--stat-urgent-bg);
    border-left: 4px solid var(--error-color);
}

.stat-card.warning {
    background-color: var(--stat-warning-bg);
    border-left: 4px solid var(--warning-color);
}

.stat-card.pending {
    background-color: var(--stat-pending-bg);
    border-left: 4px solid var(--primary-color);
}

.stat-card.merge {
    background-color: var(--stat-merge-bg);
    border-left: 4px solid var(--success-color);
}

.stat-icon {
    font-size: 32px;
    opacity: 0.8;
}

.stat-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stat-number {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

/* 筛选区域 - 紧凑横向布局 */
.filter-section {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.filter-toolbar {
    padding: var(--spacing-md) var(--spacing-lg);
}

.quick-filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xl);
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    white-space: nowrap;
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-right: var(--spacing-xs);
    flex-shrink: 0;
}

.filter-buttons {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.filter-btn {
    padding: 4px var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: var(--bg-light);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
}

.filter-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.filter-btn.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

.filter-select {
    min-width: 120px;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
}

/* 搜索区域 - 整合到筛选区域 */
.search-area {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-left: auto;
    flex-shrink: 0;
}

.search-input-group {
    display: flex;
    width: 300px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    height: 32px;
}

.search-input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.search-input {
    flex: 1;
    padding: 6px var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: none;
    background-color: var(--bg-white);
    height: 100%;
}

.search-input::placeholder {
    color: var(--text-placeholder);
}

.search-btn {
    padding: 0 var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: var(--bg-light);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    height: 100%;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.advanced-search-btn {
    padding: 4px var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    background-color: transparent;
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.advanced-search-btn:hover {
    background-color: var(--primary-light);
}

/* 角色专用筛选 */
.role-specific-filters {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--bg-gray);
    border-top: 1px solid var(--border-light);
    display: none;
}

.role-specific-filters.show {
    display: block;
}

.role-filter {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-md);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.view-controls {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.view-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    background-color: var(--bg-white);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.view-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.view-btn.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.view-btn + .view-btn {
    border-left: 1px solid var(--border-light);
}

/* 批量操作提示 */
.batch-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--batch-bg);
    border: 1px solid var(--batch-border);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.batch-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.batch-text {
    font-size: var(--font-size-sm);
    color: var(--batch-text);
    font-weight: var(--font-weight-medium);
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 表格区域 */
.table-section {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.table-container {
    overflow-x: auto;
    position: relative;
    border-radius: var(--border-radius-md);
}

/* 顶部滚动条容器 */
.top-scrollbar-container {
    overflow-x: auto;
    overflow-y: hidden;
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-light);
    border: 1px solid var(--border-light);
    position: relative;
}

.top-scrollbar-container::before {
    content: "← 拖动滚动条查看更多列 →";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    pointer-events: none;
    z-index: 1;
}

.top-scrollbar-content {
    height: 20px;
    /* 宽度将通过JavaScript动态设置，与表格宽度一致 */
}

/* 优化滚动条样式 */
.table-container::-webkit-scrollbar {
    height: 8px;
}

.top-scrollbar-container::-webkit-scrollbar {
    height: 12px;
}

.table-container::-webkit-scrollbar-track,
.top-scrollbar-container::-webkit-scrollbar-track {
    background: var(--bg-light);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb,
.top-scrollbar-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover,
.top-scrollbar-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* 顶部滚动条特殊样式 */
.top-scrollbar-container::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

/* 加载和空状态 */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    gap: var(--spacing-md);
}

.loading-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 分页区域 */
.pagination-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.page-size-select {
    padding: 2px var(--spacing-xs);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-white);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-md);
    }
    
    .stat-cards {
        grid-template-columns: 1fr;
    }
    
    .quick-filters {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .filter-group {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .filter-group label {
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
    }

    .filter-buttons {
        width: 100%;
        justify-content: flex-start;
    }

    .search-area {
        margin-left: 0;
        width: 100%;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .search-input-group {
        width: 100%;
    }
    
    .toolbar {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .toolbar-left,
    .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .search-area {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-input-group {
        max-width: none;
    }
    
    .pagination-section {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }
}

/* 高级搜索弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
}

.modal-close:hover {
    background-color: var(--bg-light);
    color: var(--text-color);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.search-form .form-row {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.search-form .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.search-form .form-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.search-form select,
.search-form input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

.search-form select:focus,
.search-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--bg-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--border-light);
}
