flowchart TD
    %% 外部角色
    Citizen([市民<br/>提出诉求])
    
    %% 市级层面
    Citizen --> Operator[市级12345中心<br/>话务员/分派员<br/>接听并创建工单]
    
    Operator --> Decision1{话务员/分派员<br/>一级分派决策}
    
    %% 场景A：即时办结
    Decision1 -->|政策咨询/信息查询| InstantA[话务员<br/>利用知识库直接答复<br/>即时办结]
    InstantA --> WaitRevisit[待回访状态]
    
    %% 场景B：市级职能部门处理
    Decision1 -->|市级垂直管理| CityDept[市级职能部门<br/>工单管理员]
    CityDept --> Decision2{市级部门管理员<br/>能否即时办结?}
    Decision2 -->|能| InstantB[市级部门管理员<br/>即时办结]
    InstantB --> WaitRevisit
    
    Decision2 -->|不能| CitySection[业务处室/科室<br/>分派员]
    CitySection --> Decision3{业务科室分派员<br/>能否即时办结?}
    Decision3 -->|能| InstantC[业务科室分派员<br/>即时办结]
    InstantC --> WaitRevisit
    
    Decision3 -->|不能| CityWorker[市级工作人员<br/>最终执行者]
    CityWorker --> CityExec[市级工作人员<br/>执行任务并办结]
    
    %% 场景C：区县级处理
    Decision1 -->|属地管理| DistrictCenter[区/县级12345分中心<br/>工单管理员/分派员]
    DistrictCenter --> Decision4{区级分中心管理员<br/>能否即时办结?}
    Decision4 -->|能| InstantD[区级分中心管理员<br/>即时办结]
    InstantD --> WaitRevisit
    
    Decision4 -->|不能| Decision5{区级分中心分派员<br/>路径选择}
    
    %% 属地路线
    Decision5 -->|属地路线| Street[街/镇总承办单位<br/>工单管理员/分派员]
    Street --> Decision6{街镇管理员<br/>能否即时办结?}
    Decision6 -->|能| InstantE[街镇管理员<br/>即时办结]
    InstantE --> WaitRevisit
    
    Decision6 -->|不能| Community[社区/村委会<br/>工单管理员/分派员]
    Community --> Decision7{社区管理员<br/>能否即时办结?}
    Decision7 -->|能| InstantF[社区管理员<br/>即时办结]
    InstantF --> WaitRevisit
    
    Decision7 -->|不能| GridAgent[网格员<br/>最终执行者]
    GridAgent --> GridExec[网格员<br/>现场处理并办结]
    
    %% 职能路线
    Decision5 -->|职能路线| DistrictDept[区级职能部门<br/>工单管理员]
    DistrictDept --> Decision8{区职能部门管理员<br/>能否即时办结?}
    Decision8 -->|能| InstantG[区职能部门管理员<br/>即时办结]
    InstantG --> WaitRevisit
    
    Decision8 -->|不能| DistrictSection[区级业务科室<br/>分派员]
    DistrictSection --> Decision9{区业务科室分派员<br/>能否即时办结?}
    Decision9 -->|能| InstantH[区业务科室分派员<br/>即时办结]
    InstantH --> WaitRevisit
    
    Decision9 -->|不能| DistrictWorker[区级工作人员<br/>最终执行者]
    DistrictWorker --> DistrictExec[区级工作人员<br/>执行任务并办结]
    
    %% 场景D：主协办协同
    Decision1 -->|复杂问题需协同| Coordinator[市级分派员<br/>构建主协办模式]
    Coordinator --> MainHandler[主办方<br/>总协调负责人]
    Coordinator --> Collaborator1[协办方1<br/>协办人]
    Coordinator --> Collaborator2[协办方2<br/>协办人]
    Coordinator --> CollaboratorN[协办方N<br/>协办人]
    
    MainHandler --> CollabWork[各方并行处理]
    Collaborator1 --> CollabWork
    Collaborator2 --> CollabWork
    CollaboratorN --> CollabWork
    
    CollabWork --> MainComplete[主办方<br/>汇总办结]
    
    %% 审核流程
    CityExec --> AuditFlow[启动审核流程]
    GridExec --> AuditFlow
    DistrictExec --> AuditFlow
    MainComplete --> AuditFlow
    
    AuditFlow --> Supervisor[各层级管理者/主管<br/>逐级审核]
    Supervisor --> Decision10{管理者<br/>审核结果}
    Decision10 -->|通过| Feedback[向市级平台反馈]
    Decision10 -->|退回| Return[退回重新处理]
    Return --> AuditFlow
    
    Feedback --> WaitRevisit
    
    %% 回访流程
    WaitRevisit --> RevisitAgent[回访员<br/>独立回访]
    RevisitAgent --> Decision11{市民满意度}
    Decision11 -->|满意| CloseCase[回访员<br/>关闭工单]
    Decision11 -->|不满意| Restart[回访员<br/>重启工单督办]
    Restart --> Operator
    
    CloseCase --> EndFlow([流程结束])
    
    %% 样式定义
    classDef citizen fill:#e3f2fd,stroke:#0277bd,stroke-width:3px
    classDef operator fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef manager fill:#fff8e1,stroke:#f57c00,stroke-width:2px
    classDef executor fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef instant fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    classDef audit fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef revisit fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef collab fill:#e1f5fe,stroke:#0288d1,stroke-width:2px
    
    class Citizen citizen
    class Operator,CityDept,CitySection,DistrictCenter,Street,Community,DistrictDept,DistrictSection operator
    class Supervisor manager
    class CityWorker,GridAgent,DistrictWorker executor
    class Decision1,Decision2,Decision3,Decision4,Decision5,Decision6,Decision7,Decision8,Decision9,Decision10,Decision11 decision
    class InstantA,InstantB,InstantC,InstantD,InstantE,InstantF,InstantG,InstantH instant
    class AuditFlow,Feedback,Return audit
    class WaitRevisit,RevisitAgent,CloseCase,Restart revisit
    class Coordinator,MainHandler,Collaborator1,Collaborator2,CollaboratorN,CollabWork,MainComplete collab