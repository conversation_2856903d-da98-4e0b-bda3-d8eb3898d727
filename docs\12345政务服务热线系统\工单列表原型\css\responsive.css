/* 响应式样式 */

/* 超大屏幕 (≥1600px) */
@media (min-width: 1600px) {
    .main-content {
        max-width: 1800px;
    }
    
    .stat-cards {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .ticket-table {
        font-size: var(--font-size-base);
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: var(--spacing-md) var(--spacing-lg);
    }
}

/* 大屏幕 (1200px - 1599px) */
@media (min-width: 1200px) and (max-width: 1599px) {
    .stat-cards {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* 隐藏部分非关键列 */
    .ticket-table th:nth-child(10),
    .ticket-table td:nth-child(10) {
        display: none;
    }
}

/* 中等屏幕 (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .main-content {
        padding: var(--spacing-md);
    }
    
    .stat-cards {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .filter-toolbar {
        padding: var(--spacing-md);
    }
    
    .quick-filters {
        gap: var(--spacing-sm);
    }
    
    .filter-group {
        min-width: 150px;
    }
    
    /* 隐藏更多列 */
    .ticket-table th:nth-child(n+9),
    .ticket-table td:nth-child(n+9) {
        display: none;
    }
    
    .toolbar {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .toolbar-left,
    .toolbar-right {
        gap: var(--spacing-xs);
    }
}

/* 小屏幕 (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .page-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .page-title {
        font-size: var(--font-size-lg);
    }
    
    .main-content {
        padding: var(--spacing-sm);
    }
    
    .stat-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }
    
    .stat-card {
        padding: var(--spacing-md);
    }
    
    .stat-icon {
        font-size: 24px;
    }
    
    .stat-number {
        font-size: var(--font-size-xl);
    }
    
    .filter-toolbar {
        padding: var(--spacing-sm);
    }
    
    .quick-filters {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .filter-group {
        flex-direction: row;
        align-items: center;
        min-width: auto;
    }
    
    .filter-group label {
        min-width: 60px;
        font-size: var(--font-size-xs);
    }
    
    .filter-buttons {
        gap: 2px;
    }
    
    .filter-btn {
        padding: 2px var(--spacing-xs);
        font-size: var(--font-size-xs);
    }
    
    .search-area {
        margin-top: var(--spacing-sm);
    }
    
    .search-input-group {
        max-width: none;
    }
    
    /* 隐藏更多列，只保留核心信息 */
    .ticket-table th:nth-child(n+7),
    .ticket-table td:nth-child(n+7) {
        display: none;
    }
    
    .ticket-table {
        font-size: var(--font-size-xs);
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .title-cell {
        max-width: 120px;
    }
    
    .toolbar {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }
    
    .toolbar-left,
    .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .pagination-section {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }
}

/* 超小屏幕 (≤767px) - 移动端 */
@media (max-width: 767px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    .header-right {
        align-self: flex-end;
    }
    
    .breadcrumb {
        font-size: var(--font-size-xs);
    }
    
    .main-content {
        padding: var(--spacing-xs);
    }
    
    /* 统计卡片改为单列 */
    .stat-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }
    
    .stat-card {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .stat-icon {
        font-size: 20px;
    }
    
    .stat-number {
        font-size: var(--font-size-lg);
    }
    
    .stat-label {
        font-size: var(--font-size-xs);
    }
    
    /* 筛选器移动端优化 */
    .filter-section {
        margin-bottom: var(--spacing-sm);
    }
    
    .filter-toolbar {
        padding: var(--spacing-sm);
    }
    
    .quick-filters {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .filter-group {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .filter-group label {
        min-width: auto;
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
    }
    
    .filter-buttons {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 2px;
    }
    
    .filter-btn {
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
        min-width: 60px;
        text-align: center;
    }
    
    .search-area {
        flex-direction: column;
        gap: var(--spacing-xs);
        margin-top: var(--spacing-sm);
    }
    
    .advanced-search-btn {
        align-self: flex-start;
    }
    
    /* 工具栏移动端优化 */
    .toolbar {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }
    
    .toolbar-left,
    .toolbar-right {
        justify-content: space-between;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }
    
    .btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        height: auto;
        min-height: 28px;
    }
    
    .btn-text {
        display: none;
    }
    
    .btn-small .btn-text {
        display: inline;
    }
    
    /* 表格移动端 - 改为卡片布局 */
    .table-container {
        display: none;
    }
    
    .mobile-card-view {
        display: block;
    }
    
    .ticket-card {
        background-color: var(--bg-white);
        border: 1px solid var(--border-light);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
        box-shadow: var(--shadow-sm);
    }
    
    .ticket-card.selected {
        border-color: var(--primary-color);
        background-color: var(--primary-light);
    }
    
    .ticket-card.urgent {
        border-left: 3px solid var(--error-color);
    }
    
    .ticket-card.timeout {
        border-left: 3px solid var(--warning-color);
    }
    
    .ticket-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-xs);
    }
    
    .ticket-card-number {
        font-family: 'Courier New', monospace;
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-bold);
        color: var(--primary-color);
    }
    
    .ticket-card-checkbox {
        margin-top: 2px;
    }
    
    .ticket-card-title {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--text-color);
        margin-bottom: var(--spacing-xs);
        line-height: var(--line-height-sm);
    }
    
    .ticket-card-meta {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-xs);
    }
    
    .ticket-card-tag {
        padding: 1px 4px;
        font-size: 10px;
        font-weight: var(--font-weight-medium);
        border-radius: 2px;
        white-space: nowrap;
    }
    
    .ticket-card-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
    }
    
    .ticket-card-time {
        font-family: 'Courier New', monospace;
    }
    
    .ticket-card-actions {
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-xs);
        margin-top: var(--spacing-xs);
        padding-top: var(--spacing-xs);
        border-top: 1px solid var(--border-light);
    }
    
    /* 批量操作移动端优化 */
    .batch-info {
        padding: var(--spacing-xs) var(--spacing-sm);
        margin-bottom: var(--spacing-xs);
    }
    
    .batch-content {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: flex-start;
    }
    
    .batch-actions {
        align-self: stretch;
        justify-content: space-between;
    }
    
    /* 分页移动端优化 */
    .pagination-section {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }
    
    .pagination-info {
        font-size: var(--font-size-xs);
        text-align: center;
    }
    
    .pagination-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* 超小屏幕优化 (≤480px) */
@media (max-width: 480px) {
    .page-title {
        font-size: var(--font-size-base);
    }
    
    .stat-card {
        padding: var(--spacing-xs);
    }
    
    .stat-number {
        font-size: var(--font-size-base);
    }
    
    .filter-btn {
        min-width: 50px;
        padding: 2px var(--spacing-xs);
    }
    
    .btn {
        min-height: 24px;
        padding: 2px var(--spacing-xs);
    }
    
    .ticket-card {
        padding: var(--spacing-xs);
    }
    
    .ticket-card-title {
        font-size: var(--font-size-xs);
    }
}

/* 打印样式 */
@media print {
    .page-header,
    .filter-section,
    .toolbar,
    .batch-info,
    .pagination-section {
        display: none;
    }
    
    .main-content {
        padding: 0;
        max-width: none;
    }
    
    .table-section {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .ticket-table {
        font-size: 10px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 4px;
        border: 1px solid #000;
    }
    
    .actions-col {
        display: none;
    }
    
    .checkbox-col {
        display: none;
    }
}
