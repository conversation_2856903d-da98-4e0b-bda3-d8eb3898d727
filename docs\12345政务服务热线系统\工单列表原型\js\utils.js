/**
 * 工具函数库
 */

// DOM操作工具
const DOM = {
    /**
     * 查询元素
     */
    $(selector, context = document) {
        return context.querySelector(selector);
    },
    
    /**
     * 查询所有元素
     */
    $$(selector, context = document) {
        return Array.from(context.querySelectorAll(selector));
    },
    
    /**
     * 创建元素
     */
    create(tag, attributes = {}, children = []) {
        const element = document.createElement(tag);
        
        // 设置属性
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'innerHTML') {
                element.innerHTML = value;
            } else if (key === 'textContent') {
                element.textContent = value;
            } else if (key.startsWith('data-')) {
                element.setAttribute(key, value);
            } else {
                element[key] = value;
            }
        });
        
        // 添加子元素
        children.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else if (child instanceof Node) {
                element.appendChild(child);
            }
        });
        
        return element;
    },
    
    /**
     * 添加类名
     */
    addClass(element, className) {
        if (element && className) {
            element.classList.add(className);
        }
    },
    
    /**
     * 移除类名
     */
    removeClass(element, className) {
        if (element && className) {
            element.classList.remove(className);
        }
    },
    
    /**
     * 切换类名
     */
    toggleClass(element, className) {
        if (element && className) {
            element.classList.toggle(className);
        }
    },
    
    /**
     * 检查是否有类名
     */
    hasClass(element, className) {
        return element && className && element.classList.contains(className);
    },
    
    /**
     * 显示元素
     */
    show(element, display = 'block') {
        if (element) {
            element.style.display = display;
        }
    },
    
    /**
     * 隐藏元素
     */
    hide(element) {
        if (element) {
            element.style.display = 'none';
        }
    },
    
    /**
     * 切换显示/隐藏
     */
    toggle(element, display = 'block') {
        if (element) {
            if (element.style.display === 'none' || !element.style.display) {
                this.show(element, display);
            } else {
                this.hide(element);
            }
        }
    },
    
    /**
     * 获取元素位置
     */
    getPosition(element) {
        if (!element) return { top: 0, left: 0 };
        const rect = element.getBoundingClientRect();
        return {
            top: rect.top + window.pageYOffset,
            left: rect.left + window.pageXOffset,
            width: rect.width,
            height: rect.height
        };
    },
    
    /**
     * 平滑滚动到元素
     */
    scrollTo(element, offset = 0) {
        if (element) {
            const top = this.getPosition(element).top - offset;
            window.scrollTo({
                top: top,
                behavior: 'smooth'
            });
        }
    }
};

// 事件处理工具
const EventHandler = {
    /**
     * 添加事件监听器
     */
    on(element, event, handler, options = false) {
        if (element && event && handler) {
            element.addEventListener(event, handler, options);
        }
    },
    
    /**
     * 移除事件监听器
     */
    off(element, event, handler, options = false) {
        if (element && event && handler) {
            element.removeEventListener(event, handler, options);
        }
    },
    
    /**
     * 触发事件
     */
    trigger(element, event, data = {}) {
        if (element && event) {
            const customEvent = new CustomEvent(event, { detail: data });
            element.dispatchEvent(customEvent);
        }
    },
    
    /**
     * 事件委托
     */
    delegate(container, selector, event, handler) {
        if (container && selector && event && handler) {
            container.addEventListener(event, (e) => {
                const target = e.target.closest(selector);
                if (target) {
                    handler.call(target, e);
                }
            });
        }
    },
    
    /**
     * 一次性事件监听器
     */
    once(element, event, handler) {
        if (element && event && handler) {
            const onceHandler = (e) => {
                handler(e);
                element.removeEventListener(event, onceHandler);
            };
            element.addEventListener(event, onceHandler);
        }
    }
};

// 数据处理工具
const DataProcessor = {
    /**
     * 数组去重
     */
    unique(array, key = null) {
        if (!Array.isArray(array)) return [];
        
        if (key) {
            const seen = new Set();
            return array.filter(item => {
                const value = item[key];
                if (seen.has(value)) {
                    return false;
                }
                seen.add(value);
                return true;
            });
        }
        
        return [...new Set(array)];
    },
    
    /**
     * 数组分组
     */
    groupBy(array, key) {
        if (!Array.isArray(array)) return {};
        
        return array.reduce((groups, item) => {
            const group = typeof key === 'function' ? key(item) : item[key];
            groups[group] = groups[group] || [];
            groups[group].push(item);
            return groups;
        }, {});
    },
    
    /**
     * 数组排序
     */
    sortBy(array, key, order = 'asc') {
        if (!Array.isArray(array)) return [];
        
        return [...array].sort((a, b) => {
            let aVal = typeof key === 'function' ? key(a) : a[key];
            let bVal = typeof key === 'function' ? key(b) : b[key];
            
            // 处理数字
            if (typeof aVal === 'string' && !isNaN(aVal)) aVal = Number(aVal);
            if (typeof bVal === 'string' && !isNaN(bVal)) bVal = Number(bVal);
            
            if (aVal < bVal) return order === 'asc' ? -1 : 1;
            if (aVal > bVal) return order === 'asc' ? 1 : -1;
            return 0;
        });
    },
    
    /**
     * 数组过滤
     */
    filterBy(array, filters) {
        if (!Array.isArray(array)) return [];
        
        return array.filter(item => {
            return Object.entries(filters).every(([key, value]) => {
                if (value === '' || value === null || value === undefined) return true;
                
                const itemValue = item[key];
                
                if (Array.isArray(value)) {
                    return value.includes(itemValue);
                }
                
                if (typeof value === 'string' && typeof itemValue === 'string') {
                    return itemValue.toLowerCase().includes(value.toLowerCase());
                }
                
                return itemValue === value;
            });
        });
    },
    
    /**
     * 分页处理
     */
    paginate(array, page = 1, pageSize = 20) {
        if (!Array.isArray(array)) return { data: [], total: 0, page: 1, pageSize: 20 };
        
        const total = array.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const data = array.slice(start, end);
        
        return {
            data,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize)
        };
    }
};

// 表单处理工具
const FormHandler = {
    /**
     * 获取表单数据
     */
    getFormData(form) {
        if (!form) return {};
        
        const formData = new FormData(form);
        const data = {};
        
        for (const [key, value] of formData.entries()) {
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    },
    
    /**
     * 设置表单数据
     */
    setFormData(form, data) {
        if (!form || !data) return;
        
        Object.entries(data).forEach(([key, value]) => {
            const element = form.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'checkbox' || element.type === 'radio') {
                    element.checked = element.value === value;
                } else {
                    element.value = value;
                }
            }
        });
    },
    
    /**
     * 重置表单
     */
    resetForm(form) {
        if (form) {
            form.reset();
        }
    },
    
    /**
     * 验证表单
     */
    validateForm(form, rules = {}) {
        if (!form) return { valid: false, errors: {} };
        
        const data = this.getFormData(form);
        const errors = {};
        
        Object.entries(rules).forEach(([field, rule]) => {
            const value = data[field];
            
            if (rule.required && (!value || value.trim() === '')) {
                errors[field] = `${rule.label || field}是必填项`;
                return;
            }
            
            if (value && rule.minLength && value.length < rule.minLength) {
                errors[field] = `${rule.label || field}至少需要${rule.minLength}个字符`;
                return;
            }
            
            if (value && rule.maxLength && value.length > rule.maxLength) {
                errors[field] = `${rule.label || field}不能超过${rule.maxLength}个字符`;
                return;
            }
            
            if (value && rule.pattern && !rule.pattern.test(value)) {
                errors[field] = rule.message || `${rule.label || field}格式不正确`;
                return;
            }
        });
        
        return {
            valid: Object.keys(errors).length === 0,
            errors
        };
    }
};

// 消息提示工具
const Message = {
    /**
     * 显示消息
     */
    show(text, type = 'info', duration = 3000) {
        const message = DOM.create('div', {
            className: `message message-${type}`,
            innerHTML: `
                <span class="message-icon">${this.getIcon(type)}</span>
                <span class="message-text">${text}</span>
                <button class="message-close">&times;</button>
            `
        });
        
        document.body.appendChild(message);
        
        // 添加关闭事件
        const closeBtn = message.querySelector('.message-close');
        EventHandler.on(closeBtn, 'click', () => this.hide(message));
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(() => this.hide(message), duration);
        }
        
        // 显示动画
        setTimeout(() => DOM.addClass(message, 'show'), 10);
        
        return message;
    },
    
    /**
     * 隐藏消息
     */
    hide(message) {
        if (message && message.parentNode) {
            DOM.removeClass(message, 'show');
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 300);
        }
    },
    
    /**
     * 获取图标
     */
    getIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    },
    
    /**
     * 成功消息
     */
    success(text, duration = 3000) {
        return this.show(text, 'success', duration);
    },
    
    /**
     * 错误消息
     */
    error(text, duration = 5000) {
        return this.show(text, 'error', duration);
    },
    
    /**
     * 警告消息
     */
    warning(text, duration = 4000) {
        return this.show(text, 'warning', duration);
    },
    
    /**
     * 信息消息
     */
    info(text, duration = 3000) {
        return this.show(text, 'info', duration);
    }
};

// 确认对话框工具
const Confirm = {
    /**
     * 显示确认对话框
     */
    show(options = {}) {
        const {
            title = '确认',
            message = '确定要执行此操作吗？',
            confirmText = '确定',
            cancelText = '取消',
            type = 'warning'
        } = options;
        
        return new Promise((resolve) => {
            const modal = DOM.create('div', {
                className: 'confirm-modal',
                innerHTML: `
                    <div class="confirm-content">
                        <div class="confirm-header">
                            <span class="confirm-icon">${this.getIcon(type)}</span>
                            <h3 class="confirm-title">${title}</h3>
                        </div>
                        <div class="confirm-body">
                            <p class="confirm-message">${message}</p>
                        </div>
                        <div class="confirm-footer">
                            <button class="btn btn-outline confirm-cancel">${cancelText}</button>
                            <button class="btn btn-primary confirm-ok">${confirmText}</button>
                        </div>
                    </div>
                `
            });
            
            document.body.appendChild(modal);
            
            const okBtn = modal.querySelector('.confirm-ok');
            const cancelBtn = modal.querySelector('.confirm-cancel');
            
            const cleanup = () => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            };
            
            EventHandler.on(okBtn, 'click', () => {
                cleanup();
                resolve(true);
            });
            
            EventHandler.on(cancelBtn, 'click', () => {
                cleanup();
                resolve(false);
            });
            
            EventHandler.on(modal, 'click', (e) => {
                if (e.target === modal) {
                    cleanup();
                    resolve(false);
                }
            });
            
            // 显示动画
            setTimeout(() => DOM.addClass(modal, 'show'), 10);
        });
    },
    
    /**
     * 获取图标
     */
    getIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.warning;
    }
};

// 加载状态工具
const Loading = {
    /**
     * 显示加载状态
     */
    show(target = document.body, text = '加载中...') {
        const loading = DOM.create('div', {
            className: 'loading-overlay',
            innerHTML: `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">${text}</div>
                </div>
            `
        });
        
        if (target === document.body) {
            loading.style.position = 'fixed';
        } else {
            loading.style.position = 'absolute';
            target.style.position = 'relative';
        }
        
        target.appendChild(loading);
        setTimeout(() => DOM.addClass(loading, 'show'), 10);
        
        return loading;
    },
    
    /**
     * 隐藏加载状态
     */
    hide(loading) {
        if (loading && loading.parentNode) {
            DOM.removeClass(loading, 'show');
            setTimeout(() => {
                if (loading.parentNode) {
                    loading.parentNode.removeChild(loading);
                }
            }, 300);
        }
    }
};

// 导出工具（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DOM, EventHandler, DataProcessor, FormHandler, Message, Confirm, Loading };
}
