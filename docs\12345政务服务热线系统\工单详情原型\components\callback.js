/**
 * 12345政务服务热线系统 - 回访和满意度组件
 * @description 回访记录和满意度评价管理组件
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 回访和满意度组件
 */
const CallbackComponent = {
    /**
     * 回访信息容器
     */
    container: null,
    
    /**
     * 回访数据
     */
    callbackData: null,
    
    /**
     * 初始化回访组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'callbackInfo') {
        console.log('初始化回访和满意度组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到回访信息容器元素:', containerId);
            return;
        }
        
        // 加载回访数据
        this.loadCallbackData();
        
        // 渲染回访信息
        this.render();
    },
    
    /**
     * 加载回访数据
     */
    loadCallbackData() {
        // 使用统一的Mock数据
        if (window.MockData && window.MockData.callback) {
            const callback = window.MockData.callback;
            this.callbackData = {
                isRequired: true,
                status: callback.status,
                plan: {
                    scheduledDate: callback.followUp.scheduledTime,
                    callbackType: 'phone',
                    assignedStaff: callback.caller,
                    department: '市级话务中心',
                    priority: 'normal',
                    notes: '工单办结后3个工作日内完成回访'
                },
                records: [
                    {
                        id: 'cb001',
                        date: callback.callTime,
                        type: 'phone',
                        staff: callback.caller,
                        duration: callback.duration,
                        status: callback.status,
                        contactResult: 'successful',
                        customerResponse: 'satisfied',
                        notes: callback.feedback,
                        satisfaction: {
                            overall: callback.satisfaction.overall,
                            aspects: {
                                responseSpeed: callback.satisfaction.processing,
                                serviceAttitude: callback.satisfaction.attitude,
                                processEfficiency: callback.satisfaction.efficiency,
                                solutionQuality: callback.satisfaction.result
                            },
                            feedback: callback.feedback,
                            suggestions: callback.suggestions
                    }
                }
            ],
            statistics: {
                totalCallbacks: 1,
                successfulContacts: 1,
                averageSatisfaction: 5.0,
                satisfactionDistribution: {
                    verySatisfied: 1,
                    satisfied: 0,
                    neutral: 0,
                    dissatisfied: 0,
                    veryDissatisfied: 0
                }
            },
            improvements: [
                {
                    category: 'service_quality',
                    suggestion: '继续保持高效的处理速度',
                    priority: 'medium',
                    status: 'noted'
                }
            ]
        };
        }
    },
    
    /**
     * 渲染回访信息
     */
    render() {
        if (!this.container || !this.callbackData) return;
        
        // 清空容器
        this.container.innerHTML = '';
        
        if (!this.callbackData.isRequired) {
            const notRequiredElement = document.createElement('div');
            notRequiredElement.className = 'callback-not-required';
            notRequiredElement.innerHTML = `
                <div class="not-required-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="not-required-text">当前工单状态无需回访</div>
            `;
            this.container.appendChild(notRequiredElement);
            return;
        }
        
        // 创建回访计划区域
        const planSection = this.createPlanSection();
        this.container.appendChild(planSection);
        
        // 创建回访记录区域
        if (this.callbackData.records.length > 0) {
            const recordsSection = this.createRecordsSection();
            this.container.appendChild(recordsSection);
        }
        
        // 创建满意度统计区域
        const statisticsSection = this.createStatisticsSection();
        this.container.appendChild(statisticsSection);
        
        // 创建改进建议区域
        if (this.callbackData.improvements.length > 0) {
            const improvementsSection = this.createImprovementsSection();
            this.container.appendChild(improvementsSection);
        }
    },
    
    /**
     * 创建回访计划区域
     * @returns {HTMLElement} 回访计划元素
     */
    createPlanSection() {
        const section = document.createElement('div');
        section.className = 'callback-plan';
        
        const plan = this.callbackData.plan;
        const statusClass = this.callbackData.status === 'completed' ? 'completed' : 'pending';
        
        section.innerHTML = `
            <div class="section-header">
                <h4><i class="fas fa-calendar-check"></i> 回访计划</h4>
                <span class="callback-status ${statusClass}">
                    ${this.callbackData.status === 'completed' ? '已完成' : '待执行'}
                </span>
            </div>
            <div class="plan-content">
                <div class="plan-grid">
                    <div class="plan-item">
                        <span class="plan-label">计划时间</span>
                        <span class="plan-value">${plan.scheduledDate}</span>
                    </div>
                    <div class="plan-item">
                        <span class="plan-label">回访方式</span>
                        <span class="plan-value">
                            <i class="${this.getCallbackTypeIcon(plan.callbackType)}"></i>
                            ${this.getCallbackTypeText(plan.callbackType)}
                        </span>
                    </div>
                    <div class="plan-item">
                        <span class="plan-label">负责人员</span>
                        <span class="plan-value">${plan.assignedStaff}</span>
                    </div>
                    <div class="plan-item">
                        <span class="plan-label">所属部门</span>
                        <span class="plan-value">${plan.department}</span>
                    </div>
                    <div class="plan-item">
                        <span class="plan-label">优先级</span>
                        <span class="plan-value priority-${plan.priority}">
                            ${this.getPriorityText(plan.priority)}
                        </span>
                    </div>
                    <div class="plan-item full-width">
                        <span class="plan-label">备注说明</span>
                        <span class="plan-value">${plan.notes}</span>
                    </div>
                </div>
                <div class="plan-actions">
                    ${this.callbackData.status === 'pending' ? `
                        <button class="btn btn-primary" onclick="CallbackComponent.executeCallback()">
                            <i class="fas fa-phone"></i> 执行回访
                        </button>
                        <button class="btn btn-secondary" onclick="CallbackComponent.rescheduleCallback()">
                            <i class="fas fa-calendar-alt"></i> 重新安排
                        </button>
                    ` : `
                        <button class="btn btn-outline" onclick="CallbackComponent.viewCallbackDetails()">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                    `}
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建回访记录区域
     * @returns {HTMLElement} 回访记录元素
     */
    createRecordsSection() {
        const section = document.createElement('div');
        section.className = 'callback-records';
        
        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-history"></i> 回访记录</h4>
            <span class="records-count">${this.callbackData.records.length} 次回访</span>
        `;
        
        const recordsList = document.createElement('div');
        recordsList.className = 'records-list';
        
        this.callbackData.records.forEach(record => {
            const recordElement = this.createRecordElement(record);
            recordsList.appendChild(recordElement);
        });
        
        section.appendChild(header);
        section.appendChild(recordsList);
        
        return section;
    },
    
    /**
     * 创建回访记录元素
     * @param {Object} record - 回访记录数据
     * @returns {HTMLElement} 回访记录元素
     */
    createRecordElement(record) {
        const element = document.createElement('div');
        element.className = 'record-item';
        
        element.innerHTML = `
            <div class="record-header">
                <div class="record-meta">
                    <span class="record-date">${record.date}</span>
                    <span class="record-type">
                        <i class="${this.getCallbackTypeIcon(record.type)}"></i>
                        ${this.getCallbackTypeText(record.type)}
                    </span>
                    <span class="record-duration">${record.duration}</span>
                </div>
                <div class="record-status ${record.status}">
                    ${record.status === 'completed' ? '已完成' : '进行中'}
                </div>
            </div>
            <div class="record-content">
                <div class="record-basic">
                    <div class="record-staff">
                        <i class="fas fa-user"></i> ${record.staff}
                    </div>
                    <div class="record-result">
                        <span class="result-label">联系结果：</span>
                        <span class="result-value ${record.contactResult}">
                            ${this.getContactResultText(record.contactResult)}
                        </span>
                    </div>
                    <div class="record-response">
                        <span class="response-label">客户反馈：</span>
                        <span class="response-value ${record.customerResponse}">
                            ${this.getCustomerResponseText(record.customerResponse)}
                        </span>
                    </div>
                </div>
                ${record.notes ? `
                    <div class="record-notes">
                        <span class="notes-label">回访备注：</span>
                        <span class="notes-content">${record.notes}</span>
                    </div>
                ` : ''}
                ${record.satisfaction ? this.createSatisfactionElement(record.satisfaction) : ''}
            </div>
        `;
        
        return element;
    },

    /**
     * 创建满意度评价元素
     * @param {Object} satisfaction - 满意度数据
     * @returns {string} 满意度HTML
     */
    createSatisfactionElement(satisfaction) {
        return `
            <div class="satisfaction-section">
                <div class="satisfaction-header">
                    <h5><i class="fas fa-star"></i> 满意度评价</h5>
                    <div class="overall-rating">
                        <span class="rating-score">${satisfaction.overall}</span>
                        <div class="rating-stars">
                            ${this.generateStarRating(satisfaction.overall)}
                        </div>
                    </div>
                </div>
                <div class="satisfaction-aspects">
                    <div class="aspects-grid">
                        <div class="aspect-item">
                            <span class="aspect-name">响应速度</span>
                            <div class="aspect-rating">
                                <span class="aspect-score">${satisfaction.aspects.responseSpeed}</span>
                                <div class="aspect-stars">${this.generateStarRating(satisfaction.aspects.responseSpeed)}</div>
                            </div>
                        </div>
                        <div class="aspect-item">
                            <span class="aspect-name">服务态度</span>
                            <div class="aspect-rating">
                                <span class="aspect-score">${satisfaction.aspects.serviceAttitude}</span>
                                <div class="aspect-stars">${this.generateStarRating(satisfaction.aspects.serviceAttitude)}</div>
                            </div>
                        </div>
                        <div class="aspect-item">
                            <span class="aspect-name">处理效率</span>
                            <div class="aspect-rating">
                                <span class="aspect-score">${satisfaction.aspects.processEfficiency}</span>
                                <div class="aspect-stars">${this.generateStarRating(satisfaction.aspects.processEfficiency)}</div>
                            </div>
                        </div>
                        <div class="aspect-item">
                            <span class="aspect-name">解决质量</span>
                            <div class="aspect-rating">
                                <span class="aspect-score">${satisfaction.aspects.solutionQuality}</span>
                                <div class="aspect-stars">${this.generateStarRating(satisfaction.aspects.solutionQuality)}</div>
                            </div>
                        </div>
                    </div>
                </div>
                ${satisfaction.feedback ? `
                    <div class="satisfaction-feedback">
                        <div class="feedback-label">客户反馈：</div>
                        <div class="feedback-content">${satisfaction.feedback}</div>
                    </div>
                ` : ''}
                ${satisfaction.suggestions ? `
                    <div class="satisfaction-suggestions">
                        <div class="suggestions-label">改进建议：</div>
                        <div class="suggestions-content">${satisfaction.suggestions}</div>
                    </div>
                ` : ''}
            </div>
        `;
    },

    /**
     * 创建满意度统计区域
     * @returns {HTMLElement} 满意度统计元素
     */
    createStatisticsSection() {
        const section = document.createElement('div');
        section.className = 'callback-statistics';

        const stats = this.callbackData.statistics;

        section.innerHTML = `
            <div class="section-header">
                <h4><i class="fas fa-chart-pie"></i> 满意度统计</h4>
            </div>
            <div class="statistics-content">
                <div class="stats-overview">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value">${stats.totalCallbacks}</div>
                            <div class="stat-label">回访次数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value">${stats.successfulContacts}</div>
                            <div class="stat-label">成功联系</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value">${stats.averageSatisfaction}</div>
                            <div class="stat-label">平均满意度</div>
                        </div>
                    </div>
                </div>
                <div class="satisfaction-distribution">
                    <h5>满意度分布</h5>
                    <div class="distribution-chart">
                        <div class="distribution-item">
                            <span class="distribution-label">非常满意</span>
                            <div class="distribution-bar">
                                <div class="distribution-fill very-satisfied" style="width: ${(stats.satisfactionDistribution.verySatisfied / stats.totalCallbacks) * 100}%"></div>
                            </div>
                            <span class="distribution-count">${stats.satisfactionDistribution.verySatisfied}</span>
                        </div>
                        <div class="distribution-item">
                            <span class="distribution-label">满意</span>
                            <div class="distribution-bar">
                                <div class="distribution-fill satisfied" style="width: ${(stats.satisfactionDistribution.satisfied / stats.totalCallbacks) * 100}%"></div>
                            </div>
                            <span class="distribution-count">${stats.satisfactionDistribution.satisfied}</span>
                        </div>
                        <div class="distribution-item">
                            <span class="distribution-label">一般</span>
                            <div class="distribution-bar">
                                <div class="distribution-fill neutral" style="width: ${(stats.satisfactionDistribution.neutral / stats.totalCallbacks) * 100}%"></div>
                            </div>
                            <span class="distribution-count">${stats.satisfactionDistribution.neutral}</span>
                        </div>
                        <div class="distribution-item">
                            <span class="distribution-label">不满意</span>
                            <div class="distribution-bar">
                                <div class="distribution-fill dissatisfied" style="width: ${(stats.satisfactionDistribution.dissatisfied / stats.totalCallbacks) * 100}%"></div>
                            </div>
                            <span class="distribution-count">${stats.satisfactionDistribution.dissatisfied}</span>
                        </div>
                        <div class="distribution-item">
                            <span class="distribution-label">非常不满意</span>
                            <div class="distribution-bar">
                                <div class="distribution-fill very-dissatisfied" style="width: ${(stats.satisfactionDistribution.veryDissatisfied / stats.totalCallbacks) * 100}%"></div>
                            </div>
                            <span class="distribution-count">${stats.satisfactionDistribution.veryDissatisfied}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return section;
    },

    /**
     * 创建改进建议区域
     * @returns {HTMLElement} 改进建议元素
     */
    createImprovementsSection() {
        const section = document.createElement('div');
        section.className = 'callback-improvements';

        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-lightbulb"></i> 改进建议</h4>
        `;

        const improvementsList = document.createElement('div');
        improvementsList.className = 'improvements-list';

        this.callbackData.improvements.forEach(improvement => {
            const improvementElement = document.createElement('div');
            improvementElement.className = 'improvement-item';

            const categoryInfo = this.getImprovementCategoryInfo(improvement.category);

            improvementElement.innerHTML = `
                <div class="improvement-header">
                    <div class="improvement-category">
                        <i class="${categoryInfo.icon}"></i>
                        <span>${categoryInfo.label}</span>
                    </div>
                    <div class="improvement-priority priority-${improvement.priority}">
                        ${this.getPriorityText(improvement.priority)}
                    </div>
                    <div class="improvement-status status-${improvement.status}">
                        ${this.getImprovementStatusText(improvement.status)}
                    </div>
                </div>
                <div class="improvement-content">
                    ${improvement.suggestion}
                </div>
            `;

            improvementsList.appendChild(improvementElement);
        });

        section.appendChild(header);
        section.appendChild(improvementsList);

        return section;
    },

    /**
     * 生成星级评分
     * @param {number} rating - 评分值
     * @returns {string} 星级HTML
     */
    generateStarRating(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHtml = '';

        // 实心星
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star"></i>';
        }

        // 半星
        if (hasHalfStar) {
            starsHtml += '<i class="fas fa-star-half-alt"></i>';
        }

        // 空心星
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star"></i>';
        }

        return starsHtml;
    },

    /**
     * 获取回访方式图标
     * @param {string} type - 回访方式
     * @returns {string} 图标类名
     */
    getCallbackTypeIcon(type) {
        const iconMap = {
            phone: 'fas fa-phone',
            sms: 'fas fa-sms',
            email: 'fas fa-envelope',
            visit: 'fas fa-home',
            online: 'fas fa-globe'
        };
        return iconMap[type] || 'fas fa-phone';
    },

    /**
     * 获取回访方式文本
     * @param {string} type - 回访方式
     * @returns {string} 方式文本
     */
    getCallbackTypeText(type) {
        const textMap = {
            phone: '电话回访',
            sms: '短信回访',
            email: '邮件回访',
            visit: '上门回访',
            online: '在线回访'
        };
        return textMap[type] || '电话回访';
    },

    /**
     * 获取优先级文本
     * @param {string} priority - 优先级
     * @returns {string} 优先级文本
     */
    getPriorityText(priority) {
        const textMap = {
            high: '高',
            normal: '普通',
            low: '低'
        };
        return textMap[priority] || '普通';
    },

    /**
     * 获取联系结果文本
     * @param {string} result - 联系结果
     * @returns {string} 结果文本
     */
    getContactResultText(result) {
        const textMap = {
            successful: '联系成功',
            failed: '联系失败',
            busy: '占线',
            no_answer: '无人接听',
            wrong_number: '号码错误'
        };
        return textMap[result] || '联系成功';
    },

    /**
     * 获取客户反馈文本
     * @param {string} response - 客户反馈
     * @returns {string} 反馈文本
     */
    getCustomerResponseText(response) {
        const textMap = {
            satisfied: '满意',
            dissatisfied: '不满意',
            neutral: '一般',
            no_comment: '无评价'
        };
        return textMap[response] || '满意';
    },

    /**
     * 获取改进建议类别信息
     * @param {string} category - 类别
     * @returns {Object} 类别信息
     */
    getImprovementCategoryInfo(category) {
        const categoryMap = {
            service_quality: { label: '服务质量', icon: 'fas fa-star' },
            response_time: { label: '响应时间', icon: 'fas fa-clock' },
            communication: { label: '沟通交流', icon: 'fas fa-comments' },
            process_efficiency: { label: '流程效率', icon: 'fas fa-cogs' },
            staff_training: { label: '人员培训', icon: 'fas fa-graduation-cap' },
            system_improvement: { label: '系统改进', icon: 'fas fa-laptop' }
        };
        return categoryMap[category] || { label: '其他', icon: 'fas fa-lightbulb' };
    },

    /**
     * 获取改进建议状态文本
     * @param {string} status - 状态
     * @returns {string} 状态文本
     */
    getImprovementStatusText(status) {
        const textMap = {
            noted: '已记录',
            reviewing: '审核中',
            approved: '已批准',
            implemented: '已实施',
            rejected: '已拒绝'
        };
        return textMap[status] || '已记录';
    },

    /**
     * 执行回访
     */
    executeCallback() {
        Utils.showToast('正在启动回访流程...', 'info');

        // 显示回访执行对话框
        this.showCallbackDialog();
    },

    /**
     * 显示回访执行对话框
     */
    showCallbackDialog() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3>执行回访</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="callbackForm">
                        <div class="form-group">
                            <label for="callbackType">回访方式</label>
                            <select id="callbackType" class="form-control" required>
                                <option value="phone">电话回访</option>
                                <option value="sms">短信回访</option>
                                <option value="email">邮件回访</option>
                                <option value="visit">上门回访</option>
                                <option value="online">在线回访</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contactResult">联系结果</label>
                            <select id="contactResult" class="form-control" required>
                                <option value="successful">联系成功</option>
                                <option value="failed">联系失败</option>
                                <option value="busy">占线</option>
                                <option value="no_answer">无人接听</option>
                                <option value="wrong_number">号码错误</option>
                            </select>
                        </div>
                        <div class="form-group" id="satisfactionGroup">
                            <label>满意度评价</label>
                            <div class="satisfaction-form">
                                <div class="overall-satisfaction">
                                    <label>总体满意度</label>
                                    <div class="rating-input">
                                        <input type="range" id="overallRating" min="1" max="5" value="5" step="0.1">
                                        <span id="overallRatingValue">5.0</span>
                                    </div>
                                </div>
                                <div class="aspect-ratings">
                                    <div class="aspect-rating-item">
                                        <label>响应速度</label>
                                        <select class="form-control aspect-select" data-aspect="responseSpeed">
                                            <option value="5">5 - 非常快</option>
                                            <option value="4">4 - 较快</option>
                                            <option value="3">3 - 一般</option>
                                            <option value="2">2 - 较慢</option>
                                            <option value="1">1 - 很慢</option>
                                        </select>
                                    </div>
                                    <div class="aspect-rating-item">
                                        <label>服务态度</label>
                                        <select class="form-control aspect-select" data-aspect="serviceAttitude">
                                            <option value="5">5 - 非常好</option>
                                            <option value="4">4 - 较好</option>
                                            <option value="3">3 - 一般</option>
                                            <option value="2">2 - 较差</option>
                                            <option value="1">1 - 很差</option>
                                        </select>
                                    </div>
                                    <div class="aspect-rating-item">
                                        <label>处理效率</label>
                                        <select class="form-control aspect-select" data-aspect="processEfficiency">
                                            <option value="5">5 - 非常高</option>
                                            <option value="4">4 - 较高</option>
                                            <option value="3">3 - 一般</option>
                                            <option value="2">2 - 较低</option>
                                            <option value="1">1 - 很低</option>
                                        </select>
                                    </div>
                                    <div class="aspect-rating-item">
                                        <label>解决质量</label>
                                        <select class="form-control aspect-select" data-aspect="solutionQuality">
                                            <option value="5">5 - 非常好</option>
                                            <option value="4">4 - 较好</option>
                                            <option value="3">3 - 一般</option>
                                            <option value="2">2 - 较差</option>
                                            <option value="1">1 - 很差</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="callbackFeedback">客户反馈</label>
                            <textarea id="callbackFeedback" class="form-control" rows="4" placeholder="请输入客户的详细反馈..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="callbackSuggestions">改进建议</label>
                            <textarea id="callbackSuggestions" class="form-control" rows="3" placeholder="请输入客户提出的改进建议..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="callbackNotes">回访备注</label>
                            <textarea id="callbackNotes" class="form-control" rows="3" placeholder="请输入回访过程中的备注信息..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="CallbackComponent.saveCallback()">保存回访记录</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        this.bindCallbackFormEvents(modal);

        // 显示模态框
        setTimeout(() => {
            modal.style.display = 'flex';
        }, 100);
    },

    /**
     * 绑定回访表单事件
     * @param {HTMLElement} modal - 模态框元素
     */
    bindCallbackFormEvents(modal) {
        const overallRating = modal.querySelector('#overallRating');
        const overallRatingValue = modal.querySelector('#overallRatingValue');
        const contactResult = modal.querySelector('#contactResult');
        const satisfactionGroup = modal.querySelector('#satisfactionGroup');

        // 总体满意度滑块事件
        overallRating.addEventListener('input', (e) => {
            overallRatingValue.textContent = parseFloat(e.target.value).toFixed(1);
        });

        // 联系结果变化事件
        contactResult.addEventListener('change', (e) => {
            if (e.target.value === 'successful') {
                satisfactionGroup.style.display = 'block';
            } else {
                satisfactionGroup.style.display = 'none';
            }
        });
    },

    /**
     * 保存回访记录
     */
    saveCallback() {
        const form = document.getElementById('callbackForm');
        const formData = new FormData(form);

        const callbackType = document.getElementById('callbackType').value;
        const contactResult = document.getElementById('contactResult').value;
        const feedback = document.getElementById('callbackFeedback').value;
        const suggestions = document.getElementById('callbackSuggestions').value;
        const notes = document.getElementById('callbackNotes').value;

        // 构建回访记录
        const newRecord = {
            id: 'cb' + Date.now(),
            date: Utils.formatDateTime(new Date()),
            type: callbackType,
            staff: `${AppState.currentUser.name} (工号: ${AppState.currentUser.id})`,
            duration: '5分钟', // 实际应用中可以计算实际时长
            status: 'completed',
            contactResult: contactResult,
            customerResponse: contactResult === 'successful' ? 'satisfied' : 'no_comment',
            notes: notes
        };

        // 如果联系成功，添加满意度评价
        if (contactResult === 'successful') {
            const overallRating = parseFloat(document.getElementById('overallRating').value);
            const aspectSelects = document.querySelectorAll('.aspect-select');
            const aspects = {};

            aspectSelects.forEach(select => {
                const aspect = select.dataset.aspect;
                aspects[aspect] = parseInt(select.value);
            });

            newRecord.satisfaction = {
                overall: overallRating,
                aspects: aspects,
                feedback: feedback,
                suggestions: suggestions
            };
        }

        // 添加到回访记录
        this.callbackData.records.unshift(newRecord);

        // 更新状态
        this.callbackData.status = 'completed';

        // 重新渲染
        this.render();

        // 关闭模态框
        document.querySelector('.modal').remove();

        Utils.showToast('回访记录保存成功', 'success');

        // 添加到时间轴
        if (window.TimelineComponent) {
            TimelineComponent.addEvent({
                id: 'event' + Date.now(),
                type: 'callback',
                time: newRecord.date,
                actor: newRecord.staff,
                content: `完成${this.getCallbackTypeText(newRecord.type)}，客户${this.getCustomerResponseText(newRecord.customerResponse)}`,
                department: AppState.currentUser.department,
                details: {
                    callbackType: newRecord.type,
                    contactResult: newRecord.contactResult,
                    satisfaction: newRecord.satisfaction?.overall || null
                }
            });
        }
    },

    /**
     * 重新安排回访
     */
    rescheduleCallback() {
        const newDate = prompt('请输入新的回访时间 (格式: YYYY-MM-DD HH:mm:ss):', this.callbackData.plan.scheduledDate);
        if (newDate && newDate !== this.callbackData.plan.scheduledDate) {
            this.callbackData.plan.scheduledDate = newDate;
            this.render();
            Utils.showToast('回访时间已重新安排', 'success');
        }
    },

    /**
     * 查看回访详情
     */
    viewCallbackDetails() {
        Utils.showToast('查看回访详情', 'info');
        // 这里可以实现详情查看逻辑
    }
};

// 将CallbackComponent暴露到全局作用域
window.CallbackComponent = CallbackComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    CallbackComponent.initialize();
});
