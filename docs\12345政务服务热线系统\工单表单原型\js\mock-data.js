/**
 * 静态演示数据
 * 包含部门、分类、地址等模拟数据
 */

// 承办单位数据
const MOCK_DEPARTMENTS = [
    { id: '001', name: '市城管局', code: 'SCGJ', type: '政府部门' },
    { id: '002', name: '市交通局', code: 'SJTJ', type: '政府部门' },
    { id: '003', name: '市住建局', code: 'SZJJ', type: '政府部门' },
    { id: '004', name: '市教育局', code: 'SJYJ', type: '政府部门' },
    { id: '005', name: '市卫健委', code: 'SWJW', type: '政府部门' },
    { id: '006', name: '市人社局', code: 'SRSJ', type: '政府部门' },
    { id: '007', name: '市生态环境局', code: 'SHJJ', type: '政府部门' },
    { id: '008', name: '市公安局', code: 'SGAJ', type: '政府部门' },
    { id: '009', name: '市政务服务局', code: 'SZWJ', type: '政府部门' },
    { id: '010', name: '市市场监管局', code: 'SJGJ', type: '政府部门' },
    { id: '011', name: '市发改委', code: 'SFGW', type: '政府部门' },
    { id: '012', name: '市财政局', code: 'SCZJ', type: '政府部门' },
    { id: '013', name: '市自然资源局', code: 'SZYJ', type: '政府部门' },
    { id: '014', name: '市水务局', code: 'SSWJ', type: '政府部门' },
    { id: '015', name: '市应急管理局', code: 'SYJJ', type: '政府部门' },
    { id: '016', name: '市文旅局', code: 'SWLJ', type: '政府部门' },
    { id: '017', name: '市农业农村局', code: 'SNYJ', type: '政府部门' },
    { id: '018', name: '市民政局', code: 'SMZJ', type: '政府部门' },
    { id: '019', name: '市司法局', code: 'SSFJ', type: '政府部门' },
    { id: '020', name: '市科技局', code: 'SKJJ', type: '政府部门' },
    { id: '021', name: '天河区政府', code: 'THQZF', type: '区政府' },
    { id: '022', name: '越秀区政府', code: 'YXQZF', type: '区政府' },
    { id: '023', name: '海珠区政府', code: 'HZQZF', type: '区政府' },
    { id: '024', name: '荔湾区政府', code: 'LWQZF', type: '区政府' },
    { id: '025', name: '白云区政府', code: 'BYQZF', type: '区政府' },
    { id: '026', name: '黄埔区政府', code: 'HPQZF', type: '区政府' },
    { id: '027', name: '番禺区政府', code: 'PYQZF', type: '区政府' },
    { id: '028', name: '花都区政府', code: 'HDQZF', type: '区政府' },
    { id: '029', name: '南沙区政府', code: 'NSQZF', type: '区政府' },
    { id: '030', name: '从化区政府', code: 'CHQZF', type: '区政府' },
    { id: '031', name: '增城区政府', code: 'ZCQZF', type: '区政府' }
];

// 问题分类数据（三级分类）
const MOCK_CATEGORIES = {
    '城市管理': {
        '环境卫生': ['垃圾清运', '道路清洁', '公厕管理', '绿化养护', '河道保洁'],
        '市政设施': ['道路维修', '路灯管理', '井盖管理', '护栏维护', '标识标牌'],
        '违法建设': ['违章建筑', '占道经营', '户外广告', '工地管理', '噪音扰民'],
        '停车管理': ['违法停车', '停车收费', '停车位规划', '共享单车', '电动车管理']
    },
    '交通运输': {
        '道路交通': ['交通拥堵', '交通信号', '道路标线', '交通事故', '道路施工'],
        '公共交通': ['公交服务', '地铁服务', '出租车服务', '网约车管理', '公交站点'],
        '交通执法': ['违法停车', '超载超限', '非法营运', '交通违法', '车辆年检'],
        '交通规划': ['道路规划', '公交线路', '停车规划', '慢行系统', '交通组织']
    },
    '住房保障': {
        '房屋质量': ['房屋漏水', '墙体开裂', '电梯故障', '消防安全', '结构安全'],
        '物业管理': ['物业服务', '物业收费', '业主维权', '小区管理', '设施维护'],
        '保障住房': ['公租房', '廉租房', '经适房', '人才房', '棚户区改造'],
        '房地产市场': ['房价调控', '中介服务', '开发商违规', '预售管理', '二手房交易']
    },
    '教育文化': {
        '基础教育': ['入学政策', '学校管理', '教师队伍', '教学质量', '校园安全'],
        '职业教育': ['技能培训', '职业院校', '就业指导', '证书认定', '校企合作'],
        '文化服务': ['图书馆', '博物馆', '文化活动', '文物保护', '非遗传承'],
        '体育健身': ['体育设施', '全民健身', '体育赛事', '体育培训', '场馆管理']
    },
    '医疗卫生': {
        '医疗服务': ['就医难', '医疗质量', '医疗费用', '医患纠纷', '急救服务'],
        '公共卫生': ['疫情防控', '食品安全', '饮用水安全', '环境卫生', '健康教育'],
        '医保政策': ['医保报销', '异地就医', '慢病管理', '大病救助', '生育保险'],
        '药品管理': ['药品质量', '药品价格', '药品供应', '处方管理', '疫苗接种']
    },
    '社会保障': {
        '养老保险': ['养老金发放', '退休手续', '社保转移', '养老服务', '敬老院管理'],
        '就业服务': ['就业政策', '失业保险', '职业培训', '创业扶持', '劳动维权'],
        '社会救助': ['低保申请', '临时救助', '残疾人保障', '孤儿救助', '流浪救助'],
        '优抚安置': ['退役军人', '烈士褒扬', '优抚对象', '军人家属', '双拥工作']
    },
    '环境保护': {
        '大气污染': ['空气质量', '工业废气', '机动车尾气', '扬尘污染', '恶臭污染'],
        '水污染': ['河流污染', '地下水污染', '饮用水源', '污水处理', '黑臭水体'],
        '噪声污染': ['交通噪声', '工业噪声', '建筑噪声', '社会噪声', '夜间施工'],
        '固废处理': ['垃圾分类', '危废处理', '建筑垃圾', '医疗废物', '电子垃圾']
    },
    '公共安全': {
        '治安管理': ['盗窃案件', '诈骗案件', '治安纠纷', '社区安全', '校园安全'],
        '消防安全': ['火灾隐患', '消防设施', '消防通道', '消防宣传', '应急演练'],
        '食品安全': ['食品质量', '餐饮卫生', '食品添加剂', '食品标识', '食品召回'],
        '生产安全': ['安全生产', '职业健康', '特种设备', '危化品管理', '事故调查']
    },
    '政务服务': {
        '办事服务': ['证件办理', '审批服务', '便民服务', '网上办事', '一站式服务'],
        '政务公开': ['信息公开', '政策解读', '办事指南', '收费标准', '服务承诺'],
        '投诉建议': ['服务态度', '办事效率', '政策咨询', '流程优化', '便民措施'],
        '数字政府': ['网站建设', 'APP服务', '数据共享', '智慧城市', '电子证照']
    },
    '其他': {
        '民生热点': ['房价问题', '就业问题', '教育公平', '医疗改革', '养老问题'],
        '经济发展': ['营商环境', '招商引资', '产业发展', '创新创业', '金融服务'],
        '社会治理': ['基层治理', '社区服务', '志愿服务', '社会组织', '信用建设'],
        '其他事项': ['意见建议', '政策咨询', '信息查询', '投诉举报', '表扬感谢']
    }
};

// 行政区划数据
const MOCK_ADMINISTRATIVE_DIVISIONS = {
    provinces: [
        { code: '110000', name: '北京市', type: 'municipality' },
        { code: '120000', name: '天津市', type: 'municipality' },
        { code: '130000', name: '河北省', type: 'province' },
        { code: '140000', name: '山西省', type: 'province' },
        { code: '150000', name: '内蒙古自治区', type: 'autonomous_region' },
        { code: '210000', name: '辽宁省', type: 'province' },
        { code: '220000', name: '吉林省', type: 'province' },
        { code: '230000', name: '黑龙江省', type: 'province' },
        { code: '310000', name: '上海市', type: 'municipality' },
        { code: '320000', name: '江苏省', type: 'province' },
        { code: '330000', name: '浙江省', type: 'province' },
        { code: '340000', name: '安徽省', type: 'province' },
        { code: '350000', name: '福建省', type: 'province' },
        { code: '360000', name: '江西省', type: 'province' },
        { code: '370000', name: '山东省', type: 'province' },
        { code: '410000', name: '河南省', type: 'province' },
        { code: '420000', name: '湖北省', type: 'province' },
        { code: '430000', name: '湖南省', type: 'province' },
        { code: '440000', name: '广东省', type: 'province' },
        { code: '450000', name: '广西壮族自治区', type: 'autonomous_region' },
        { code: '460000', name: '海南省', type: 'province' },
        { code: '500000', name: '重庆市', type: 'municipality' },
        { code: '510000', name: '四川省', type: 'province' },
        { code: '520000', name: '贵州省', type: 'province' },
        { code: '530000', name: '云南省', type: 'province' },
        { code: '540000', name: '西藏自治区', type: 'autonomous_region' },
        { code: '610000', name: '陕西省', type: 'province' },
        { code: '620000', name: '甘肃省', type: 'province' },
        { code: '630000', name: '青海省', type: 'province' },
        { code: '640000', name: '宁夏回族自治区', type: 'autonomous_region' },
        { code: '650000', name: '新疆维吾尔自治区', type: 'autonomous_region' }
    ],

    cities: {
        '440000': [ // 广东省
            { code: '440100', name: '广州市', provinceCode: '440000' },
            { code: '440200', name: '韶关市', provinceCode: '440000' },
            { code: '440300', name: '深圳市', provinceCode: '440000' },
            { code: '440400', name: '珠海市', provinceCode: '440000' },
            { code: '440500', name: '汕头市', provinceCode: '440000' },
            { code: '440600', name: '佛山市', provinceCode: '440000' },
            { code: '440700', name: '江门市', provinceCode: '440000' },
            { code: '440800', name: '湛江市', provinceCode: '440000' },
            { code: '440900', name: '茂名市', provinceCode: '440000' },
            { code: '441200', name: '肇庆市', provinceCode: '440000' },
            { code: '441300', name: '惠州市', provinceCode: '440000' },
            { code: '441400', name: '梅州市', provinceCode: '440000' },
            { code: '441500', name: '汕尾市', provinceCode: '440000' },
            { code: '441600', name: '河源市', provinceCode: '440000' },
            { code: '441700', name: '阳江市', provinceCode: '440000' },
            { code: '441800', name: '清远市', provinceCode: '440000' },
            { code: '441900', name: '东莞市', provinceCode: '440000' },
            { code: '442000', name: '中山市', provinceCode: '440000' },
            { code: '445100', name: '潮州市', provinceCode: '440000' },
            { code: '445200', name: '揭阳市', provinceCode: '440000' },
            { code: '445300', name: '云浮市', provinceCode: '440000' }
        ],
        '110000': [ // 北京市
            { code: '110100', name: '北京市', provinceCode: '110000' }
        ],
        '310000': [ // 上海市
            { code: '310100', name: '上海市', provinceCode: '310000' }
        ]
    },

    districts: {
        '440100': [ // 广州市
            { code: '440103', name: '荔湾区', cityCode: '440100' },
            { code: '440104', name: '越秀区', cityCode: '440100' },
            { code: '440105', name: '海珠区', cityCode: '440100' },
            { code: '440106', name: '天河区', cityCode: '440100' },
            { code: '440111', name: '白云区', cityCode: '440100' },
            { code: '440112', name: '黄埔区', cityCode: '440100' },
            { code: '440113', name: '番禺区', cityCode: '440100' },
            { code: '440114', name: '花都区', cityCode: '440100' },
            { code: '440115', name: '南沙区', cityCode: '440100' },
            { code: '440117', name: '从化区', cityCode: '440100' },
            { code: '440118', name: '增城区', cityCode: '440100' }
        ],
        '440300': [ // 深圳市
            { code: '440303', name: '罗湖区', cityCode: '440300' },
            { code: '440304', name: '福田区', cityCode: '440300' },
            { code: '440305', name: '南山区', cityCode: '440300' },
            { code: '440306', name: '宝安区', cityCode: '440300' },
            { code: '440307', name: '龙岗区', cityCode: '440300' },
            { code: '440308', name: '盐田区', cityCode: '440300' },
            { code: '440309', name: '龙华区', cityCode: '440300' },
            { code: '440310', name: '坪山区', cityCode: '440300' },
            { code: '440311', name: '光明区', cityCode: '440300' },
            { code: '440312', name: '大鹏新区', cityCode: '440300' }
        ]
    },

    streets: {
        '440106': [ // 天河区
            { code: '440106001', name: '五山街道', districtCode: '440106' },
            { code: '440106002', name: '员村街道', districtCode: '440106' },
            { code: '440106003', name: '车陂街道', districtCode: '440106' },
            { code: '440106004', name: '沙河街道', districtCode: '440106' },
            { code: '440106005', name: '石牌街道', districtCode: '440106' },
            { code: '440106006', name: '沙东街道', districtCode: '440106' },
            { code: '440106007', name: '天河南街道', districtCode: '440106' },
            { code: '440106008', name: '林和街道', districtCode: '440106' },
            { code: '440106009', name: '兴华街道', districtCode: '440106' },
            { code: '440106010', name: '棠下街道', districtCode: '440106' },
            { code: '440106011', name: '天园街道', districtCode: '440106' },
            { code: '440106012', name: '冼村街道', districtCode: '440106' },
            { code: '440106013', name: '猎德街道', districtCode: '440106' },
            { code: '440106014', name: '元岗街道', districtCode: '440106' },
            { code: '440106015', name: '黄村街道', districtCode: '440106' },
            { code: '440106016', name: '长兴街道', districtCode: '440106' },
            { code: '440106017', name: '龙洞街道', districtCode: '440106' },
            { code: '440106018', name: '凤凰街道', districtCode: '440106' },
            { code: '440106019', name: '前进街道', districtCode: '440106' },
            { code: '440106020', name: '珠吉街道', districtCode: '440106' },
            { code: '440106021', name: '新塘街道', districtCode: '440106' }
        ]
    },

    communities: {
        '440106001': [ // 五山街道
            { code: '440106001001', name: '华师社区', streetCode: '440106001' },
            { code: '440106001002', name: '华农社区', streetCode: '440106001' },
            { code: '440106001003', name: '岳洲社区', streetCode: '440106001' },
            { code: '440106001004', name: '茶山社区', streetCode: '440106001' },
            { code: '440106001005', name: '五山社区', streetCode: '440106001' },
            { code: '440106001006', name: '瘦狗岭社区', streetCode: '440106001' }
        ]
    }
};

// 地址建议数据
const MOCK_ADDRESSES = [
    '花城大道85号',
    '中山五路219号',
    '新港中路397号',
    '上下九步行街128号',
    '机场路1630号',
    '科学城开发大道',
    '市桥街市桥大道',
    '新华街迎宾大道',
    '万顷沙镇港前大道',
    '街口街河滨北路'
];

// 历史地址数据
const MOCK_RECENT_ADDRESSES = [
    {
        id: 1,
        fullAddress: '广东省广州市天河区五山街道华师社区花城大道85号',
        province: '440000',
        city: '440100',
        district: '440106',
        street: '440106001',
        community: '440106001001',
        detail: '花城大道85号',
        coordinates: { lng: 113.324520, lat: 23.129163 },
        frequency: 15
    },
    {
        id: 2,
        fullAddress: '广东省广州市越秀区中山五路219号',
        province: '440000',
        city: '440100',
        district: '440104',
        street: '',
        community: '',
        detail: '中山五路219号',
        coordinates: { lng: 113.280637, lat: 23.125178 },
        frequency: 8
    },
    {
        id: 3,
        fullAddress: '广东省广州市海珠区新港中路397号',
        province: '440000',
        city: '440100',
        district: '440105',
        street: '',
        community: '',
        detail: '新港中路397号',
        coordinates: { lng: 113.325311, lat: 23.103006 },
        frequency: 5
    },
    {
        id: 4,
        fullAddress: '广东省广州市荔湾区上下九步行街128号',
        province: '440000',
        city: '440100',
        district: '440103',
        street: '',
        community: '',
        detail: '上下九步行街128号',
        coordinates: { lng: 113.243028, lat: 23.115536 },
        frequency: 3
    },
    {
        id: 5,
        fullAddress: '广东省广州市白云区机场路1630号',
        province: '440000',
        city: '440100',
        district: '440111',
        street: '',
        community: '',
        detail: '机场路1630号',
        coordinates: { lng: 113.298572, lat: 23.168306 },
        frequency: 2
    }
];

// 自定义标签历史数据
const MOCK_CUSTOM_TAGS = [
    '紧急处理', '重点关注', '多次投诉', '媒体关注', '领导关注',
    '跨部门', '历史问题', '民生热点', '季节性', '临时性'
];

// 用户偏好设置
const MOCK_USER_PREFERENCES = {
    autoSave: true,
    autoSaveInterval: 30,
    defaultTicketSource: '电话来电',
    defaultPriority: '普通',
    defaultProcessMode: '派单处理',
    recentDepartments: ['001', '002', '003', '021', '022'],
    favoriteCategories: ['城市管理', '交通运输', '住房保障'],
    theme: 'light', // 固定为明亮主题
    language: 'zh-CN'
};

// 模拟API响应数据
const MOCK_API_RESPONSES = {
    // 保存草稿响应
    saveDraft: {
        success: true,
        message: '草稿保存成功',
        data: {
            draftId: 'draft_' + Date.now(),
            saveTime: new Date().toISOString()
        }
    },
    
    // 提交工单响应
    submitTicket: {
        success: true,
        message: '工单提交成功',
        data: {
            ticketId: 'WD' + new Date().toISOString().slice(0, 10).replace(/-/g, '') + '000001',
            submitTime: new Date().toISOString(),
            status: 'submitted'
        }
    },
    
    // 文件上传响应
    uploadFile: {
        success: true,
        message: '文件上传成功',
        data: {
            fileId: 'file_' + Date.now(),
            fileName: 'example.jpg',
            fileSize: 1024000,
            fileUrl: '/uploads/example.jpg',
            uploadTime: new Date().toISOString()
        }
    },
    
    // 地址验证响应
    validateAddress: {
        success: true,
        message: '地址验证成功',
        data: {
            isValid: true,
            standardAddress: '广东省广州市天河区珠江新城花城大道85号',
            coordinates: {
                longitude: 113.324520,
                latitude: 23.129163
            },
            district: '天河区',
            street: '珠江新城街道'
        }
    }
};

// 模拟延迟函数
function mockDelay(ms = 1000) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 模拟API调用函数
const MockAPI = {
    /**
     * 获取部门列表
     * @param {string} keyword 搜索关键词
     * @returns {Promise} 部门列表
     */
    async getDepartments(keyword = '') {
        await mockDelay(500);
        let departments = MOCK_DEPARTMENTS;
        
        if (keyword) {
            departments = departments.filter(dept => 
                dept.name.includes(keyword) || dept.code.includes(keyword.toUpperCase())
            );
        }
        
        return {
            success: true,
            data: departments
        };
    },
    
    /**
     * 获取分类列表
     * @param {string} parentCategory 父级分类
     * @returns {Promise} 分类列表
     */
    async getCategories(parentCategory = null) {
        await mockDelay(300);
        
        if (!parentCategory) {
            // 返回一级分类
            return {
                success: true,
                data: Object.keys(MOCK_CATEGORIES)
            };
        } else if (MOCK_CATEGORIES[parentCategory]) {
            // 返回二级分类
            return {
                success: true,
                data: Object.keys(MOCK_CATEGORIES[parentCategory])
            };
        } else {
            // 查找三级分类
            for (const [level1, level2Data] of Object.entries(MOCK_CATEGORIES)) {
                if (level2Data[parentCategory]) {
                    return {
                        success: true,
                        data: level2Data[parentCategory]
                    };
                }
            }
        }
        
        return {
            success: false,
            message: '分类不存在'
        };
    },
    
    /**
     * 地址搜索建议
     * @param {string} keyword 搜索关键词
     * @returns {Promise} 地址建议列表
     */
    async searchAddresses(keyword) {
        await mockDelay(200);
        
        const suggestions = MOCK_ADDRESSES.filter(addr => 
            addr.includes(keyword)
        ).slice(0, 10);
        
        return {
            success: true,
            data: suggestions
        };
    },
    
    /**
     * 验证地址
     * @param {string} address 地址
     * @returns {Promise} 验证结果
     */
    async validateAddress(address) {
        await mockDelay(800);
        return MOCK_API_RESPONSES.validateAddress;
    },
    
    /**
     * 保存草稿
     * @param {Object} formData 表单数据
     * @returns {Promise} 保存结果
     */
    async saveDraft(formData) {
        await mockDelay(1000);
        return MOCK_API_RESPONSES.saveDraft;
    },
    
    /**
     * 提交工单
     * @param {Object} formData 表单数据
     * @returns {Promise} 提交结果
     */
    async submitTicket(formData) {
        await mockDelay(2000);
        return MOCK_API_RESPONSES.submitTicket;
    },
    
    /**
     * 上传文件
     * @param {File} file 文件对象
     * @returns {Promise} 上传结果
     */
    async uploadFile(file) {
        await mockDelay(1500);
        return {
            ...MOCK_API_RESPONSES.uploadFile,
            data: {
                ...MOCK_API_RESPONSES.uploadFile.data,
                fileName: file.name,
                fileSize: file.size
            }
        };
    },

    /**
     * 获取省份列表
     * @returns {Promise} 省份列表
     */
    async getProvinces() {
        await mockDelay(300);
        return {
            success: true,
            data: MOCK_ADMINISTRATIVE_DIVISIONS.provinces
        };
    },

    /**
     * 获取城市列表
     * @param {string} provinceCode 省份代码
     * @returns {Promise} 城市列表
     */
    async getCities(provinceCode) {
        await mockDelay(300);
        const cities = MOCK_ADMINISTRATIVE_DIVISIONS.cities[provinceCode] || [];
        return {
            success: true,
            data: cities
        };
    },

    /**
     * 获取区县列表
     * @param {string} cityCode 城市代码
     * @returns {Promise} 区县列表
     */
    async getDistricts(cityCode) {
        await mockDelay(300);
        const districts = MOCK_ADMINISTRATIVE_DIVISIONS.districts[cityCode] || [];
        return {
            success: true,
            data: districts
        };
    },

    /**
     * 获取街道列表
     * @param {string} districtCode 区县代码
     * @returns {Promise} 街道列表
     */
    async getStreets(districtCode) {
        await mockDelay(300);
        const streets = MOCK_ADMINISTRATIVE_DIVISIONS.streets[districtCode] || [];
        return {
            success: true,
            data: streets
        };
    },

    /**
     * 获取社区列表
     * @param {string} streetCode 街道代码
     * @returns {Promise} 社区列表
     */
    async getCommunities(streetCode) {
        await mockDelay(300);
        const communities = MOCK_ADMINISTRATIVE_DIVISIONS.communities[streetCode] || [];
        return {
            success: true,
            data: communities
        };
    },

    /**
     * 地址详细信息搜索建议
     * @param {string} keyword 搜索关键词
     * @param {Object} location 当前选择的行政区划
     * @returns {Promise} 地址建议列表
     */
    async searchDetailAddresses(keyword, location = {}) {
        await mockDelay(200);

        const suggestions = MOCK_ADDRESSES.filter(addr =>
            addr.includes(keyword)
        ).slice(0, 10);

        return {
            success: true,
            data: suggestions
        };
    },

    /**
     * 获取历史地址
     * @returns {Promise} 历史地址列表
     */
    async getHistoryAddresses() {
        await mockDelay(200);
        return {
            success: true,
            data: MOCK_RECENT_ADDRESSES.sort((a, b) => b.frequency - a.frequency)
        };
    },

    /**
     * 地址验证
     * @param {Object} addressData 地址数据
     * @returns {Promise} 验证结果
     */
    async validateFullAddress(addressData) {
        await mockDelay(800);

        // 模拟地址验证逻辑
        const isValid = addressData.province && addressData.city && addressData.district;

        return {
            success: true,
            data: {
                isValid: isValid,
                standardAddress: isValid ? this.buildFullAddress(addressData) : '',
                coordinates: isValid ? {
                    longitude: 113.324520 + (Math.random() - 0.5) * 0.1,
                    latitude: 23.129163 + (Math.random() - 0.5) * 0.1
                } : null,
                suggestions: isValid ? [] : ['请检查行政区划选择是否正确']
            }
        };
    },

    /**
     * 构建完整地址
     * @param {Object} addressData 地址数据
     * @returns {string} 完整地址
     */
    buildFullAddress(addressData) {
        const parts = [];

        if (addressData.provinceName) parts.push(addressData.provinceName);
        if (addressData.cityName) parts.push(addressData.cityName);
        if (addressData.districtName) parts.push(addressData.districtName);
        if (addressData.streetName) parts.push(addressData.streetName);
        if (addressData.communityName) parts.push(addressData.communityName);
        if (addressData.detail) parts.push(addressData.detail);

        return parts.join('');
    }
};

// 导出数据（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MOCK_DEPARTMENTS,
        MOCK_CATEGORIES,
        MOCK_ADDRESSES,
        MOCK_RECENT_ADDRESSES,
        MOCK_CUSTOM_TAGS,
        MOCK_USER_PREFERENCES,
        MOCK_API_RESPONSES,
        MockAPI
    };
}
