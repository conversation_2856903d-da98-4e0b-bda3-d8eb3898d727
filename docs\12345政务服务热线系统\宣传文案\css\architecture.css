/* 系统架构页面专用样式 */

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 架构概览统计 */
.architecture-overview {
    padding: 3rem 0;
    background: white;
}

.architecture-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    min-width: 180px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.tech {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.org {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-icon.ai {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stat-icon.security {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* 架构部分 */
.architecture-sections {
    padding: 2rem 0 4rem;
    background: #f8f9fa;
}

.architecture-section {
    margin-bottom: 4rem;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.section-icon {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.section-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #ffffff;
}

.section-subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

.section-content {
    padding: 2rem;
}

/* 技术层级 */
.tech-layers {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.tech-layer {
    background: #f8f9fa;
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.tech-layer.presentation {
    border-left: 4px solid #17a2b8;
}

.tech-layer.application {
    border-left: 4px solid #28a745;
}

.tech-layer.ai-layer {
    border-left: 4px solid #ffc107;
}

.tech-layer.data {
    border-left: 4px solid #6f42c1;
}

.tech-layer.infrastructure {
    border-left: 4px solid #dc3545;
}

.layer-header {
    background: white;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.layer-header h3 {
    margin: 0;
    color: #333333;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.layer-components {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.component {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.component:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.component-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.component-info h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.component-info p {
    color: #555;
    line-height: 1.5;
    margin: 0;
    font-size: 0.9rem;
}

/* 组织架构 */
.org-structure {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.org-level {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    border-left: 4px solid;
    position: relative;
}

.org-level.level-1 {
    border-left-color: #dc3545;
}

.org-level.level-2 {
    border-left-color: #fd7e14;
}

.org-level.level-3 {
    border-left-color: #ffc107;
}

.org-level.level-4 {
    border-left-color: #28a745;
}

.org-level.level-5 {
    border-left-color: #17a2b8;
}

.level-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.level-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.level-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.org-units {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.org-unit {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.org-unit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.org-unit.primary {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.unit-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.unit-info h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.unit-info p {
    color: #555;
    line-height: 1.5;
    margin: 0;
    font-size: 0.9rem;
}

/* 系统特性 */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.feature-card li {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
    color: #555;
    font-size: 0.9rem;
}

.feature-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .architecture-stats {
        gap: 2rem;
        flex-direction: column;
        align-items: center;
    }
    
    .stat-card {
        width: 100%;
        max-width: 300px;
    }
    
    .section-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .layer-components {
        grid-template-columns: 1fr;
    }
    
    .org-units {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .level-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .section-content {
        padding: 1rem;
    }
    
    .tech-layer {
        margin-bottom: 1rem;
    }
    
    .layer-components {
        padding: 1rem;
    }
    
    .component {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .org-unit {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tech-layer,
.org-level,
.feature-card {
    animation: slideInUp 0.6s ease-out;
}

.tech-layer:nth-child(even) {
    animation-delay: 0.1s;
}

.org-level:nth-child(even) {
    animation-delay: 0.1s;
}

/* 打印样式 */
@media print {
    .header,
    .footer {
        display: none;
    }
    
    .architecture-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 2rem;
    }
    
    .page-header {
        background: none;
        color: #333;
    }
    
    .tech-layer,
    .org-level {
        break-inside: avoid;
    }
}
