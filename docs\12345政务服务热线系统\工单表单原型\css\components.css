/**
 * 组件样式
 * 包含按钮、附件、模态框等组件样式
 */

/* 按钮基础样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    user-select: none;
    white-space: nowrap;
    min-height: 40px;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-light);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 主要按钮 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-primary:active {
    background-color: var(--primary-active);
    border-color: var(--primary-active);
}

/* 次要按钮 */
.btn-secondary {
    background-color: var(--bg-white);
    border-color: var(--border-color);
    color: var(--text-color);
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-secondary:active {
    background-color: var(--primary-light);
}

/* 轮廓按钮 */
.btn-outline {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-outline:active {
    background-color: var(--primary-active);
    border-color: var(--primary-active);
}

/* 成功按钮 */
.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #73d13d;
    border-color: #73d13d;
}

/* 警告按钮 */
.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #ffc53d;
    border-color: #ffc53d;
}

/* 危险按钮 */
.btn-danger {
    background-color: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

.btn-danger:hover {
    background-color: #ff7875;
    border-color: #ff7875;
}

/* 链接按钮 */
.btn-link {
    background-color: transparent;
    border-color: transparent;
    color: var(--primary-color);
    text-decoration: underline;
    padding: 0;
    min-height: auto;
}

.btn-link:hover {
    color: var(--primary-hover);
}

/* 按钮尺寸 */
.btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-md);
    min-height: 48px;
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    min-height: 32px;
}

/* 特殊按钮 */
.copy-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.copy-btn:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.add-tag-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    white-space: nowrap;
}

.add-tag-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.map-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    white-space: nowrap;
}

.map-btn:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* 按钮图标和文字 */
.btn-icon {
    font-size: var(--font-size-sm);
    line-height: 1;
    transition: transform var(--transition-fast);
}

.btn-text {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

/* 按钮悬停效果 */
.copy-btn:hover .btn-icon,
.add-tag-btn:hover .btn-icon,
.map-btn:hover .btn-icon {
    transform: scale(1.1);
}

.copy-btn:active,
.add-tag-btn:active,
.map-btn:active {
    transform: translateY(1px);
}

/* 按钮成功状态 */
.copy-btn.success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.copy-btn.success .btn-icon {
    transform: scale(1.2);
}

/* 按钮加载状态 */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading .btn-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}



.btn-close {
    background-color: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: var(--font-size-xl);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.btn-close:hover {
    background-color: var(--bg-gray);
    color: var(--text-color);
}

/* 附件卡片 */
.attachment-card {
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.attachment-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.attachment-preview {
    width: 100%;
    height: 120px;
    background-color: var(--bg-gray);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.attachment-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.attachment-preview .file-icon {
    font-size: 48px;
    color: var(--text-disabled);
}

.attachment-info {
    flex: 1;
}

.attachment-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    word-break: break-all;
}

.attachment-size {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.attachment-description {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.attachment-description input {
    width: 100%;
    border: none;
    background: transparent;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    padding: var(--spacing-xs) 0;
}

.attachment-description input:focus {
    outline: none;
    border-bottom: 1px solid var(--primary-color);
}

.attachment-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.attachment-actions .btn {
    flex: 1;
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
    min-height: 28px;
}

/* 上传进度 */
.upload-progress {
    width: 100%;
    height: 4px;
    background-color: var(--bg-gray);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-top: var(--spacing-xs);
}

.upload-progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: var(--border-radius-sm);
    transition: width var(--transition-normal);
    width: 0%;
}

.upload-progress-bar.success {
    background-color: var(--success-color);
}

.upload-progress-bar.error {
    background-color: var(--error-color);
}

/* 预览内容 */
.preview-content {
    max-height: 60vh;
    overflow-y: auto;
}

.preview-section {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.preview-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.preview-section-title {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
}

.preview-field {
    display: flex;
    margin-bottom: var(--spacing-sm);
}

.preview-field:last-child {
    margin-bottom: 0;
}

.preview-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    min-width: 120px;
    margin-right: var(--spacing-md);
}

.preview-value {
    color: var(--text-color);
    flex: 1;
    word-break: break-all;
}

.preview-value.empty {
    color: var(--text-disabled);
    font-style: italic;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.draft {
    background-color: var(--warning-color);
    color: white;
}

.status-indicator.submitted {
    background-color: var(--info-color);
    color: white;
}

.status-indicator.processing {
    background-color: var(--primary-color);
    color: white;
}

.status-indicator.completed {
    background-color: var(--success-color);
    color: white;
}

.status-indicator.rejected {
    background-color: var(--error-color);
    color: white;
}

/* 徽章 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-gray);
    color: var(--text-secondary);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.badge.primary {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.badge.success {
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
}

.badge.warning {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
}

.badge.error {
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
}

/* 分割线 */
.divider {
    height: 1px;
    background-color: var(--border-light);
    margin: var(--spacing-lg) 0;
}

.divider.vertical {
    width: 1px;
    height: auto;
    margin: 0 var(--spacing-md);
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    text-align: center;
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: 64px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state-title {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
}

.empty-state-description {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 1000;
    margin-bottom: var(--spacing-xs);
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 1000;
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 步骤指示器 */
.steps {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.step {
    display: flex;
    align-items: center;
    flex: 1;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 100%;
    height: 1px;
    background-color: var(--border-color);
    z-index: 1;
}

.step-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--bg-gray);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    position: relative;
    z-index: 2;
}

.step.active .step-indicator {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.step.completed .step-indicator {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.step.completed .step-indicator::after {
    content: '✓';
}

.step-title {
    margin-left: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.step.active .step-title {
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

.step.completed .step-title {
    color: var(--success-color);
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-sm);
}

.breadcrumb-item {
    color: var(--text-secondary);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: var(--spacing-xs);
    color: var(--text-disabled);
}

.breadcrumb-item.active {
    color: var(--text-color);
    font-weight: var(--font-weight-medium);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}
