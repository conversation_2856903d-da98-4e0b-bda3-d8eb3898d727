/* 组件样式 */

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--btn-padding-md);
    height: var(--btn-height-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-light);
}

.btn[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 按钮尺寸 */
.btn-small {
    padding: var(--btn-padding-sm);
    height: var(--btn-height-sm);
    font-size: var(--font-size-xs);
}

.btn-large {
    padding: var(--btn-padding-lg);
    height: var(--btn-height-lg);
    font-size: var(--font-size-base);
}

/* 按钮类型 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #ffffff;
}

.btn-primary:hover:not([disabled]) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-primary:active {
    background-color: var(--primary-active);
    border-color: var(--primary-active);
}

.btn-secondary {
    background-color: var(--bg-white);
    border-color: var(--border-color);
    color: var(--text-color);
}

.btn-secondary:hover:not([disabled]) {
    background-color: var(--bg-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--text-color);
}

.btn-outline:hover:not([disabled]) {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: #ffffff;
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: #ffffff;
}

.btn-error {
    background-color: var(--error-color);
    border-color: var(--error-color);
    color: #ffffff;
}

/* 按钮图标 */
.btn-icon {
    font-size: var(--font-size-sm);
    line-height: 1;
}

.btn-text {
    line-height: 1;
}

/* 输入框组件 */
.form-input {
    display: block;
    width: 100%;
    padding: var(--input-padding);
    height: var(--input-height-md);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-base);
    color: var(--text-color);
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.form-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.form-input::placeholder {
    color: var(--text-placeholder);
}

.form-input[disabled] {
    background-color: var(--bg-light);
    color: var(--text-disabled);
    cursor: not-allowed;
}

/* 选择框组件 */
.form-select {
    display: block;
    width: 100%;
    padding: var(--input-padding);
    height: var(--input-height-md);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-base);
    color: var(--text-color);
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

/* 复选框组件 */
.checkbox {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkbox-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-white);
    transition: all var(--transition-normal);
}

.checkbox-input:checked + .checkbox-box {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #ffffff;
}

.checkbox-input:checked + .checkbox-box::after {
    content: '✓';
    font-size: 10px;
    font-weight: bold;
}

.checkbox-label {
    margin-left: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-color);
}

/* 表格复选框 */
.table-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

/* 标签组件 */
.tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--tag-padding);
    height: var(--tag-height);
    font-size: var(--tag-font-size);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    border-radius: var(--tag-border-radius);
    white-space: nowrap;
}

.tag-primary {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.tag-success {
    background-color: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.tag-warning {
    background-color: var(--warning-light);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.tag-error {
    background-color: var(--error-light);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.tag-info {
    background-color: var(--info-light);
    color: var(--info-color);
    border: 1px solid var(--info-color);
}

.tag-gray {
    background-color: var(--bg-light);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

/* 徽章组件 */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: var(--badge-size);
    height: var(--badge-size);
    padding: 0 4px;
    font-size: var(--badge-font-size);
    font-weight: var(--font-weight-bold);
    line-height: 1;
    color: #ffffff;
    background-color: var(--error-color);
    border-radius: var(--badge-border-radius);
    white-space: nowrap;
}

.badge-primary {
    background-color: var(--primary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-warning {
    background-color: var(--warning-color);
}

.badge-error {
    background-color: var(--error-color);
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--indicator-margin);
    font-size: var(--font-size-sm);
}

.status-dot {
    width: var(--indicator-size);
    height: var(--indicator-size);
    border-radius: 50%;
    flex-shrink: 0;
}

.status-dot.draft {
    background-color: var(--status-draft);
}

.status-dot.pending {
    background-color: var(--status-pending);
}

.status-dot.processing {
    background-color: var(--status-processing);
}

.status-dot.review {
    background-color: var(--status-review);
}

.status-dot.callback {
    background-color: var(--status-callback);
}

.status-dot.closed {
    background-color: var(--status-closed);
}

.status-dot.cancelled {
    background-color: var(--status-cancelled);
}

.status-dot.suspended {
    background-color: var(--status-suspended);
}

/* 紧急程度指示器 */
.urgency-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.urgency-indicator.normal {
    color: var(--urgency-normal);
}

.urgency-indicator.urgent {
    color: var(--urgency-urgent);
}

.urgency-indicator.critical {
    color: var(--urgency-critical);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* 处理模式指示器 */
.mode-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.mode-indicator.instant {
    color: var(--mode-instant);
}

.mode-indicator.normal {
    color: var(--mode-normal);
}

.mode-indicator.collaborative {
    color: var(--mode-collaborative);
}

.mode-indicator.supervise {
    color: var(--mode-supervise);
}

/* 时限指示器 */
.time-limit {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.time-limit.normal {
    color: var(--success-color);
}

.time-limit.warning {
    color: var(--warning-color);
}

.time-limit.danger {
    color: var(--error-color);
}

.time-limit.timeout {
    color: var(--error-color);
    animation: blink 1s infinite;
}

/* 加载状态 */
.loading-spinner {
    width: var(--spinner-size);
    height: var(--spinner-size);
    border: var(--spinner-border) solid var(--bg-light);
    border-top: var(--spinner-border) solid var(--spinner-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    text-align: center;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.empty-desc {
    font-size: var(--font-size-sm);
    color: var(--text-disabled);
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-dark);
    color: #ffffff;
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: var(--z-tooltip);
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}
