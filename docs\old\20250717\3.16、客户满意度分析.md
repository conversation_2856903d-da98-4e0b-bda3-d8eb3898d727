
---

### **“客户满意度分析”页面内容详解**

#### **一、 页面核心目标**

1.  **量化客户口碑**: 将客户满意度（CSAT）、净推荐值（NPS）等指标进行量化和可视化呈现。
2.  **关联分析**: 将满意度得分与工单的具体属性（如处理人、工单类型、处理时长）进行关联，找出影响满意度的关键因素。
3.  **洞察客户原声**: 深入分析客户留下的具体评论和标签，理解得分背后的具体原因。
4.  **识别服务明星与待改进者**: 找出持续获得高评价的员工和团队，以及需要关注和辅导的对象。
5.  **追踪满意度趋势**: 监控客户口碑的长期变化，评估服务改进措施是否带来了客户体验的提升。

---

### **二、 页面内容与布局**

页面采用**“筛选器 + 核心指标卡 + 多个分析图表 + 客户评论词云/列表”**的布局。

#### **1. 全局筛选器区 (Global Filters)**

*   **时间范围**: **[最重要的筛选器]** 按**评价提交时间**进行筛选。
*   **部门/团队**: 树形选择器，分析特定团队获得的服务评价。
*   **处理人员**: 多选搜索框。
*   **工单模板/类型**: 分析处理不同业务类型工单获得的评价。
*   **客户/客户组**: 分析特定客户群体的满意度。
*   **评价分数**: 可以直接筛选特定分数段的评价（如只看1星差评，或只看5星好评）。

#### **2. 核心满意度指标卡 (Key Satisfaction Metrics)**

位于页面顶部，展示最核心的客户口碑指标。

*   **总体客户满意度 (CSAT)**: 一个大的**仪表盘图**或**百分比数字**，如 `95.8%` (计算方式：(4星+5星评价数) / 总评价数)。
*   **净推荐值 (NPS)**: 一个大的数字，如 `+52` (计算方式：推荐者% - 贬损者%)。
*   **总评价数**: `1,280` (在选定时间范围内收到的总评价数)。
*   **平均评分**: `4.8 / 5.0`。
*   **各分段评价数**:
    *   `5星好评`: `1100`
    *   `1-2星差评`: `35` (用红色警示)

#### **3. 满意度分析图表卡片区 (Satisfaction Analysis Charts)**

这是页面的主体，从不同维度深入剖析满意度数据。

*   **卡片1：满意度/NPS得分分布**
    *   **图表类型**: **条形图**。
    *   **CSAT视图**: 展示1星、2星、3星、4星、5星评价分别的数量。
    *   **NPS视图**: 展示贬损者(0-6分)、被动者(7-8分)、推荐者(9-10分)三类用户的数量和占比。
    *   **交互**: 点击某个条形（如“1星差评”），页面下方的评论列表会联动筛选，只显示所有1星差评的具体内容。

*   **卡片2：满意度趋势图**
    *   **图表类型**: **折线图**。
    *   **X轴**: 时间（按天、周、月）。
    *   **Y轴**: 分数/百分比。
    *   **内容**: 展示“CSAT满意度”或“NPS得分”在过去一段时间内的波动情况。可以帮助判断某些产品发布或流程变更是否影响了客户口碑。

*   **卡片3：按不同维度分析满意度 - [核心根因分析工具]**
    *   **图表类型**: **分组条形图 (Grouped Bar Chart)** 或 **箱形图 (Box Plot)**。
    *   **内容**: 这是一个**可交互的图表**，以“**平均分**”或“**差评率**”作为衡量指标，然后按不同维度进行分解对比。
        *   **按“处理团队/处理人”分解**: **[最常用的分析]** 清晰地对比出不同团队或个人的服务质量差异，识别服务明星和需要辅导的员工。
        *   **按“工单类型”分解**: 找出客户对哪类服务的评价最高，对哪类最低。例如，发现“安装指导”类工单满意度普遍偏低，可能意味着产品说明书或流程需要改进。
        *   **按“工单处理时长”分解**: **[关联分析]** 将工单按解决时长分段（如<1小时, 1-4小时, 4-24小时, >24小时），对比各段的平均满意度。通常能验证“解决越快，满意度越高”的假设，或发现异常。

*   **卡片4：客户评论词云与标签分析 (Customer Comment Analysis)**
    *   **左侧 (词云 - Word Cloud)**:
        *   对所有客户的文字评论进行分词和频率统计，生成一个词云。字体越大的词，表示被客户提及的频率越高。例如，“**耐心**”、“**专业**”、“**快速**”等正面词，或“**等待**”、“**重复**”、“**无人回复**”等负面词会非常醒目。
    *   **右侧 (标签排行榜 - Tag Ranking)**:
        *   回访员或系统可以为客户评论打上标签（如：表扬-服务态度、投诉-解决不彻底）。这里会用条形图展示各个标签出现的次数。
    *   **洞察**: 这是**定性分析**的核心，帮助管理者快速抓住客户反馈的焦点，理解得分背后的具体原因。

#### **4. 客户评价明细列表 (Satisfaction Details Table)**

位于页面的最下方，是所有分析的最终落脚点，提供了客户的“原声”。

*   **内容**: 与页面上所有筛选器和图表交互联动。
*   **列表表头**:
    *   `评价时间`:
    *   `关联工单ID`: 可点击跳转。
    *   `客户信息`:
    *   `处理人/团队`:
    *   `**评分**`: 显示客户给出的具体分数（如 5/5 或 10/10）。
    *   `**客户原评论**`: **[最宝贵的信息]** 完整展示客户留下的文字评论。
    *   `评价标签`: 显示该评论被打上的标签。
*   **功能**: 管理者可以在此直接回复或跟进某些差评。提供导出功能。

---