# 12345政务服务热线工单列表原型

## 项目概述

这是一个基于深度优化设计方案的12345政务服务热线工单列表原型系统，实现了完整的工单管理功能，支持多角色、多场景的业务需求。

## 功能特性

### 🎯 核心功能
- **多角色支持**：市级话务员、各级管理者、执行人员、回访员等
- **智能筛选**：状态、紧急程度、处理模式、角色定制化筛选
- **高级搜索**：工单编号、市民信息、关键词、地址等多维度搜索
- **智能排序**：基于业务优先级的动态排序算法
- **批量操作**：批量派单、合并、导出等操作
- **实时更新**：自动刷新、状态同步

### 🔧 技术特性
- **响应式设计**：完美适配桌面端、平板端、手机端
- **模块化架构**：CSS和JS文件合理拆分，便于维护
- **性能优化**：虚拟滚动、懒加载、缓存策略
- **用户体验**：加载状态、错误处理、消息提示
- **无障碍设计**：键盘导航、屏幕阅读器支持

### 📊 业务特性
- **工单状态管理**：草稿、待接收、处理中、待审核、待回访、已关闭等
- **主协办模式**：支持主办单位和协办单位的协同处理
- **工单合并拆分**：市级话务员专用的工单合并和拆分功能
- **督办催办**：多级督办机制和催办流程
- **时限管理**：智能时限计算和预警提醒

## 文件结构

```
工单列表原型/
├── index.html              # 主页面
├── css/                    # 样式文件
│   ├── variables.css       # CSS变量定义
│   ├── base.css           # 基础样式和重置
│   ├── components.css     # 组件样式
│   ├── layout.css         # 布局样式
│   ├── table.css          # 表格样式
│   ├── filters.css        # 筛选器样式
│   ├── modals.css         # 模态框样式
│   └── responsive.css     # 响应式样式
├── js/                     # JavaScript文件
│   ├── config.js          # 系统配置
│   ├── utils.js           # 工具函数
│   ├── mock-data.js       # 模拟数据
│   ├── api.js             # API接口
│   ├── components/        # 组件目录
│   │   ├── table.js       # 表格组件
│   │   ├── filters.js     # 筛选组件
│   │   ├── modals.js      # 模态框组件
│   │   └── pagination.js  # 分页组件
│   ├── ticket-list.js     # 工单列表控制器
│   └── main.js            # 主入口文件
└── README.md              # 说明文档
```

## 快速开始

### 1. 直接打开
直接用浏览器打开 `index.html` 文件即可运行原型。

### 2. 本地服务器（推荐）
为了更好的体验，建议使用本地服务器：

```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx http-server

# 使用PHP
php -S localhost:8000
```

然后访问 `http://localhost:8000`

## 角色切换

系统支持多种角色，可以通过修改 `js/api.js` 文件中的 `currentUser.role` 来切换角色：

```javascript
currentUser: {
    id: 1,
    name: '张三',
    role: 'municipal_operator', // 修改这里切换角色
    department: '市12345中心',
    permissions: ['view', 'create', 'edit', 'dispatch', 'merge', 'split']
}
```

### 支持的角色类型
- `municipal_operator` - 市级话务员
- `district_operator` - 区级话务员  
- `street_operator` - 街镇话务员
- `department_staff` - 部门工作人员
- `executor` - 执行人员
- `collaborator` - 协办人
- `callback_staff` - 回访员
- `manager` - 管理者
- `leader` - 领导
- `citizen` - 市民

## 主要功能说明

### 工单筛选
- **快速筛选**：状态、紧急程度、处理模式
- **角色筛选**：根据用户角色显示专用筛选条件
- **搜索功能**：支持工单编号、市民姓名、问题关键词搜索
- **高级搜索**：多条件组合搜索（功能占位，需要进一步实现）

### 工单操作
- **查看详情**：点击工单编号或使用操作按钮
- **批量操作**：选择多个工单进行批量处理
- **排序功能**：点击表头进行排序
- **导出功能**：支持Excel、CSV等格式导出

### 响应式支持
- **桌面端**：完整表格显示，支持所有功能
- **平板端**：简化表格，折叠部分操作
- **手机端**：卡片布局，滑动操作（需要进一步实现）

## 自定义配置

### 修改配置
主要配置在 `js/config.js` 文件中：

```javascript
const CONFIG = {
    // 分页配置
    pagination: {
        defaultPageSize: 50,
        pageSizeOptions: [20, 50, 100]
    },
    
    // 表格配置
    table: {
        autoRefreshInterval: 30000 // 自动刷新间隔
    },
    
    // 颜色配置
    colors: {
        status: {
            draft: '#8c8c8c',
            pending: '#fa8c16',
            // ...
        }
    }
};
```

### 修改样式
CSS变量在 `css/variables.css` 中定义，可以轻松自定义主题：

```css
:root {
    --primary-color: #1890ff;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    /* ... */
}
```

## 数据模拟

系统使用 `js/mock-data.js` 生成模拟数据，包括：
- 200个模拟工单
- 多种工单状态和类型
- 不同紧急程度和处理模式
- 市民信息和部门信息
- 时间和地理信息

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 开发说明

### 代码结构
- **模块化设计**：每个功能模块独立，便于维护
- **事件驱动**：使用事件系统进行组件间通信
- **配置驱动**：通过配置文件控制系统行为
- **工具函数**：提供丰富的工具函数库

### 扩展开发
1. **添加新功能**：在对应的组件文件中添加
2. **修改样式**：在对应的CSS文件中修改
3. **添加配置**：在 `config.js` 中添加新配置项
4. **添加工具函数**：在 `utils.js` 中添加

### 调试工具
在开发环境下，可以通过 `window.DEBUG` 访问调试工具：

```javascript
// 查看配置
console.log(window.DEBUG.config);

// 查看模拟数据
console.log(window.DEBUG.mockData);

// 获取控制器实例
const controller = window.DEBUG.controller();
```

## 注意事项

1. **模拟数据**：当前使用模拟数据，实际项目需要对接真实API
2. **权限控制**：权限验证在前端实现，实际项目需要后端验证
3. **文件上传**：文件上传功能需要后端支持
4. **实时通信**：实时更新功能需要WebSocket或SSE支持

## 后续开发建议

1. **API对接**：将模拟API替换为真实的后端接口
2. **权限系统**：完善权限验证和控制机制
3. **实时通信**：实现WebSocket连接进行实时数据更新
4. **移动端优化**：完善移动端的卡片布局和交互
5. **性能优化**：针对大数据量场景进行进一步优化
6. **测试覆盖**：添加单元测试和集成测试
7. **文档完善**：添加API文档和组件文档

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目文档：参考 `docs/12345政务服务热线系统/14、工单列表内容.md`
- 技术支持：查看代码注释和配置说明

---

**版本**：v1.0.0  
**更新时间**：2024-12-01  
**开发状态**：原型阶段
