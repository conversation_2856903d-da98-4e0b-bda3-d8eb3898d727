/**
 * 状态标识系统
 * 处理工单状态、紧急程度、督办标识等可视化标识
 */

window.StatusIndicator = {
    // 状态配置
    statusConfig: {
        draft: {
            text: '草稿',
            icon: 'edit',
            color: '#8c8c8c',
            bgColor: 'rgba(140, 140, 140, 0.1)',
            description: '工单尚未提交，可以编辑'
        },
        pending: {
            text: '待接收',
            icon: 'inbox',
            color: '#1890ff',
            bgColor: 'rgba(24, 144, 255, 0.1)',
            description: '工单已提交，等待承办单位接收'
        },
        processing: {
            text: '处理中',
            icon: 'cog',
            color: '#52c41a',
            bgColor: 'rgba(82, 196, 26, 0.1)',
            description: '工单正在处理中',
            animation: 'spin'
        },
        reviewing: {
            text: '待审核',
            icon: 'search',
            color: '#faad14',
            bgColor: 'rgba(250, 173, 20, 0.1)',
            description: '工单处理完成，等待审核'
        },
        callback: {
            text: '待回访',
            icon: 'phone',
            color: '#722ed1',
            bgColor: 'rgba(114, 46, 209, 0.1)',
            description: '工单已审核通过，等待回访'
        },
        closed: {
            text: '已关闭',
            icon: 'check-circle',
            color: '#52c41a',
            bgColor: 'rgba(82, 196, 26, 0.1)',
            description: '工单已完成并关闭'
        },
        cancelled: {
            text: '已取消',
            icon: 'times-circle',
            color: '#ff4d4f',
            bgColor: 'rgba(255, 77, 79, 0.1)',
            description: '工单已取消'
        }
    },
    
    // 紧急程度配置
    urgencyConfig: {
        normal: {
            text: '一般',
            icon: 'circle',
            color: '#52c41a',
            bgColor: 'rgba(82, 196, 26, 0.1)',
            level: 1,
            description: '普通工单'
        },
        urgent: {
            text: '紧急',
            icon: 'exclamation-triangle',
            color: '#faad14',
            bgColor: 'rgba(250, 173, 20, 0.1)',
            level: 2,
            description: '紧急工单，需要优先处理',
            animation: 'pulse'
        },
        critical: {
            text: '特急',
            icon: 'fire',
            color: '#ff4d4f',
            bgColor: 'rgba(255, 77, 79, 0.1)',
            level: 3,
            description: '特急工单，需要立即处理',
            animation: 'pulse'
        }
    },
    
    // 处理模式配置
    modeConfig: {
        instant: {
            text: '即时办结',
            icon: 'bolt',
            color: '#52c41a',
            bgColor: 'rgba(82, 196, 26, 0.1)',
            description: '即时办结，当场解决'
        },
        normal: {
            text: '普通流转',
            icon: 'share',
            color: '#1890ff',
            bgColor: 'rgba(24, 144, 255, 0.1)',
            description: '按正常流程流转处理'
        },
        cooperation: {
            text: '主协办',
            icon: 'handshake',
            color: '#722ed1',
            bgColor: 'rgba(114, 46, 209, 0.1)',
            description: '需要多个部门协同处理'
        }
    },
    
    // 督办级别配置
    superviseConfig: {
        none: {
            text: '',
            icon: '',
            color: '',
            bgColor: '',
            description: '无督办'
        },
        general: {
            text: '一般督办',
            icon: 'eye',
            color: '#1890ff',
            bgColor: 'rgba(24, 144, 255, 0.1)',
            description: '一般督办工单'
        },
        important: {
            text: '重点督办',
            icon: 'exclamation',
            color: '#faad14',
            bgColor: 'rgba(250, 173, 20, 0.1)',
            description: '重点督办工单',
            animation: 'pulse'
        },
        leader: {
            text: '领导督办',
            icon: 'crown',
            color: '#ff4d4f',
            bgColor: 'rgba(255, 77, 79, 0.1)',
            description: '领导督办工单',
            animation: 'pulse'
        }
    },
    
    // VIP类型配置
    vipConfig: {
        normal: {
            text: '',
            icon: '',
            color: '',
            bgColor: ''
        },
        vip: {
            text: 'VIP',
            icon: 'star',
            color: '#faad14',
            bgColor: 'rgba(250, 173, 20, 0.1)',
            description: 'VIP客户'
        },
        important: {
            text: '重要客户',
            icon: 'crown',
            color: '#722ed1',
            bgColor: 'rgba(114, 46, 209, 0.1)',
            description: '重要客户'
        },
        leader: {
            text: '领导关注',
            icon: 'shield-alt',
            color: '#ff4d4f',
            bgColor: 'rgba(255, 77, 79, 0.1)',
            description: '领导关注客户'
        }
    },
    
    /**
     * 初始化状态标识系统
     */
    init: function() {
        this.createStatusStyles();
        this.bindEvents();
    },
    
    /**
     * 创建状态样式
     */
    createStatusStyles: function() {
        const style = document.createElement('style');
        style.id = 'status-indicator-styles';
        
        let css = `
            /* 状态徽章基础样式 */
            .status-badge, .urgency-badge, .mode-badge, .supervise-badge, .vip-badge {
                display: inline-flex;
                align-items: center;
                gap: 4px;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
                white-space: nowrap;
                border: 1px solid transparent;
                transition: all 0.3s ease;
            }
            
            .status-badge i, .urgency-badge i, .mode-badge i, .supervise-badge i, .vip-badge i {
                font-size: 10px;
            }
            
            /* 动画效果 */
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.6; }
            }
            
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            
            .pulse { animation: pulse 2s infinite; }
            .spin { animation: spin 2s linear infinite; }
            
            /* 悬停效果 */
            .status-badge:hover, .urgency-badge:hover, .mode-badge:hover, 
            .supervise-badge:hover, .vip-badge:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            
            /* 时限徽章 */
            .time-limit-badge {
                padding: 2px 6px;
                border-radius: 8px;
                font-size: 11px;
                font-weight: 600;
                white-space: nowrap;
            }
            
            .time-limit-badge.normal {
                background: rgba(82, 196, 26, 0.1);
                color: #52c41a;
            }
            
            .time-limit-badge.warning {
                background: rgba(250, 173, 20, 0.1);
                color: #faad14;
            }
            
            .time-limit-badge.danger {
                background: rgba(255, 77, 79, 0.1);
                color: #ff4d4f;
                animation: pulse 2s infinite;
            }
            
            /* 工单行高亮 */
            .ticket-table tbody tr.urgent {
                border-left: 3px solid #faad14;
                background: rgba(250, 173, 20, 0.02);
            }
            
            .ticket-table tbody tr.critical {
                border-left: 3px solid #ff4d4f;
                background: rgba(255, 77, 79, 0.02);
                animation: highlight 3s ease-in-out infinite;
            }
            
            @keyframes highlight {
                0%, 100% { background: rgba(255, 77, 79, 0.02); }
                50% { background: rgba(255, 77, 79, 0.05); }
            }
            
            /* 督办标识 */
            .supervise-indicator {
                position: relative;
            }
            
            .supervise-indicator::before {
                content: '';
                position: absolute;
                top: -2px;
                right: -2px;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #ff4d4f;
                animation: pulse 2s infinite;
            }
            
            /* 新工单标识 */
            .new-ticket-indicator {
                position: relative;
            }
            
            .new-ticket-indicator::after {
                content: 'NEW';
                position: absolute;
                top: -8px;
                right: -8px;
                background: #ff4d4f;
                color: white;
                font-size: 8px;
                padding: 1px 3px;
                border-radius: 6px;
                font-weight: bold;
            }
            
            /* 响应式调整 */
            @media (max-width: 768px) {
                .status-badge, .urgency-badge, .mode-badge, .supervise-badge, .vip-badge {
                    font-size: 10px;
                    padding: 1px 6px;
                    gap: 2px;
                }
                
                .status-badge i, .urgency-badge i, .mode-badge i, .supervise-badge i, .vip-badge i {
                    font-size: 8px;
                }
            }
        `;
        
        // 为每种状态生成具体样式
        Object.entries(this.statusConfig).forEach(([key, config]) => {
            css += `
                .status-badge.${key} {
                    color: ${config.color};
                    background: ${config.bgColor};
                    border-color: ${config.color}33;
                }
            `;
            
            if (config.animation) {
                css += `.status-badge.${key} i { animation: ${config.animation} 2s infinite; }`;
            }
        });
        
        Object.entries(this.urgencyConfig).forEach(([key, config]) => {
            css += `
                .urgency-badge.${key} {
                    color: ${config.color};
                    background: ${config.bgColor};
                    border-color: ${config.color}33;
                }
            `;
            
            if (config.animation) {
                css += `.urgency-badge.${key} { animation: ${config.animation} 2s infinite; }`;
            }
        });
        
        Object.entries(this.modeConfig).forEach(([key, config]) => {
            css += `
                .mode-badge.${key} {
                    color: ${config.color};
                    background: ${config.bgColor};
                    border-color: ${config.color}33;
                }
            `;
        });
        
        Object.entries(this.superviseConfig).forEach(([key, config]) => {
            if (key !== 'none') {
                css += `
                    .supervise-badge.${key} {
                        color: ${config.color};
                        background: ${config.bgColor};
                        border-color: ${config.color}33;
                    }
                `;
                
                if (config.animation) {
                    css += `.supervise-badge.${key} { animation: ${config.animation} 2s infinite; }`;
                }
            }
        });
        
        Object.entries(this.vipConfig).forEach(([key, config]) => {
            if (key !== 'normal') {
                css += `
                    .vip-badge.${key} {
                        color: ${config.color};
                        background: ${config.bgColor};
                        border-color: ${config.color}33;
                    }
                `;
            }
        });
        
        style.textContent = css;
        document.head.appendChild(style);
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 徽章悬停显示详细信息
        document.addEventListener('mouseenter', (e) => {
            if (e.target.matches('.status-badge, .urgency-badge, .mode-badge, .supervise-badge, .vip-badge')) {
                this.showTooltip(e.target);
            }
        }, true);
        
        document.addEventListener('mouseleave', (e) => {
            if (e.target.matches('.status-badge, .urgency-badge, .mode-badge, .supervise-badge, .vip-badge')) {
                this.hideTooltip();
            }
        }, true);
    },
    
    /**
     * 显示工具提示
     * @param {Element} element 目标元素
     */
    showTooltip: function(element) {
        const description = this.getElementDescription(element);
        if (!description) return;
        
        // 移除现有的工具提示
        this.hideTooltip();
        
        // 创建工具提示
        const tooltip = document.createElement('div');
        tooltip.className = 'status-tooltip';
        tooltip.textContent = description;
        
        // 设置样式
        Object.assign(tooltip.style, {
            position: 'absolute',
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '6px 10px',
            borderRadius: '4px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            zIndex: '9999',
            pointerEvents: 'none',
            opacity: '0',
            transition: 'opacity 0.2s ease'
        });
        
        document.body.appendChild(tooltip);
        
        // 计算位置
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        let left = rect.left + (rect.width - tooltipRect.width) / 2;
        let top = rect.top - tooltipRect.height - 8;
        
        // 边界检查
        if (left < 10) left = 10;
        if (left + tooltipRect.width > window.innerWidth - 10) {
            left = window.innerWidth - tooltipRect.width - 10;
        }
        if (top < 10) {
            top = rect.bottom + 8;
        }
        
        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';
        
        // 显示动画
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 10);
        
        this.currentTooltip = tooltip;
    },
    
    /**
     * 隐藏工具提示
     */
    hideTooltip: function() {
        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    },
    
    /**
     * 获取元素描述
     * @param {Element} element 目标元素
     * @returns {string} 描述文本
     */
    getElementDescription: function(element) {
        const classList = element.classList;
        
        if (classList.contains('status-badge')) {
            const status = Array.from(classList).find(cls => this.statusConfig[cls]);
            return status ? this.statusConfig[status].description : '';
        }
        
        if (classList.contains('urgency-badge')) {
            const urgency = Array.from(classList).find(cls => this.urgencyConfig[cls]);
            return urgency ? this.urgencyConfig[urgency].description : '';
        }
        
        if (classList.contains('mode-badge')) {
            const mode = Array.from(classList).find(cls => this.modeConfig[cls]);
            return mode ? this.modeConfig[mode].description : '';
        }
        
        if (classList.contains('supervise-badge')) {
            const supervise = Array.from(classList).find(cls => this.superviseConfig[cls]);
            return supervise ? this.superviseConfig[supervise].description : '';
        }
        
        if (classList.contains('vip-badge')) {
            const vip = Array.from(classList).find(cls => this.vipConfig[cls]);
            return vip ? this.vipConfig[vip].description : '';
        }
        
        return '';
    },
    
    /**
     * 创建状态徽章
     * @param {string} type 徽章类型
     * @param {string} value 状态值
     * @param {Object} options 选项
     * @returns {string} HTML字符串
     */
    createBadge: function(type, value, options = {}) {
        const configs = {
            status: this.statusConfig,
            urgency: this.urgencyConfig,
            mode: this.modeConfig,
            supervise: this.superviseConfig,
            vip: this.vipConfig
        };
        
        const config = configs[type] && configs[type][value];
        if (!config || (type === 'supervise' && value === 'none') || (type === 'vip' && value === 'normal')) {
            return '';
        }
        
        const className = `${type}-badge ${value}`;
        const icon = config.icon ? `<i class="fas fa-${config.icon}"></i>` : '';
        const text = options.hideText ? '' : config.text;
        
        return `
            <span class="${className}" title="${config.description || ''}">
                ${icon}
                ${text ? `<span>${text}</span>` : ''}
            </span>
        `;
    },
    
    /**
     * 创建时限徽章
     * @param {Object} timeLimit 时限信息
     * @returns {string} HTML字符串
     */
    createTimeLimitBadge: function(timeLimit) {
        if (!timeLimit || !timeLimit.remaining) return '';
        
        const level = timeLimit.warningLevel || 'normal';
        
        return `
            <span class="time-limit-badge ${level}" title="剩余时间: ${timeLimit.remaining}">
                ${timeLimit.remaining}
            </span>
        `;
    },
    
    /**
     * 更新工单行样式
     * @param {Element} row 表格行元素
     * @param {Object} ticket 工单数据
     */
    updateRowStyle: function(row, ticket) {
        if (!row || !ticket) return;
        
        // 移除现有的紧急程度类
        row.classList.remove('urgent', 'critical');
        
        // 添加紧急程度类
        if (ticket.urgency === 'urgent') {
            row.classList.add('urgent');
        } else if (ticket.urgency === 'critical') {
            row.classList.add('critical');
        }
        
        // 添加督办标识
        if (ticket.supervise && ticket.supervise !== 'none') {
            row.classList.add('supervise-indicator');
        }
        
        // 添加新工单标识（创建时间在24小时内）
        const createTime = new Date(ticket.createTime);
        const now = new Date();
        const hoursDiff = (now - createTime) / (1000 * 60 * 60);
        
        if (hoursDiff <= 24) {
            row.classList.add('new-ticket-indicator');
        }
    },
    
    /**
     * 获取状态配置
     * @param {string} type 类型
     * @param {string} value 值
     * @returns {Object} 配置对象
     */
    getConfig: function(type, value) {
        const configs = {
            status: this.statusConfig,
            urgency: this.urgencyConfig,
            mode: this.modeConfig,
            supervise: this.superviseConfig,
            vip: this.vipConfig
        };
        
        return configs[type] && configs[type][value];
    },
    
    /**
     * 获取状态优先级
     * @param {string} status 状态
     * @returns {number} 优先级
     */
    getStatusPriority: function(status) {
        const priorities = {
            critical: 5,
            urgent: 4,
            leader: 3,
            important: 2,
            general: 1,
            normal: 0
        };
        
        return priorities[status] || 0;
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.StatusIndicator.init();
});
