<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手 - 12345智慧政务平台</title>
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/base.css">
    <!-- 布局样式 -->
    <link rel="stylesheet" href="css/layout.css">
    <!-- 组件样式 -->
    <link rel="stylesheet" href="css/components/agent-status.css">
    <link rel="stylesheet" href="css/components/softphone.css">
    <link rel="stylesheet" href="css/components/left-panel.css">
    <link rel="stylesheet" href="css/components/center-panel.css">
    <link rel="stylesheet" href="css/components/right-panel.css">
    <link rel="stylesheet" href="css/components/buttons.css">
    <link rel="stylesheet" href="css/components/drawer.css">
    <!-- 动画样式 -->
    <link rel="stylesheet" href="css/animations.css">
</head>

<body>
    <!-- 坐席状态与小休提醒栏 -->
    <div class="agent-status-bar">
        <div class="status-section">
            <div class="status-indicator">
                <span class="status-dot" id="statusDot"></span>
                <span class="status-text" id="statusText">空闲</span>
            </div>
            <div class="work-timer">
                <span class="timer-label">当前状态时长：</span>
                <span class="timer-value" id="statusTimer">00:00:00</span>
            </div>
            <div class="work-duration">
                <span class="duration-label">连续工作时长：</span>
                <span class="duration-value" id="workDuration">01:45:32</span>
            </div>
        </div>



        <div class="incoming-call-indicator" id="incomingCallIndicator" style="display: none;">
            <div class="call-pulse"></div>
            <div class="call-text">📞 电话呼入中...</div>
        </div>

        <div class="softphone-tools">
            <div class="call-controls">
                <button class="btn-call btn-answer" id="answerBtn" title="接听" style="display: none;">
                    <span class="icon">📞</span>
                    <span class="label">接听</span>
                </button>
                <button class="btn-call btn-hangup" id="hangupBtn" title="挂断" style="display: none;">
                    <span class="icon">📵</span>
                    <span class="label">挂断</span>
                </button>
                <button class="btn-call btn-mute" id="muteBtn" title="静音" style="display: none;">
                    <span class="icon">🔇</span>
                    <span class="label">静音</span>
                </button>
                <button class="btn-call btn-hold" id="holdBtn" title="保持" style="display: none;">
                    <span class="icon">⏸️</span>
                    <span class="label">保持</span>
                </button>
                <button class="btn-call btn-transfer" id="transferBtn" title="转接" style="display: none;">
                    <span class="icon">📲</span>
                    <span class="label">转接</span>
                </button>
                <button class="btn-call btn-record" id="recordBtn" title="录音" style="display: none;">
                    <span class="icon">🎙️</span>
                    <span class="label">录音</span>
                </button>
            </div>
            <div class="call-timer" id="callTimer" style="display: none;">
                <span class="timer-icon">⏱️</span>
                <span class="timer-text" id="callTimerText">00:00</span>
            </div>
        </div>

        <div class="status-controls">
            <button class="btn-status" id="breakBtn" data-status="break">小休</button>
            <button class="btn-status" id="busyBtn" data-status="busy">忙碌</button>
            <button class="btn-status" id="availableBtn" data-status="available">空闲</button>
            <button class="btn-simulate-call" id="simulateCallBtn" title="模拟来电">
                <span class="icon">📞</span>
                <span class="label">模拟来电</span>
            </button>
        </div>
    </div>

    <div class="container">
        <!-- 左栏：客户身份与核心信息 -->
        <div class="left-panel">
            <div class="customer-info-card">
                <h3>市民基本信息</h3>
                <!-- 占位符状态 -->
                <div class="customer-info-placeholder" id="customerInfoPlaceholder">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">👤</div>
                        <div class="placeholder-text">等待来电...</div>
                        <div class="placeholder-desc">接通电话后将显示市民基本信息</div>
                    </div>
                </div>
                <!-- 实际内容 -->
                <div class="customer-info-content" id="customerInfoContent" style="display: none;">
                    <!-- 左侧：市民基本信息 -->
                    <div class="customer-basic-section">
                        <div class="customer-basic">
                            <div class="customer-name">张女士</div>
                            <div class="phone-number">138****1234</div>
                            <div class="location">📍 广东 广州</div>
                            <div class="tags">
                                <span class="tag vip">VIP</span>
                                <span class="tag love">爱心市民</span>
                                <span class="tag frequent">高频来电</span>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：统计信息摘要 -->
                    <div class="statistics-section">
                        <div class="statistics-summary">
                            <h4>统计信息摘要</h4>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="label">来电总次数：</span>
                                    <span class="value">12次</span>
                                </div>
                                <div class="stat-item">
                                    <span class="label">历史工单总数：</span>
                                    <span class="value">5件</span>
                                </div>
                                <div class="stat-item">
                                    <span class="label">未完成工单：</span>
                                    <span class="value urgent">1件</span>
                                </div>
                                <div class="stat-item">
                                    <span class="label">平均满意度：</span>
                                    <span class="value rating">4.8 ★★★★☆</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="address-info">
                <h4>📍 关联地址信息</h4>
                <!-- 占位符状态 -->
                <div class="address-placeholder" id="addressPlaceholder">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">📍</div>
                        <div class="placeholder-text">暂无地址信息</div>
                        <div class="placeholder-desc">接通电话后将显示市民的常用地址和历史工单地址</div>
                    </div>
                </div>
                <!-- 实际内容 -->
                <div class="address-content" id="addressContent" style="display: none;">
                    <div class="address-item">
                        <label>🏠 常用地址</label>
                        <a href="#" class="address-link" data-tooltip="点击在地图中查看">📍 天河区珠江新城华夏路10号富力中心A栋2501室</a>
                    </div>
                    <div class="history-addresses">
                        <label>📋 历史工单地址</label>
                        <ul>
                            <li>
                                🕒 <a href="#" class="address-link" data-tooltip="上次报修路灯的地址">📍 天河区体育西路191号</a>
                                <span class="address-note">(路灯报修)</span>
                            </li>
                            <li>
                                🕒 <a href="#" class="address-link" data-tooltip="上次投诉噪音的地址">📍 天河公园</a>
                                <span class="address-note">(噪音投诉)</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 转人工信息卡片 -->
            <div class="transfer-info">
                <h4>📞 转人工信息</h4>
                <!-- 占位符状态 -->
                <div class="transfer-placeholder" id="transferPlaceholder">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">📞</div>
                        <div class="placeholder-text">等待来电...</div>
                        <div class="placeholder-desc">接通电话后将显示转人工相关信息</div>
                    </div>
                </div>
                <!-- 实际内容 -->
                <div class="transfer-content" id="transferContent" style="display: none;">
                    <!-- 传统模式内容 -->
                    <div class="traditional-mode" id="traditionalMode">
                        <div class="ivr-path">
                            <label>🎯 IVR路径</label>
                            <div class="path-chain">
                                <span class="path-step">主菜单</span>
                                <span class="path-arrow">→</span>
                                <span class="path-step">市政服务</span>
                                <span class="path-arrow">→</span>
                                <span class="path-step">排水问题</span>
                                <span class="path-arrow">→</span>
                                <span class="path-step current">转人工</span>
                            </div>
                        </div>
                        <div class="queue-info">
                            <label>⏱️ 历史排队信息</label>
                            <div class="queue-stats">
                                <div class="queue-item">
                                    <span class="queue-label">排队时长：</span>
                                    <span class="queue-value">2分15秒</span>
                                </div>
                                <div class="queue-item">
                                    <span class="queue-label">排队开始：</span>
                                    <span class="queue-value">12:25:30</span>
                                </div>
                                <div class="queue-item">
                                    <span class="queue-label">接通时间：</span>
                                    <span class="queue-value">12:27:45</span>
                                </div>
                                <div class="queue-item">
                                    <span class="queue-label">排队位置：</span>
                                    <span class="queue-value">第4位</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 智能模式内容 -->
                    <div class="intelligent-mode" id="intelligentMode" style="display: none;">
                        <div class="conversation-summary">
                            <label>🤖 智能对话摘要</label>
                            <div class="summary-content">
                                <div class="summary-item">
                                    <span class="summary-label">识别意图：</span>
                                    <span class="summary-value">市政排水问题投诉</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">情绪分析：</span>
                                    <span class="summary-value emotion-calm">平静理性</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">紧急程度：</span>
                                    <span class="summary-value priority-medium">中等</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">转人工原因：</span>
                                    <span class="summary-value">需要现场勘查确认</span>
                                </div>
                            </div>
                        </div>
                        <div class="ai-recommendation">
                            <label>💡 AI建议</label>
                            <div class="recommendation-content">
                                <p>建议分配给市政排水专员处理，预计处理时间3-5个工作日</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <button class="btn btn-primary" id="newTicketBtn" disabled>新建工单</button>
                <button class="btn btn-secondary" id="linkHistoryBtn" disabled>关联历史工单</button>
                <button class="btn btn-secondary" id="editTagsBtn" disabled>添加/修改用户标签</button>
            </div>
        </div>

        <!-- 中栏：交互历史与动态工作区 -->
        <div class="center-panel">
            <!-- 紧凑的历史工单概览 -->
            <div class="history-overview">
                <div class="history-header">
                    <h3>历史工单概览</h3>
                    <button class="btn-link expand-history">展开全部</button>
                </div>
                <!-- 占位符状态 -->
                <div class="history-placeholder" id="historyPlaceholder">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">📋</div>
                        <div class="placeholder-text">暂无历史工单</div>
                        <div class="placeholder-desc">接通电话后将显示该市民的历史工单</div>
                    </div>
                </div>
                <!-- 实际内容 -->
                <div class="ticket-summary" id="ticketSummary" style="display: none;">·
                    <div class="ticket-item closed">
                        <span class="ticket-id">2025071600123</span>
                        <span class="ticket-title">富力中心南门路灯不亮</span>
                        <span class="ticket-status">【已关闭】</span>
                        <div class="ticket-actions-mini">
                            <button class="btn-mini">详情</button>
                            <button class="btn-mini">重启</button>
                        </div>
                    </div>
                    <div class="ticket-item processing">
                        <span class="ticket-id">2025071500089</span>
                        <span class="ticket-title">天河公园广场舞音响过大</span>
                        <span class="ticket-status">【处理中】</span>
                        <div class="ticket-actions-mini">
                            <button class="btn-mini">详情</button>
                            <button class="btn-mini">重启</button>
                        </div>
                    </div>
                    <div class="ticket-item closed">
                        <span class="ticket-id">2025071200056</span>
                        <span class="ticket-title">华夏路人行道井盖松动</span>
                        <span class="ticket-status">【已关闭】</span>
                        <div class="ticket-actions-mini">
                            <button class="btn-mini">详情</button>
                            <button class="btn-mini">重启</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dynamic-workspace">

                <!-- 双核信息呈现区域 -->
                <div class="dual-info-display" id="dualInfoDisplay">
                    <!-- 实时语音转写框 -->
                    <div class="voice-transcript-box">
                        <div class="transcript-header">
                            <h4>实时语音转写</h4>
                            <div class="transcript-status">
                                <span class="status-indicator"></span>
                                <span class="status-text">等待通话开始</span>
                            </div>
                        </div>
                        <div class="transcript-content" id="transcriptContent" style="display: none;">
                            <div class="transcript-message agent">
                                <span class="speaker">坐席：</span>
                                <span class="message">您好，这里是12345政务服务热线，请问有什么可以帮助您的？</span>
                                <span class="timestamp">14:30:15</span>
                            </div>
                            <div class="transcript-message citizen">
                                <span class="speaker">市民：</span>
                                <span class="message">你好，我想反映一下我们小区门口的积水问题...</span>
                                <span class="timestamp">14:30:22</span>
                            </div>
                        </div>
                        <div class="transcript-placeholder" id="transcriptPlaceholder">
                            <div class="placeholder-text">等待语音输入...</div>
                            <div class="placeholder-desc">接通电话后将实时显示对话内容</div>
                        </div>
                    </div>

                    <!-- 实时对话摘要框 -->
                    <div class="conversation-summary-box">
                        <div class="summary-header">
                            <h4>实时对话摘要</h4>
                            <div class="summary-status">
                                <span class="status-indicator"></span>
                                <span class="status-text">等待通话开始</span>
                            </div>
                        </div>
                        <div class="summary-content" id="summaryContent">
                            <div class="summary-item">
                                <span class="summary-label">核心诉求：</span>
                                <span class="summary-value" id="coreRequest" data-empty="true">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">问题详情：</span>
                                <span class="summary-value" id="problemDetails" data-empty="true">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">情绪状态：</span>
                                <span class="summary-value" id="emotionState" data-empty="true">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">紧急程度：</span>
                                <span class="summary-value" id="urgencyLevel" data-empty="true">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">建议分类：</span>
                                <span class="summary-value" id="suggestedCategory" data-empty="true">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">推荐承办：</span>
                                <span class="summary-value" id="recommendedDept" data-empty="true">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">关键信息：</span>
                                <span class="summary-value" id="keyInfo" data-empty="true">--</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">决策建议：</span>
                                <span class="summary-value" id="decisionSuggestion" data-empty="true">--</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 智能填充的工单草稿 (点击新建工单后显示) -->
                <div class="smart-ticket-draft" id="smartTicketDraft" style="display: none;">
                    <div class="draft-header">
                        <h4>智能填充工单草稿</h4>
                        <div class="draft-status">
                            <span class="status-indicator success"></span>
                            <span class="status-text">已智能填充完毕</span>
                        </div>
                    </div>
                    <div class="draft-form">
                        <div class="form-group">
                            <label>问题分类：</label>
                            <input type="text" value="市政设施 -> 排水设施" class="auto-filled">
                        </div>
                        <div class="form-group">
                            <label>事发地址：</label>
                            <input type="text" value="富力中心南门" class="auto-filled">
                        </div>
                        <div class="form-group">
                            <label>问题描述：</label>
                            <textarea class="auto-filled">富力中心南门积水严重，深度约20-30厘米，影响居民出行，需要及时处理排水问题</textarea>
                        </div>
                        <div class="form-group">
                            <label>主办单位：</label>
                            <input type="text" value="市政排水管理处" class="auto-filled">
                        </div>
                        <div class="form-group">
                            <label>协办单位：</label>
                            <input type="text" placeholder="可手动添加协办单位">
                        </div>
                        <div class="form-group">
                            <label>紧急程度：</label>
                            <select class="auto-filled">
                                <option value="low">低</option>
                                <option value="medium" selected>中等</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮区 -->
                <div class="action-buttons" id="actionButtons" style="display: none;">
                    <button class="btn btn-secondary" id="saveDraftBtn">暂存草稿</button>
                    <button class="btn btn-warning" id="resetFormBtn">重新填写</button>
                    <button class="btn btn-info" id="instantCloseBtn">即时办结</button>
                    <button class="btn btn-success" id="confirmSubmitBtn">确认派发</button>
                </div>
            </div>
        </div>

        <!-- 右栏：智能辅助与知识库 -->
        <div class="right-panel">
            <div class="ai-assistance" id="aiAssistance">
                <h3>智能辅助实时推送</h3>
                <div class="keywords" id="keywordsContent" style="display: none;">
                    <label>关键词识别：</label>
                    <span class="keyword">积水</span>
                    <span class="keyword">内涝</span>
                    <span class="keyword">排水</span>
                </div>
                <div class="intent" id="intentContent" style="display: none;">
                    <label>意图判断：</label>
                    <span class="intent-result">市政排水问题投诉 (置信度: 95%)</span>
                </div>
                <div class="ai-placeholder" id="aiPlaceholder">
                    <div class="placeholder-text">等待通话开始...</div>
                    <div class="placeholder-desc">接通电话后将实时显示关键词识别和意图判断</div>
                </div>
            </div>

            <div class="knowledge-base" id="knowledgeBase">
                <h4>知识库推荐列表</h4>
                <div class="kb-content" id="kbContent" style="display: none;">
                    <div class="kb-item best-match">
                        <span class="match-label">【最匹配】</span>
                        <span class="kb-type">文章</span>
                        <div class="kb-title">《城市内涝应急处置标准作业流程(SOP)》</div>
                    </div>
                    <div class="kb-item">
                        <span class="kb-type">文章</span>
                        <div class="kb-title">《各区市政排水管理处联系方式及职责范围》</div>
                    </div>
                    <div class="kb-item">
                        <span class="kb-type">FAQ</span>
                        <div class="kb-title">"如何界定道路积水的严重等级？"</div>
                    </div>
                </div>
                <div class="kb-placeholder" id="kbPlaceholder">
                    <div class="placeholder-text">暂无推荐内容</div>
                    <div class="placeholder-desc">系统将根据通话内容智能推荐相关知识库</div>
                </div>
            </div>

            <div class="standard-scripts" id="standardScripts">
                <h4>标准化话术推荐</h4>
                <div class="scripts-content" id="scriptsContent" style="display: none;">
                    <div class="script-item">
                        <label>安抚话术：</label>
                        <p>"您先别着急，请注意安全，我们会立即为您处理。"</p>
                    </div>
                    <div class="script-item">
                        <label>信息采集话术：</label>
                        <p>"请问积水最深处大概在什么位置？是否影响到居民楼一楼？"</p>
                    </div>
                </div>
                <div class="scripts-placeholder" id="scriptsPlaceholder">
                    <div class="placeholder-text">暂无话术推荐</div>
                    <div class="placeholder-desc">系统将根据对话场景推荐合适的标准化话术</div>
                </div>
            </div>

            <div class="similar-tickets" id="similarTickets">
                <h4>历史相似工单</h4>
                <div class="similar-content" id="similarContent" style="display: none;">
                    <div class="similar-info">
                        系统发现近期在本区域有3件相似的'积水'工单，<a href="#" class="link">点击查看处理方案</a>。
                    </div>
                </div>
                <div class="similar-placeholder" id="similarPlaceholder">
                    <div class="placeholder-text">暂无相似工单</div>
                    <div class="placeholder-desc">系统将自动检索历史相似案例供参考</div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript模块 -->
    <script src="js/modules/agent-status.js"></script>
    <script src="js/modules/softphone.js"></script>
    <script src="js/modules/dual-info.js"></script>
    <script src="js/modules/ticket-manager.js"></script>
    <script src="js/modules/ui-effects.js"></script>
    <script src="js/modules/notification.js"></script>
    <script src="js/modules/event-handlers.js"></script>
    <script src="js/modules/drawer.js"></script>
    <!-- 主应用文件 -->
    <script src="js/main.js"></script>
</body>

</html>