/**
 * 表单通用样式
 * 包含输入框、选择框、文本域等表单元素样式
 */

/* 基础表单元素 */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-family: inherit;
    line-height: 1.5;
    background-color: var(--bg-white);
    color: var(--text-color);
    transition: all var(--transition-normal);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
    outline: none;
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: var(--primary-hover);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
    background-color: var(--bg-gray);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.form-input[readonly] {
    background-color: var(--bg-gray);
    cursor: default;
}

/* 输入框 */
.form-input {
    height: 40px;
}

.form-input::placeholder {
    color: var(--text-disabled);
}

/* 选择框 */
.form-select {
    height: 40px;
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
    appearance: none;
}

.form-select:disabled {
    cursor: not-allowed;
}

/* 可搜索选择框 */
.form-select.searchable {
    position: relative;
}

/* 文本域 */
.form-textarea {
    min-height: 80px;
    resize: vertical;
    font-family: inherit;
}

.form-textarea::placeholder {
    color: var(--text-disabled);
}

/* 单选按钮组 */
.radio-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.radio-button {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    background-color: var(--bg-white);
    min-height: 40px;
    gap: var(--spacing-xs);
}

.radio-button:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.radio-button input[type="radio"] {
    display: none;
}

.radio-button input[type="radio"]:checked + .radio-icon + .radio-text,
.radio-button input[type="radio"]:checked + .radio-text {
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

.radio-button input[type="radio"]:checked ~ * {
    color: var(--primary-color);
}

.radio-button:has(input[type="radio"]:checked) {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
    box-shadow: 0 0 0 1px var(--primary-color);
}

.radio-icon {
    font-size: var(--font-size-lg);
    margin-right: var(--spacing-xs);
}

.radio-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    color: var(--text-color);
    transition: all var(--transition-normal);
}

.radio-desc {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    display: block;
}

/* 紧急程度单选按钮 */
.priority-group .radio-button {
    min-width: 80px;
    justify-content: center;
}

.priority-normal:has(input:checked) {
    border-color: var(--priority-normal);
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--priority-normal);
}

.priority-urgent:has(input:checked) {
    border-color: var(--priority-urgent);
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--priority-urgent);
}

.priority-critical:has(input:checked) {
    border-color: var(--priority-critical);
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--priority-critical);
}

/* 复选框组 */
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: background-color var(--transition-normal);
}

.checkbox-label:hover {
    background-color: var(--bg-gray);
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    position: relative;
    appearance: none;
    background-color: var(--bg-white);
    transition: all var(--transition-normal);
}

.checkbox-label input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label input[type="checkbox"]:focus {
    box-shadow: 0 0 0 2px var(--primary-light);
}

.checkbox-text {
    font-size: var(--font-size-sm);
    color: var(--text-color);
    user-select: none;
}

/* 标签云 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
    min-height: 60px;
}

.tag-item {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-gray);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    color: var(--text-color);
    cursor: pointer;
    transition: all var(--transition-normal);
    user-select: none;
}

.tag-item:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.tag-item.selected {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.tag-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 自定义标签 */
.custom-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
}

.custom-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--info-color);
    color: white;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
}

.custom-tag .remove-tag {
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
    transition: opacity var(--transition-fast);
}

.custom-tag .remove-tag:hover {
    opacity: 1;
}

/* 标签列表 */
.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    min-height: 32px;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
}

.tag-list-item {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-gray);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    color: var(--text-color);
}

/* 表单验证状态 */
.form-group.has-error .form-input,
.form-group.has-error .form-select,
.form-group.has-error .form-textarea {
    border-color: var(--error-color);
}

.form-group.has-error .form-input:focus,
.form-group.has-error .form-select:focus,
.form-group.has-error .form-textarea:focus {
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.form-group.has-success .form-input,
.form-group.has-success .form-select,
.form-group.has-success .form-textarea {
    border-color: var(--success-color);
}

.form-group.has-success .form-input:focus,
.form-group.has-success .form-select:focus,
.form-group.has-success .form-textarea:focus {
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

/* 必填字段标识 */
.form-group.required .form-label::after {
    content: ' *';
    color: var(--error-color);
}

/* 表单组合 */
.form-group-inline {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-group-inline .form-label {
    margin-bottom: 0;
    white-space: nowrap;
}

.form-group-inline .form-input,
.form-group-inline .form-select {
    flex: 1;
}

/* 输入框前缀和后缀 */
.input-prefix,
.input-suffix {
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-sm);
    background-color: var(--bg-gray);
    border: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.input-prefix {
    border-right: none;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.input-suffix {
    border-left: none;
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.input-group .form-input {
    border-radius: 0;
}

.input-group .form-input:first-child {
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.input-group .form-input:last-child {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

/* 表单尺寸变体 */
.form-input.large,
.form-select.large,
.form-textarea.large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-md);
}

.form-input.large {
    height: 48px;
}

.form-select.large {
    height: 48px;
}

.form-input.small,
.form-select.small,
.form-textarea.small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.form-input.small {
    height: 32px;
}

.form-select.small {
    height: 32px;
}

/* 只读状态 */
.form-input[readonly],
.form-select[readonly],
.form-textarea[readonly] {
    background-color: var(--bg-gray);
    cursor: default;
}

/* 数字输入框 */
.form-input[type="number"] {
    text-align: right;
}

/* 搜索输入框 */
.form-input[type="search"] {
    padding-right: 40px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
}

/* 文件输入框 */
.form-input[type="file"] {
    padding: var(--spacing-xs);
    height: auto;
    cursor: pointer;
}

.form-input[type="file"]::-webkit-file-upload-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    margin-right: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-gray);
    color: var(--text-color);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.form-input[type="file"]::-webkit-file-upload-button:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}
