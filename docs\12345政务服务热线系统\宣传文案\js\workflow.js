/**
 * 业务流程页面专用脚本
 * 提供流程图交互和动画效果
 */

document.addEventListener('DOMContentLoaded', function() {
    initWorkflowPage();
});

/**
 * 初始化业务流程页面
 */
function initWorkflowPage() {
    // 初始化流程阶段动画
    initStageAnimations();
    
    // 初始化流程导航
    initFlowNavigation();
    
    // 初始化场景切换
    initScenarioSwitching();
    
    // 初始化轨道展示
    initTrackVisualization();
    
    // 初始化协同流程演示
    initCollaborationDemo();
}

/**
 * 初始化流程阶段动画
 */
function initStageAnimations() {
    const stageElements = document.querySelectorAll('.stage-section');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                    animateStageContent(entry.target);
                }, index * 200);
            }
        });
    }, {
        threshold: 0.2,
        rootMargin: '0px 0px -100px 0px'
    });
    
    stageElements.forEach(stage => {
        observer.observe(stage);
    });
}

/**
 * 动画化阶段内容
 */
function animateStageContent(stageElement) {
    const processSteps = stageElement.querySelectorAll('.process-step');
    const trackSteps = stageElement.querySelectorAll('.track-step');
    const scenarios = stageElement.querySelectorAll('.scenario');
    
    // 动画化流程步骤
    processSteps.forEach((step, index) => {
        setTimeout(() => {
            step.style.opacity = '0';
            step.style.transform = 'translateX(-30px)';
            step.style.transition = 'all 0.6s ease-out';
            
            setTimeout(() => {
                step.style.opacity = '1';
                step.style.transform = 'translateX(0)';
            }, 50);
        }, index * 150);
    });
    
    // 动画化轨道步骤
    trackSteps.forEach((step, index) => {
        setTimeout(() => {
            step.style.opacity = '0';
            step.style.transform = 'translateY(20px)';
            step.style.transition = 'all 0.5s ease-out';
            
            setTimeout(() => {
                step.style.opacity = '1';
                step.style.transform = 'translateY(0)';
            }, 50);
        }, index * 100);
    });
    
    // 动画化场景
    scenarios.forEach((scenario, index) => {
        setTimeout(() => {
            scenario.style.opacity = '0';
            scenario.style.transform = 'scale(0.9)';
            scenario.style.transition = 'all 0.5s ease-out';
            
            setTimeout(() => {
                scenario.style.opacity = '1';
                scenario.style.transform = 'scale(1)';
            }, 50);
        }, index * 100);
    });
}

/**
 * 初始化流程导航
 */
function initFlowNavigation() {
    createFlowNavigation();
    bindNavigationEvents();
}

/**
 * 创建流程导航
 */
function createFlowNavigation() {
    const workflowStages = document.querySelector('.workflow-stages');
    
    if (!workflowStages) return;
    
    const navContainer = document.createElement('div');
    navContainer.className = 'flow-navigation';
    navContainer.innerHTML = `
        <div class="nav-container">
            <h3>流程导航</h3>
            <div class="nav-items">
                <a href="#stage-1" class="nav-item" data-stage="1">
                    <span class="nav-number">1</span>
                    <span class="nav-text">市级受理分派</span>
                </a>
                <a href="#stage-2" class="nav-item" data-stage="2">
                    <span class="nav-number">2</span>
                    <span class="nav-text">多轨道流转</span>
                </a>
                <a href="#stage-3" class="nav-item" data-stage="3">
                    <span class="nav-number">3</span>
                    <span class="nav-text">逐级下沉</span>
                </a>
                <a href="#stage-4" class="nav-item" data-stage="4">
                    <span class="nav-number">4</span>
                    <span class="nav-text">逐级审核</span>
                </a>
                <a href="#stage-5" class="nav-item" data-stage="5">
                    <span class="nav-number">5</span>
                    <span class="nav-text">回访关闭</span>
                </a>
            </div>
        </div>
    `;
    
    workflowStages.insertBefore(navContainer, workflowStages.firstChild);
    
    // 为阶段添加ID
    const stageSections = document.querySelectorAll('.stage-section');
    stageSections.forEach((section, index) => {
        section.id = `stage-${index + 1}`;
    });
    
    // 添加导航样式
    addNavigationStyles();
}

/**
 * 添加导航样式
 */
function addNavigationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .flow-navigation {
            position: sticky;
            top: 100px;
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            z-index: 100;
        }
        
        .nav-container h3 {
            margin-bottom: 1rem;
            color: #333;
            font-size: 1.1rem;
            text-align: center;
        }
        
        .nav-items {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            color: #666;
            background: #f8f9fa;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .nav-item:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .nav-number {
            width: 20px;
            height: 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .nav-item:hover .nav-number,
        .nav-item.active .nav-number {
            background: rgba(255,255,255,0.3);
        }
        
        @media (max-width: 768px) {
            .flow-navigation {
                position: relative;
                top: auto;
            }
            
            .nav-items {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-item {
                width: 200px;
                justify-content: center;
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * 绑定导航事件
 */
function bindNavigationEvents() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                // 更新导航状态
                navItems.forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
                
                // 平滑滚动到目标
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // 高亮目标阶段
                highlightStage(targetElement);
            }
        });
    });
    
    // 监听滚动更新导航状态
    window.addEventListener('scroll', throttle(updateNavigationState, 100));
}

/**
 * 更新导航状态
 */
function updateNavigationState() {
    const stageSections = document.querySelectorAll('.stage-section');
    const navItems = document.querySelectorAll('.nav-item');
    
    let currentStage = null;
    
    stageSections.forEach((section, index) => {
        const rect = section.getBoundingClientRect();
        if (rect.top <= 200 && rect.bottom >= 200) {
            currentStage = index + 1;
        }
    });
    
    if (currentStage) {
        navItems.forEach(item => item.classList.remove('active'));
        const activeNav = document.querySelector(`[data-stage="${currentStage}"]`);
        if (activeNav) {
            activeNav.classList.add('active');
        }
    }
}

/**
 * 高亮阶段
 */
function highlightStage(stageElement) {
    // 移除其他阶段的高亮
    document.querySelectorAll('.stage-section').forEach(stage => {
        stage.classList.remove('highlighted');
    });
    
    // 添加当前阶段高亮
    stageElement.classList.add('highlighted');
    
    // 3秒后移除高亮
    setTimeout(() => {
        stageElement.classList.remove('highlighted');
    }, 3000);
    
    // 添加高亮样式
    if (!document.querySelector('#highlight-styles')) {
        const style = document.createElement('style');
        style.id = 'highlight-styles';
        style.textContent = `
            .stage-section.highlighted {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
                transform: scale(1.02);
                transition: all 0.5s ease;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 初始化场景切换
 */
function initScenarioSwitching() {
    const scenarioContainers = document.querySelectorAll('.scenarios');
    
    scenarioContainers.forEach(container => {
        const scenarios = container.querySelectorAll('.scenario');
        
        scenarios.forEach(scenario => {
            scenario.addEventListener('click', function() {
                // 移除其他场景的激活状态
                scenarios.forEach(s => s.classList.remove('active'));
                
                // 激活当前场景
                this.classList.add('active');
                
                // 显示场景详情
                showScenarioDetails(this);
            });
        });
    });
    
    // 添加场景样式
    addScenarioStyles();
}

/**
 * 添加场景样式
 */
function addScenarioStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .scenario {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .scenario:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .scenario.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10;
        }
        
        .scenario.active::after {
            content: '✓';
            position: absolute;
            top: -5px;
            right: -5px;
            width: 25px;
            height: 25px;
            background: #28a745;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 显示场景详情
 */
function showScenarioDetails(scenario) {
    const scenarioTitle = scenario.querySelector('h5').textContent;
    const scenarioContent = scenario.querySelector('p').textContent;
    
    // 创建详情弹窗
    const modal = document.createElement('div');
    modal.className = 'scenario-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${scenarioTitle}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <p>${scenarioContent}</p>
                <div class="scenario-flow">
                    <h4>处理流程：</h4>
                    <div class="flow-steps">
                        ${generateFlowSteps(scenarioTitle)}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 绑定关闭事件
    const closeBtn = modal.querySelector('.close-btn');
    closeBtn.addEventListener('click', () => {
        modal.remove();
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
    
    // 添加模态框样式
    addModalStyles();
}

/**
 * 生成流程步骤
 */
function generateFlowSteps(scenarioTitle) {
    const flowSteps = {
        '场景A：即时办结': [
            '接收市民来电',
            '识别为咨询类诉求',
            '查询知识库',
            '直接答复市民',
            '即时办结工单',
            '进入回访流程'
        ],
        '场景B：派发至市级职能部门': [
            '接收市民来电',
            '识别为市级事务',
            '选择对应职能部门',
            '派发工单',
            '部门内部处理',
            '逐级审核返回'
        ],
        '场景C：派发至区/县级总口': [
            '接收市民来电',
            '识别为属地事务',
            '根据地域属性',
            '派发至区县分中心',
            '区内二次分派',
            '属地处理执行'
        ],
        '场景D：市级层面的主协办协同': [
            '接收复杂工单',
            '识别需要协同',
            '确定主办方',
            '添加协办方',
            '多方并行处理',
            '主办方汇总办结'
        ]
    };
    
    const steps = flowSteps[scenarioTitle] || ['暂无详细流程'];
    
    return steps.map((step, index) => `
        <div class="flow-step">
            <span class="step-number">${index + 1}</span>
            <span class="step-text">${step}</span>
        </div>
    `).join('');
}

/**
 * 添加模态框样式
 */
function addModalStyles() {
    if (document.querySelector('#modal-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'modal-styles';
    style.textContent = `
        .scenario-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }
        
        .modal-content {
            background: white;
            border-radius: 15px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideInUp 0.3s ease;
        }
        
        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h3 {
            margin: 0;
            color: #333;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .scenario-flow h4 {
            margin: 1.5rem 0 1rem;
            color: #333;
        }
        
        .flow-steps {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .step-number {
            width: 25px;
            height: 25px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .step-text {
            color: #555;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * 初始化轨道可视化
 */
function initTrackVisualization() {
    const tracks = document.querySelectorAll('.track');
    
    tracks.forEach(track => {
        track.addEventListener('mouseenter', function() {
            this.classList.add('highlighted');
            
            // 高亮相关轨道步骤
            const trackSteps = this.querySelectorAll('.track-step');
            trackSteps.forEach((step, index) => {
                setTimeout(() => {
                    step.classList.add('pulse');
                }, index * 100);
            });
        });
        
        track.addEventListener('mouseleave', function() {
            this.classList.remove('highlighted');
            
            const trackSteps = this.querySelectorAll('.track-step');
            trackSteps.forEach(step => {
                step.classList.remove('pulse');
            });
        });
    });
    
    // 添加轨道样式
    addTrackStyles();
}

/**
 * 添加轨道样式
 */
function addTrackStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .track.highlighted {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }
        
        .track-step.pulse {
            animation: pulse 1s ease-in-out;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * 初始化协同流程演示
 */
function initCollaborationDemo() {
    const collaborationTypes = document.querySelectorAll('.collaboration-type');
    
    collaborationTypes.forEach(type => {
        const steps = type.querySelectorAll('.collab-step');
        
        type.addEventListener('mouseenter', function() {
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.classList.add('animate');
                }, index * 200);
            });
        });
        
        type.addEventListener('mouseleave', function() {
            steps.forEach(step => {
                step.classList.remove('animate');
            });
        });
    });
    
    // 添加协同样式
    addCollaborationStyles();
}

/**
 * 添加协同样式
 */
function addCollaborationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .collab-step.animate {
            transform: translateX(10px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .collab-step.animate .step-num {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
