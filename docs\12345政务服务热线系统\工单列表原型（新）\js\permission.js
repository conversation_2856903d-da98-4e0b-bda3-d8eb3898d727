/**
 * 角色权限控制模块
 * 根据用户角色控制界面元素的显示和功能权限
 */

window.PermissionManager = {
    // 角色定义
    roles: {
        operator: {
            name: '市级话务员',
            level: 1,
            description: '负责接听电话、录入工单、初步分类'
        },
        manager: {
            name: '区级管理者',
            level: 2,
            description: '负责工单分配、流程监督、质量管控'
        },
        admin: {
            name: '系统管理员',
            level: 3,
            description: '系统管理、用户管理、全局配置'
        },
        executor: {
            name: '执行人员',
            level: 1,
            description: '负责具体工单的处理和执行'
        },
        callback: {
            name: '回访员',
            level: 1,
            description: '负责工单完成后的回访工作'
        }
    },
    
    // 权限定义
    permissions: {
        // 查看权限
        'view_all_tickets': ['admin', 'manager'],
        'view_assigned_tickets': ['operator', 'manager', 'admin', 'executor', 'callback'],
        'view_department_tickets': ['manager', 'admin'],
        'view_ticket_detail': ['operator', 'manager', 'admin', 'executor', 'callback'],
        'view_sensitive_info': ['admin', 'manager'],
        
        // 操作权限
        'create_ticket': ['operator', 'admin'],
        'edit_ticket': ['operator', 'admin'],
        'delete_ticket': ['admin'],
        'assign_ticket': ['manager', 'admin'],
        'accept_ticket': ['executor'],
        'reject_ticket': ['manager', 'admin', 'executor'],
        'close_ticket': ['manager', 'admin'],
        'reopen_ticket': ['admin'],
        
        // 批量操作权限
        'batch_assign': ['manager', 'admin'],
        'batch_approve': ['admin'],
        'batch_reject': ['manager', 'admin'],
        'batch_delete': ['admin'],
        'batch_export': ['operator', 'manager', 'admin', 'executor', 'callback'],
        'batch_print': ['operator', 'manager', 'admin', 'executor', 'callback'],
        
        // 高级功能权限
        'supervise_ticket': ['manager', 'admin'],
        'escalate_ticket': ['manager', 'admin'],
        'transfer_ticket': ['manager', 'admin'],
        'callback_ticket': ['callback', 'admin'],
        
        // 系统功能权限
        'manage_users': ['admin'],
        'manage_departments': ['admin'],
        'view_statistics': ['manager', 'admin'],
        'export_reports': ['manager', 'admin'],
        'system_settings': ['admin']
    },
    
    // 字段可见性配置
    fieldVisibility: {
        operator: {
            visible: ['ticketNo', 'status', 'urgency', 'citizen', 'content', 'timeLimit', 'createTime'],
            hidden: ['mode', 'supervise', 'assignedUnit', 'handler', 'internalNotes']
        },
        manager: {
            visible: ['ticketNo', 'status', 'mode', 'urgency', 'supervise', 'citizen', 'content', 'timeLimit', 'createTime', 'assignedUnit', 'handler'],
            hidden: ['internalNotes']
        },
        admin: {
            visible: ['ticketNo', 'status', 'mode', 'urgency', 'supervise', 'citizen', 'content', 'timeLimit', 'createTime', 'assignedUnit', 'handler', 'internalNotes'],
            hidden: []
        },
        executor: {
            visible: ['ticketNo', 'status', 'urgency', 'citizen', 'content', 'timeLimit', 'assignedUnit', 'handler'],
            hidden: ['mode', 'supervise', 'internalNotes']
        },
        callback: {
            visible: ['ticketNo', 'status', 'citizen', 'content', 'createTime', 'handler'],
            hidden: ['mode', 'supervise', 'urgency', 'timeLimit', 'assignedUnit', 'internalNotes']
        }
    },
    
    // 操作按钮配置
    actionButtons: {
        operator: {
            draft: ['edit', 'assign', 'delete'],
            pending: ['view', 'edit'],
            processing: ['view'],
            reviewing: ['view'],
            callback: ['view'],
            closed: ['view']
        },
        manager: {
            draft: ['edit', 'assign', 'delete'],
            pending: ['assign', 'reject', 'view'],
            processing: ['view', 'supervise', 'transfer'],
            reviewing: ['approve', 'reject', 'view'],
            callback: ['view', 'close'],
            closed: ['view', 'reopen']
        },
        admin: {
            draft: ['edit', 'assign', 'delete'],
            pending: ['assign', 'reject', 'view'],
            processing: ['view', 'supervise', 'transfer', 'escalate'],
            reviewing: ['approve', 'reject', 'view'],
            callback: ['view', 'close'],
            closed: ['view', 'reopen', 'delete']
        },
        executor: {
            draft: ['view'],
            pending: ['accept', 'reject', 'view'],
            processing: ['update', 'submit', 'view'],
            reviewing: ['view'],
            callback: ['view'],
            closed: ['view']
        },
        callback: {
            draft: ['view'],
            pending: ['view'],
            processing: ['view'],
            reviewing: ['view'],
            callback: ['callback', 'view'],
            closed: ['view']
        }
    },
    
    // 当前用户
    currentUser: null,
    
    /**
     * 初始化权限管理
     */
    init: function() {
        this.currentUser = window.currentUser || this.getDefaultUser();
        this.applyPermissions();
        this.bindEvents();
    },
    
    /**
     * 获取默认用户（用于测试）
     */
    getDefaultUser: function() {
        return {
            id: 'user001',
            name: '张三',
            role: 'operator',
            department: '市级话务中心',
            permissions: []
        };
    },
    
    /**
     * 应用权限控制
     */
    applyPermissions: function() {
        this.applyFieldVisibility();
        this.applyButtonVisibility();
        this.applyMenuVisibility();
        this.applyDataFiltering();
    },
    
    /**
     * 应用字段可见性
     */
    applyFieldVisibility: function() {
        const userRole = this.currentUser.role;
        const config = this.fieldVisibility[userRole];
        
        if (!config) return;
        
        // 隐藏不可见的表格列
        config.hidden.forEach(field => {
            const headers = document.querySelectorAll(`th[data-field="${field}"]`);
            const cells = document.querySelectorAll(`td[data-field="${field}"]`);
            
            headers.forEach(header => header.style.display = 'none');
            cells.forEach(cell => cell.style.display = 'none');
        });
        
        // 显示可见的表格列
        config.visible.forEach(field => {
            const headers = document.querySelectorAll(`th[data-field="${field}"]`);
            const cells = document.querySelectorAll(`td[data-field="${field}"]`);
            
            headers.forEach(header => header.style.display = '');
            cells.forEach(cell => cell.style.display = '');
        });
        
        // 处理敏感信息脱敏
        if (!this.hasPermission('view_sensitive_info')) {
            this.applySensitiveInfoMasking();
        }
    },
    
    /**
     * 应用敏感信息脱敏
     */
    applySensitiveInfoMasking: function() {
        // 脱敏市民姓名
        document.querySelectorAll('.citizen-name').forEach(el => {
            const name = el.textContent;
            if (name && name.length > 1) {
                el.textContent = Utils.maskSensitiveInfo(name, 'name');
            }
        });
        
        // 脱敏电话号码
        document.querySelectorAll('.citizen-phone').forEach(el => {
            const phone = el.textContent;
            if (phone && phone.length === 11) {
                el.textContent = Utils.maskSensitiveInfo(phone, 'phone');
            }
        });
        
        // 脱敏身份证号（如果有）
        document.querySelectorAll('.citizen-idcard').forEach(el => {
            const idcard = el.textContent;
            if (idcard && idcard.length >= 15) {
                el.textContent = Utils.maskSensitiveInfo(idcard, 'idcard');
            }
        });
    },
    
    /**
     * 应用按钮可见性
     */
    applyButtonVisibility: function() {
        const userRole = this.currentUser.role;
        
        // 批量操作按钮
        document.querySelectorAll('.batch-btn').forEach(btn => {
            const operation = btn.dataset.operation;
            const permission = `batch_${operation}`;
            
            if (this.hasPermission(permission)) {
                btn.style.display = '';
            } else {
                btn.style.display = 'none';
            }
        });
        
        // 工具栏按钮
        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            const action = btn.dataset.action;
            
            if (this.hasActionPermission(action)) {
                btn.style.display = '';
            } else {
                btn.style.display = 'none';
            }
        });
        
        // 快速筛选标签
        this.applyQuickFilterVisibility();
    },
    
    /**
     * 应用快速筛选可见性
     */
    applyQuickFilterVisibility: function() {
        const userRole = this.currentUser.role;
        
        // 根据角色显示不同的快速筛选选项
        const filterVisibility = {
            operator: ['all', 'my-todo', 'urgent'],
            manager: ['all', 'my-todo', 'urgent', 'overtime', 'supervise'],
            admin: ['all', 'my-todo', 'urgent', 'overtime', 'supervise'],
            executor: ['all', 'my-todo', 'urgent'],
            callback: ['all', 'my-todo']
        };
        
        const visibleFilters = filterVisibility[userRole] || ['all'];
        
        document.querySelectorAll('.filter-tab').forEach(tab => {
            const filter = tab.dataset.filter;
            
            if (visibleFilters.includes(filter)) {
                tab.style.display = '';
            } else {
                tab.style.display = 'none';
            }
        });
    },
    
    /**
     * 应用菜单可见性
     */
    applyMenuVisibility: function() {
        // 高级筛选选项
        if (!this.hasPermission('view_all_tickets')) {
            const advancedFilters = document.querySelectorAll('.advanced-filter-item');
            advancedFilters.forEach(item => {
                const field = item.dataset.field;
                if (['assignedUnit', 'handler', 'supervise'].includes(field)) {
                    item.style.display = 'none';
                }
            });
        }
        
        // 导出选项
        if (!this.hasPermission('export_reports')) {
            const exportBtns = document.querySelectorAll('[data-action="export-advanced"]');
            exportBtns.forEach(btn => btn.style.display = 'none');
        }
    },
    
    /**
     * 应用数据过滤
     */
    applyDataFiltering: function() {
        const userRole = this.currentUser.role;
        
        // 根据角色过滤数据
        if (userRole === 'executor') {
            // 执行人员只能看到分配给自己的工单
            this.setDataFilter('assignedTo', this.currentUser.id);
        } else if (userRole === 'callback') {
            // 回访员只能看到需要回访的工单
            this.setDataFilter('status', 'callback');
        } else if (userRole === 'operator' && !this.hasPermission('view_all_tickets')) {
            // 话务员只能看到自己创建的工单
            this.setDataFilter('createdBy', this.currentUser.id);
        }
    },
    
    /**
     * 设置数据过滤条件
     * @param {string} field 字段名
     * @param {*} value 过滤值
     */
    setDataFilter: function(field, value) {
        if (window.FilterManager) {
            window.FilterManager.setFilter(field, value);
        }
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 监听用户角色变更
        document.addEventListener('userRoleChanged', (e) => {
            this.currentUser = e.detail.user;
            this.applyPermissions();
        });
        
        // 监听表格更新事件
        document.addEventListener('tableUpdated', () => {
            this.applyFieldVisibility();
            this.updateActionButtons();
        });
    },
    
    /**
     * 更新操作按钮
     */
    updateActionButtons: function() {
        const userRole = this.currentUser.role;
        const buttonConfig = this.actionButtons[userRole];
        
        if (!buttonConfig) return;
        
        document.querySelectorAll('tbody tr').forEach(row => {
            const ticketId = row.dataset.ticketId;
            const statusCell = row.querySelector('[data-field="status"]');
            
            if (!statusCell) return;
            
            const status = this.extractStatusFromCell(statusCell);
            const allowedActions = buttonConfig[status] || [];
            
            const actionButtons = row.querySelector('.action-buttons');
            if (actionButtons) {
                this.updateRowActionButtons(actionButtons, allowedActions, ticketId);
            }
        });
    },
    
    /**
     * 从状态单元格提取状态
     * @param {Element} statusCell 状态单元格
     * @returns {string} 状态值
     */
    extractStatusFromCell: function(statusCell) {
        const statusBadge = statusCell.querySelector('.status-badge');
        if (statusBadge) {
            const classList = Array.from(statusBadge.classList);
            return classList.find(cls => ['draft', 'pending', 'processing', 'reviewing', 'callback', 'closed'].includes(cls)) || 'unknown';
        }
        return 'unknown';
    },
    
    /**
     * 更新行操作按钮
     * @param {Element} container 按钮容器
     * @param {Array} allowedActions 允许的操作
     * @param {string} ticketId 工单ID
     */
    updateRowActionButtons: function(container, allowedActions, ticketId) {
        const buttons = container.querySelectorAll('.action-btn');
        
        buttons.forEach(btn => {
            const action = btn.dataset.action;
            
            if (allowedActions.includes(action)) {
                btn.style.display = '';
                btn.disabled = false;
            } else {
                btn.style.display = 'none';
                btn.disabled = true;
            }
        });
    },
    
    /**
     * 检查是否有指定权限
     * @param {string} permission 权限名称
     * @returns {boolean} 是否有权限
     */
    hasPermission: function(permission) {
        if (!this.currentUser) return false;
        
        const allowedRoles = this.permissions[permission];
        if (!allowedRoles) return false;
        
        return allowedRoles.includes(this.currentUser.role);
    },
    
    /**
     * 检查是否有操作权限
     * @param {string} action 操作名称
     * @returns {boolean} 是否有权限
     */
    hasActionPermission: function(action) {
        const permissionMap = {
            'create': 'create_ticket',
            'edit': 'edit_ticket',
            'delete': 'delete_ticket',
            'assign': 'assign_ticket',
            'export': 'batch_export',
            'print': 'batch_print',
            'statistics': 'view_statistics',
            'settings': 'system_settings'
        };
        
        const permission = permissionMap[action];
        return permission ? this.hasPermission(permission) : true;
    },
    
    /**
     * 检查是否可以查看工单
     * @param {Object} ticket 工单对象
     * @returns {boolean} 是否可以查看
     */
    canViewTicket: function(ticket) {
        if (!ticket || !this.currentUser) return false;
        
        const userRole = this.currentUser.role;
        const userId = this.currentUser.id;
        
        // 管理员可以查看所有工单
        if (userRole === 'admin') return true;
        
        // 区级管理者可以查看本部门的工单
        if (userRole === 'manager') {
            return ticket.department === this.currentUser.department || 
                   this.hasPermission('view_all_tickets');
        }
        
        // 执行人员只能查看分配给自己的工单
        if (userRole === 'executor') {
            return ticket.assignedTo === userId;
        }
        
        // 回访员只能查看需要回访的工单
        if (userRole === 'callback') {
            return ticket.status === 'callback';
        }
        
        // 话务员可以查看自己创建的工单
        if (userRole === 'operator') {
            return ticket.createdBy === userId || this.hasPermission('view_all_tickets');
        }
        
        return false;
    },
    
    /**
     * 检查是否可以编辑工单
     * @param {Object} ticket 工单对象
     * @returns {boolean} 是否可以编辑
     */
    canEditTicket: function(ticket) {
        if (!this.hasPermission('edit_ticket')) return false;
        if (!this.canViewTicket(ticket)) return false;
        
        // 只有草稿状态的工单可以编辑
        return ticket.status === 'draft';
    },
    
    /**
     * 获取用户角色信息
     * @returns {Object} 角色信息
     */
    getCurrentRole: function() {
        if (!this.currentUser) return null;
        
        return {
            ...this.roles[this.currentUser.role],
            code: this.currentUser.role
        };
    },
    
    /**
     * 获取用户权限列表
     * @returns {Array} 权限列表
     */
    getUserPermissions: function() {
        if (!this.currentUser) return [];
        
        const userRole = this.currentUser.role;
        const permissions = [];
        
        Object.entries(this.permissions).forEach(([permission, roles]) => {
            if (roles.includes(userRole)) {
                permissions.push(permission);
            }
        });
        
        return permissions;
    },
    
    /**
     * 切换用户角色（用于测试）
     * @param {string} role 新角色
     */
    switchRole: function(role) {
        if (!this.roles[role]) {
            console.error('无效的角色:', role);
            return;
        }
        
        this.currentUser.role = role;
        window.currentUser = this.currentUser;
        
        // 触发角色变更事件
        const event = new CustomEvent('userRoleChanged', {
            detail: { user: this.currentUser }
        });
        document.dispatchEvent(event);
        
        Utils.showMessage(`已切换到角色: ${this.roles[role].name}`, 'info');
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他模块已加载
    setTimeout(() => {
        window.PermissionManager.init();
    }, 200);
});
