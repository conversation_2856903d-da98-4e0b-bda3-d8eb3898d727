/**
 * API接口管理
 */

const API = {
    // 基础配置
    baseURL: CONFIG.api.baseUrl,
    timeout: CONFIG.api.timeout,
    
    // 模拟数据存储
    mockData: {
        tickets: MockData.generateTickets(200),
        currentUser: {
            id: 1,
            name: '张三',
            role: 'municipal_operator',
            department: '市12345中心',
            permissions: ['view', 'create', 'edit', 'dispatch', 'merge', 'split']
        }
    },
    
    /**
     * HTTP请求封装
     */
    async request(url, options = {}) {
        const {
            method = 'GET',
            data = null,
            headers = {},
            timeout = this.timeout
        } = options;
        
        // 模拟网络延迟
        await this.delay(Math.random() * 500 + 200);
        
        try {
            // 这里是模拟实现，实际项目中应该使用真实的HTTP请求
            return await this.mockRequest(url, { method, data, headers });
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    },
    
    /**
     * 模拟请求处理
     */
    async mockRequest(url, options) {
        const { method, data } = options;
        
        // 解析URL和参数
        const [path, queryString] = url.split('?');
        const params = this.parseQueryString(queryString);
        
        switch (path) {
            case '/tickets':
                return this.handleTicketsRequest(method, params, data);
            case '/tickets/stats':
                return this.handleStatsRequest();
            case '/tickets/export':
                return this.handleExportRequest(params);
            case '/user/current':
                return this.handleCurrentUserRequest();
            default:
                throw new Error(`Unknown API endpoint: ${path}`);
        }
    },
    
    /**
     * 处理工单列表请求
     */
    handleTicketsRequest(method, params, data) {
        switch (method) {
            case 'GET':
                return this.getTickets(params);
            case 'POST':
                return this.createTicket(data);
            case 'PUT':
                return this.updateTickets(data);
            case 'DELETE':
                return this.deleteTickets(data);
            default:
                throw new Error(`Unsupported method: ${method}`);
        }
    },
    
    /**
     * 获取工单列表
     */
    getTickets(params = {}) {
        let tickets = [...this.mockData.tickets];
        
        // 应用筛选
        if (params.status) {
            tickets = tickets.filter(t => t.status === params.status);
        }
        
        if (params.urgency) {
            tickets = tickets.filter(t => t.urgency === params.urgency);
        }
        
        if (params.mode) {
            tickets = tickets.filter(t => t.mode === params.mode);
        }
        
        if (params.department) {
            tickets = tickets.filter(t => t.department.includes(params.department));
        }
        
        if (params.area) {
            tickets = tickets.filter(t => t.area.includes(params.area));
        }
        
        if (params.search) {
            const searchTerm = params.search.toLowerCase();
            tickets = tickets.filter(t => 
                t.ticketNumber.toLowerCase().includes(searchTerm) ||
                t.title.toLowerCase().includes(searchTerm) ||
                t.citizen.name.toLowerCase().includes(searchTerm) ||
                t.description.toLowerCase().includes(searchTerm)
            );
        }
        
        // 应用排序
        if (params.sortField) {
            const order = params.sortOrder === 'desc' ? -1 : 1;
            tickets.sort((a, b) => {
                let aVal = this.getNestedValue(a, params.sortField);
                let bVal = this.getNestedValue(b, params.sortField);
                
                if (typeof aVal === 'string') aVal = aVal.toLowerCase();
                if (typeof bVal === 'string') bVal = bVal.toLowerCase();
                
                if (aVal < bVal) return -1 * order;
                if (aVal > bVal) return 1 * order;
                return 0;
            });
        } else {
            // 默认按优先级和创建时间排序
            tickets.sort((a, b) => {
                if (a.priority !== b.priority) {
                    return b.priority - a.priority;
                }
                return b.createTime - a.createTime;
            });
        }
        
        // 应用分页
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || CONFIG.pagination.defaultPageSize;
        const total = tickets.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const data = tickets.slice(start, end);
        
        return {
            success: true,
            data: data,
            pagination: {
                page: page,
                pageSize: pageSize,
                total: total,
                totalPages: Math.ceil(total / pageSize)
            }
        };
    },
    
    /**
     * 创建工单
     */
    createTicket(ticketData) {
        const newTicket = {
            ...MockData.generateTicket(this.mockData.tickets.length + 1),
            ...ticketData,
            id: this.mockData.tickets.length + 1,
            createTime: Date.now()
        };
        
        this.mockData.tickets.unshift(newTicket);
        
        return {
            success: true,
            data: newTicket,
            message: '工单创建成功'
        };
    },
    
    /**
     * 批量更新工单
     */
    updateTickets(updateData) {
        const { ids, updates } = updateData;
        const updatedTickets = [];
        
        ids.forEach(id => {
            const ticket = this.mockData.tickets.find(t => t.id === id);
            if (ticket) {
                Object.assign(ticket, updates);
                updatedTickets.push(ticket);
            }
        });
        
        return {
            success: true,
            data: updatedTickets,
            message: `成功更新 ${updatedTickets.length} 个工单`
        };
    },
    
    /**
     * 批量删除工单
     */
    deleteTickets(deleteData) {
        const { ids } = deleteData;
        const deletedCount = ids.length;
        
        this.mockData.tickets = this.mockData.tickets.filter(t => !ids.includes(t.id));
        
        return {
            success: true,
            data: { deletedCount },
            message: `成功删除 ${deletedCount} 个工单`
        };
    },
    
    /**
     * 获取统计数据
     */
    handleStatsRequest() {
        const stats = MockData.generateStats(this.mockData.tickets);
        
        return {
            success: true,
            data: stats
        };
    },
    
    /**
     * 处理导出请求
     */
    handleExportRequest(params) {
        // 模拟导出处理
        const tickets = this.getTickets(params).data;
        
        return {
            success: true,
            data: {
                downloadUrl: '/downloads/tickets_export.xlsx',
                filename: `工单导出_${UTILS.formatTime(Date.now(), 'YYYYMMDD_HHmmss')}.xlsx`,
                count: tickets.length
            },
            message: '导出任务已创建'
        };
    },
    
    /**
     * 获取当前用户信息
     */
    handleCurrentUserRequest() {
        return {
            success: true,
            data: this.mockData.currentUser
        };
    },
    
    /**
     * 工单合并
     */
    async mergeTickets(mainTicketId, mergeTicketIds, reason) {
        await this.delay(1000);
        
        const mainTicket = this.mockData.tickets.find(t => t.id === mainTicketId);
        const mergeTickets = this.mockData.tickets.filter(t => mergeTicketIds.includes(t.id));
        
        if (!mainTicket || mergeTickets.length === 0) {
            throw new Error('工单不存在');
        }
        
        // 更新主工单
        mainTicket.relationType = 'parent';
        mainTicket.relationCount = mergeTickets.length;
        mainTicket.title = `【合并工单】${mainTicket.title}`;
        
        // 更新被合并工单
        mergeTickets.forEach(ticket => {
            ticket.status = 'merged';
            ticket.relationType = 'merged';
            ticket.parentTicketId = mainTicket.ticketNumber;
        });
        
        return {
            success: true,
            data: { mainTicket, mergeTickets },
            message: `成功合并 ${mergeTickets.length} 个工单`
        };
    },
    
    /**
     * 工单拆分
     */
    async splitTicket(ticketId, splitPlan) {
        await this.delay(1000);
        
        const originalTicket = this.mockData.tickets.find(t => t.id === ticketId);
        if (!originalTicket) {
            throw new Error('工单不存在');
        }
        
        // 创建子工单
        const subTickets = splitPlan.map((plan, index) => {
            const subTicket = {
                ...MockData.generateTicket(this.mockData.tickets.length + index + 1),
                id: this.mockData.tickets.length + index + 1,
                title: plan.title,
                description: plan.description,
                department: plan.department,
                urgency: plan.urgency || originalTicket.urgency,
                relationType: 'child',
                parentTicketId: originalTicket.ticketNumber,
                createTime: Date.now()
            };
            return subTicket;
        });
        
        // 更新原工单
        originalTicket.status = 'split';
        originalTicket.relationType = 'parent';
        originalTicket.relationCount = subTickets.length;
        originalTicket.title = `【已拆分】${originalTicket.title}`;
        
        // 添加子工单到列表
        this.mockData.tickets.unshift(...subTickets);
        
        return {
            success: true,
            data: { originalTicket, subTickets },
            message: `成功拆分为 ${subTickets.length} 个子工单`
        };
    },
    
    /**
     * 工具方法
     */
    
    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },
    
    // 解析查询字符串
    parseQueryString(queryString) {
        if (!queryString) return {};
        
        const params = {};
        queryString.split('&').forEach(pair => {
            const [key, value] = pair.split('=');
            if (key && value) {
                params[decodeURIComponent(key)] = decodeURIComponent(value);
            }
        });
        return params;
    },
    
    // 获取嵌套对象值
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    },
    
    /**
     * 公共API方法
     */
    
    // 获取工单列表
    async getTicketList(params = {}) {
        const queryString = Object.entries(params)
            .filter(([key, value]) => value !== null && value !== undefined && value !== '')
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&');
        
        const url = `/tickets${queryString ? '?' + queryString : ''}`;
        return await this.request(url);
    },
    
    // 获取统计数据
    async getStats() {
        return await this.request('/tickets/stats');
    },
    
    // 获取当前用户
    async getCurrentUser() {
        return await this.request('/user/current');
    },
    
    // 导出工单
    async exportTickets(params = {}) {
        const queryString = Object.entries(params)
            .filter(([key, value]) => value !== null && value !== undefined && value !== '')
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&');
        
        const url = `/tickets/export${queryString ? '?' + queryString : ''}`;
        return await this.request(url);
    },
    
    // 批量操作工单
    async batchUpdateTickets(ids, updates) {
        return await this.request('/tickets', {
            method: 'PUT',
            data: { ids, updates }
        });
    },
    
    // 批量删除工单
    async batchDeleteTickets(ids) {
        return await this.request('/tickets', {
            method: 'DELETE',
            data: { ids }
        });
    }
};

// 导出API（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
