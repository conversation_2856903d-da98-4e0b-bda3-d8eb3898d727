/**
 * 流程图缩放和拖拽控制功能
 * 使用CSS transform实现，不产生滚动条
 */

// 缩放和拖拽相关变量
let currentZoom = 1;
let currentX = 0;
let currentY = 0;
let minZoom = 0.3;
let maxZoom = 3;
let zoomStep = 0.1;
let isFullscreen = false;

// 拖拽相关变量
let isDragging = false;
let startX = 0;
let startY = 0;

/**
 * 初始化图表控制功能
 */
function initDiagramControls() {
    const diagramWrapper = document.getElementById('diagram-wrapper');
    
    if (diagramWrapper) {
        // 添加拖拽功能
        diagramWrapper.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', endDrag);
        
        // 添加滚轮缩放功能
        diagramWrapper.addEventListener('wheel', handleWheel, { passive: false });
        
        // 添加键盘快捷键
        document.addEventListener('keydown', handleKeyboard);
        
        // 添加触摸支持
        diagramWrapper.addEventListener('touchstart', handleTouchStart, { passive: false });
        diagramWrapper.addEventListener('touchmove', handleTouchMove, { passive: false });
        diagramWrapper.addEventListener('touchend', handleTouchEnd);
        
        // 防止右键菜单
        diagramWrapper.addEventListener('contextmenu', e => e.preventDefault());
    }
}

/**
 * 放大功能
 */
function zoomIn() {
    if (currentZoom < maxZoom) {
        currentZoom = Math.min(currentZoom + zoomStep, maxZoom);
        applyTransform();
    }
}

/**
 * 缩小功能
 */
function zoomOut() {
    if (currentZoom > minZoom) {
        currentZoom = Math.max(currentZoom - zoomStep, minZoom);
        applyTransform();
    }
}

/**
 * 重置缩放和位置
 */
function resetZoom() {
    currentZoom = 1;
    currentX = 0;
    currentY = 0;
    applyTransform();
}

/**
 * 应用变换（缩放和平移）
 */
function applyTransform() {
    const svg = document.querySelector('#mermaid-diagram svg');
    const zoomInfo = document.getElementById('zoom-info');
    
    if (svg) {
        svg.style.transform = `translate(${currentX}px, ${currentY}px) scale(${currentZoom})`;
        svg.style.transformOrigin = '0 0';
        svg.style.transition = isDragging ? 'none' : 'transform 0.3s ease';
    }
    
    if (zoomInfo) {
        zoomInfo.textContent = `${Math.round(currentZoom * 100)}%`;
    }
}

/**
 * 开始拖拽
 */
function startDrag(e) {
    // 防止在控制按钮上开始拖拽
    if (e.target.closest('.diagram-controls') || e.target.closest('.zoom-info')) {
        return;
    }
    
    isDragging = true;
    startX = e.clientX - currentX;
    startY = e.clientY - currentY;
    
    const diagramWrapper = document.getElementById('diagram-wrapper');
    if (diagramWrapper) {
        diagramWrapper.style.cursor = 'grabbing';
    }
    
    e.preventDefault();
}

/**
 * 拖拽中
 */
function drag(e) {
    if (!isDragging) return;
    
    e.preventDefault();
    
    currentX = e.clientX - startX;
    currentY = e.clientY - startY;
    
    applyTransform();
}

/**
 * 结束拖拽
 */
function endDrag() {
    if (!isDragging) return;
    
    isDragging = false;
    const diagramWrapper = document.getElementById('diagram-wrapper');
    if (diagramWrapper) {
        diagramWrapper.style.cursor = 'grab';
    }
}

/**
 * 处理滚轮缩放
 */
function handleWheel(e) {
    if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
        
        const rect = e.currentTarget.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        const oldZoom = currentZoom;
        
        if (e.deltaY < 0) {
            zoomIn();
        } else {
            zoomOut();
        }
        
        // 以鼠标位置为中心缩放
        if (currentZoom !== oldZoom) {
            const zoomRatio = currentZoom / oldZoom;
            currentX = mouseX - (mouseX - currentX) * zoomRatio;
            currentY = mouseY - (mouseY - currentY) * zoomRatio;
            applyTransform();
        }
    }
}

/**
 * 处理键盘快捷键
 */
function handleKeyboard(e) {
    if ((e.ctrlKey || e.metaKey) && (e.key === '+' || e.key === '=')) {
        e.preventDefault();
        zoomIn();
    }
    
    if ((e.ctrlKey || e.metaKey) && e.key === '-') {
        e.preventDefault();
        zoomOut();
    }
    
    if ((e.ctrlKey || e.metaKey) && e.key === '0') {
        e.preventDefault();
        resetZoom();
    }
    
    if (e.key === 'F11') {
        e.preventDefault();
        toggleFullscreen();
    }
}

// 触摸事件变量
let touchStartX = 0;
let touchStartY = 0;

/**
 * 处理触摸开始
 */
function handleTouchStart(e) {
    if (e.target.closest('.diagram-controls')) return;
    
    if (e.touches.length === 1) {
        const touch = e.touches[0];
        touchStartX = touch.clientX - currentX;
        touchStartY = touch.clientY - currentY;
        isDragging = true;
    }
}

/**
 * 处理触摸移动
 */
function handleTouchMove(e) {
    if (!isDragging || e.touches.length !== 1) return;
    
    e.preventDefault();
    const touch = e.touches[0];
    
    currentX = touch.clientX - touchStartX;
    currentY = touch.clientY - touchStartY;
    
    applyTransform();
}

/**
 * 处理触摸结束
 */
function handleTouchEnd(e) {
    isDragging = false;
}

/**
 * 切换全屏模式
 */
function toggleFullscreen() {
    const container = document.getElementById('diagram-container');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    
    if (!container) return;
    
    isFullscreen = !isFullscreen;
    
    if (isFullscreen) {
        container.classList.add('fullscreen');
        if (fullscreenBtn) {
            fullscreenBtn.innerHTML = '<span>⛶</span>';
            fullscreenBtn.title = '退出全屏 (ESC)';
        }
        document.addEventListener('keydown', handleEscapeKey);
    } else {
        container.classList.remove('fullscreen');
        if (fullscreenBtn) {
            fullscreenBtn.innerHTML = '<span>⛶</span>';
            fullscreenBtn.title = '全屏 (F11)';
        }
        document.removeEventListener('keydown', handleEscapeKey);
    }
}

/**
 * 处理ESC键退出全屏
 */
function handleEscapeKey(event) {
    if (event.key === 'Escape' && isFullscreen) {
        toggleFullscreen();
    }
}

/**
 * 显示帮助弹窗
 */
function showHelp() {
    const helpModal = document.getElementById('help-modal');
    if (helpModal) {
        helpModal.style.display = 'flex';
        
        helpModal.addEventListener('click', function(e) {
            if (e.target === helpModal) {
                hideHelp();
            }
        });
    }
}

/**
 * 隐藏帮助弹窗
 */
function hideHelp() {
    const helpModal = document.getElementById('help-modal');
    if (helpModal) {
        helpModal.style.display = 'none';
    }
}

/**
 * 显示导出选项弹窗
 */
function showExportOptions() {
    const exportModal = document.getElementById('export-modal');
    if (exportModal) {
        exportModal.style.display = 'flex';

        exportModal.addEventListener('click', function(e) {
            if (e.target === exportModal) {
                hideExportOptions();
            }
        });
    }
}

/**
 * 隐藏导出选项弹窗
 */
function hideExportOptions() {
    const exportModal = document.getElementById('export-modal');
    if (exportModal) {
        exportModal.style.display = 'none';
    }
}

/**
 * 执行导出
 */
function executeExport() {
    const svg = document.querySelector('#mermaid-diagram svg');
    if (!svg) {
        alert('没有找到可导出的流程图');
        return;
    }

    // 获取导出选项
    const format = document.querySelector('input[name="exportFormat"]:checked').value;
    const quality = parseInt(document.querySelector('input[name="exportQuality"]:checked').value);
    const background = document.querySelector('input[name="exportBackground"]:checked').value;

    // 隐藏导出选项弹窗
    hideExportOptions();

    // 显示加载提示
    const loadingDiv = createLoadingIndicator();
    document.body.appendChild(loadingDiv);

    // 获取当前阶段名称
    const activeBtn = document.querySelector('.stage-btn.active, .collab-btn.active');
    const stageName = activeBtn ? activeBtn.textContent.trim() : '流程图';

    // 创建临时容器
    const tempContainer = createTempContainer(background);
    const svgClone = prepareSVGForExport(svg);
    tempContainer.appendChild(svgClone);
    document.body.appendChild(tempContainer);

    // 获取背景色
    let backgroundColor = '#ffffff';
    if (background === 'transparent') {
        backgroundColor = null;
    } else if (background === 'current') {
        backgroundColor = '#f8f9fa';
    }

    // 使用html2canvas生成图片
    const bbox = svg.getBBox();
    html2canvas(tempContainer, {
        backgroundColor: backgroundColor,
        scale: quality,
        useCORS: true,
        allowTaint: true,
        logging: false,
        width: bbox.width + 80,
        height: bbox.height + 80
    }).then(canvas => {
        // 清理临时元素
        document.body.removeChild(tempContainer);
        document.body.removeChild(loadingDiv);

        // 下载图片
        downloadImage(canvas, stageName, format);

        // 显示成功提示
        showExportSuccess();
    }).catch(error => {
        console.error('导出失败:', error);
        document.body.removeChild(tempContainer);
        document.body.removeChild(loadingDiv);
        alert('导出失败，请重试');
    });
}

/**
 * 创建加载指示器
 */
function createLoadingIndicator() {
    const loadingDiv = document.createElement('div');
    loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 20px 30px;
        border-radius: 10px;
        z-index: 10001;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 15px;
    `;
    loadingDiv.innerHTML = `
        <div style="width: 20px; height: 20px; border: 2px solid #fff; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        正在生成图片，请稍候...
    `;

    // 添加旋转动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    return loadingDiv;
}

/**
 * 创建临时容器
 */
function createTempContainer(background) {
    const tempContainer = document.createElement('div');
    let bgColor = 'white';
    if (background === 'current') {
        bgColor = '#f8f9fa';
    } else if (background === 'transparent') {
        bgColor = 'transparent';
    }

    tempContainer.style.cssText = `
        position: absolute;
        top: -10000px;
        left: -10000px;
        background: ${bgColor};
        padding: 40px;
        border-radius: 10px;
    `;

    return tempContainer;
}

/**
 * 准备SVG用于导出
 */
function prepareSVGForExport(svg) {
    const svgClone = svg.cloneNode(true);

    // 重置SVG的transform
    svgClone.style.transform = 'none';
    svgClone.style.transformOrigin = 'initial';

    // 确保SVG有正确的尺寸
    const bbox = svg.getBBox();
    svgClone.setAttribute('width', bbox.width);
    svgClone.setAttribute('height', bbox.height);
    svgClone.setAttribute('viewBox', `${bbox.x} ${bbox.y} ${bbox.width} ${bbox.height}`);

    return svgClone;
}

/**
 * 下载图片
 */
function downloadImage(canvas, stageName, format) {
    const link = document.createElement('a');
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    link.download = `${stageName}_${timestamp}.${format}`;

    if (format === 'jpg') {
        link.href = canvas.toDataURL('image/jpeg', 0.9);
    } else {
        link.href = canvas.toDataURL('image/png');
    }

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 显示导出成功提示
 */
function showExportSuccess() {
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #27ae60;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10001;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;
    successDiv.innerHTML = '✅ 图片导出成功！';

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);

    document.body.appendChild(successDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.style.animation = 'slideIn 0.3s ease reverse';
            setTimeout(() => {
                if (successDiv.parentNode) {
                    document.body.removeChild(successDiv);
                }
                if (style.parentNode) {
                    document.head.removeChild(style);
                }
            }, 300);
        }
    }, 3000);
}

/**
 * 重置所有控制状态
 */
function resetDiagramControls() {
    currentZoom = 1;
    currentX = 0;
    currentY = 0;
    isFullscreen = false;
    isDragging = false;

    const container = document.getElementById('diagram-container');
    const fullscreenBtn = document.getElementById('fullscreen-btn');

    if (container) {
        container.classList.remove('fullscreen');
    }

    if (fullscreenBtn) {
        fullscreenBtn.innerHTML = '<span>⛶</span>';
        fullscreenBtn.title = '全屏 (F11)';
    }

    applyTransform();
}

// 导出函数供全局使用
window.zoomIn = zoomIn;
window.zoomOut = zoomOut;
window.resetZoom = resetZoom;
window.toggleFullscreen = toggleFullscreen;
window.showExportOptions = showExportOptions;
window.hideExportOptions = hideExportOptions;
window.executeExport = executeExport;
window.initDiagramControls = initDiagramControls;
window.resetDiagramControls = resetDiagramControls;
window.showHelp = showHelp;
window.hideHelp = hideHelp;
