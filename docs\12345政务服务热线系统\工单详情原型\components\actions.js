/**
 * 12345政务服务热线系统 - 工单操作按钮组件
 * @description 根据工单状态和用户角色动态生成操作按钮
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 操作按钮配置
 * @typedef {Object} ActionConfig
 * @property {string} id - 操作ID
 * @property {string} label - 按钮文本
 * @property {string} icon - 图标类名
 * @property {string} type - 按钮类型 (primary, secondary, success, warning, danger)
 * @property {Array<string>} roles - 允许的用户角色
 * @property {Array<string>} statuses - 允许的工单状态
 * @property {Function} action - 点击事件处理函数
 * @property {boolean} [confirm] - 是否需要确认
 * @property {string} [confirmMessage] - 确认消息
 */

/**
 * 操作按钮组件
 */
const ActionsComponent = {
    /**
     * 操作按钮容器
     */
    container: null,
    
    /**
     * 操作配置列表
     * @type {Array<ActionConfig>}
     */
    actionConfigs: [
        // 编辑工单
        {
            id: 'edit',
            label: '编辑工单',
            icon: 'fas fa-edit',
            type: 'primary',
            roles: ['operator', 'system_admin'],
            statuses: ['draft'],
            action: () => this.editTicket()
        },
        
        // 即时办结
        {
            id: 'complete_immediately',
            label: '即时办结',
            icon: 'fas fa-check-circle',
            type: 'success',
            roles: ['operator', 'admin', 'processor'],
            statuses: ['draft', 'processing'],
            action: () => this.completeImmediately(),
            confirm: true,
            confirmMessage: '确定要即时办结此工单吗？'
        },
        
        // 工单合并
        {
            id: 'merge',
            label: '工单合并',
            icon: 'fas fa-object-group',
            type: 'secondary',
            roles: ['operator', 'admin'],
            statuses: ['draft'],
            action: () => this.mergeTicket()
        },
        
        // 工单分拆
        {
            id: 'split',
            label: '工单分拆',
            icon: 'fas fa-object-ungroup',
            type: 'secondary',
            roles: ['operator', 'admin'],
            statuses: ['draft'],
            action: () => this.splitTicket()
        },
        
        // 接收工单
        {
            id: 'receive',
            label: '接收工单',
            icon: 'fas fa-inbox',
            type: 'primary',
            roles: ['admin'],
            statuses: ['pending'],
            action: () => this.receiveTicket()
        },
        
        // 退回工单
        {
            id: 'return',
            label: '退回工单',
            icon: 'fas fa-undo',
            type: 'warning',
            roles: ['admin'],
            statuses: ['pending'],
            action: () => this.returnTicket(),
            confirm: true,
            confirmMessage: '确定要退回此工单吗？请填写退回原因。'
        },
        
        // 转办工单
        {
            id: 'transfer',
            label: '转办工单',
            icon: 'fas fa-exchange-alt',
            type: 'secondary',
            roles: ['admin'],
            statuses: ['pending', 'processing'],
            action: () => this.transferTicket()
        },
        
        // 指派工单
        {
            id: 'assign',
            label: '指派工单',
            icon: 'fas fa-share',
            type: 'primary',
            roles: ['admin'],
            statuses: ['processing'],
            action: () => this.assignTicket()
        },
        
        // 工单补记
        {
            id: 'add_note',
            label: '工单补记',
            icon: 'fas fa-comment',
            type: 'secondary',
            roles: ['processor', 'admin'],
            statuses: ['processing', 'suspended'],
            action: () => this.addNote()
        },
        
        // 联系客户
        {
            id: 'contact_customer',
            label: '联系客户',
            icon: 'fas fa-phone',
            type: 'secondary',
            roles: ['processor'],
            statuses: ['processing'],
            action: () => this.contactCustomer()
        },
        
        // 邀请协办
        {
            id: 'invite_collaboration',
            label: '邀请协办',
            icon: 'fas fa-users',
            type: 'secondary',
            roles: ['processor', 'admin'],
            statuses: ['processing'],
            action: () => this.inviteCollaboration()
        },
        
        // 申请延期
        {
            id: 'request_extension',
            label: '申请延期',
            icon: 'fas fa-clock',
            type: 'warning',
            roles: ['processor'],
            statuses: ['processing'],
            action: () => this.requestExtension()
        },
        
        // 申请挂起
        {
            id: 'request_suspension',
            label: '申请挂起',
            icon: 'fas fa-pause-circle',
            type: 'warning',
            roles: ['processor'],
            statuses: ['processing'],
            action: () => this.requestSuspension()
        },
        
        // 申请申诉
        {
            id: 'request_appeal',
            label: '申请申诉',
            icon: 'fas fa-exclamation-circle',
            type: 'danger',
            roles: ['processor'],
            statuses: ['processing'],
            action: () => this.requestAppeal()
        },
        
        // 办结工单
        {
            id: 'complete',
            label: '办结工单',
            icon: 'fas fa-check-circle',
            type: 'success',
            roles: ['processor'],
            statuses: ['processing'],
            action: () => this.completeTicket()
        },
        
        // 审核通过
        {
            id: 'approve',
            label: '审核通过',
            icon: 'fas fa-check',
            type: 'success',
            roles: ['reviewer', 'admin'],
            statuses: ['reviewing'],
            action: () => this.approveTicket()
        },
        
        // 审核退回
        {
            id: 'reject',
            label: '审核退回',
            icon: 'fas fa-times',
            type: 'danger',
            roles: ['reviewer', 'admin'],
            statuses: ['reviewing'],
            action: () => this.rejectTicket(),
            confirm: true,
            confirmMessage: '确定要退回此工单吗？请填写退回原因。'
        },
        
        // 执行回访
        {
            id: 'callback',
            label: '执行回访',
            icon: 'fas fa-phone-alt',
            type: 'primary',
            roles: ['callback'],
            statuses: ['callback'],
            action: () => this.executeCallback()
        },
        
        // 关闭工单
        {
            id: 'close',
            label: '关闭工单',
            icon: 'fas fa-lock',
            type: 'secondary',
            roles: ['callback'],
            statuses: ['callback'],
            action: () => this.closeTicket()
        },
        
        // 重启工单
        {
            id: 'reopen',
            label: '重启工单',
            icon: 'fas fa-redo',
            type: 'warning',
            roles: ['callback', 'admin', 'system_admin'],
            statuses: ['callback', 'closed'],
            action: () => this.reopenTicket(),
            confirm: true,
            confirmMessage: '确定要重启此工单吗？请填写重启原因。'
        },
        
        // 督办催办
        {
            id: 'supervise',
            label: '督办催办',
            icon: 'fas fa-eye',
            type: 'danger',
            roles: ['admin', 'system_admin'],
            statuses: ['pending', 'processing', 'reviewing', 'callback'],
            action: () => this.superviseTicket()
        },
        
        // 强制改派
        {
            id: 'force_reassign',
            label: '强制改派',
            icon: 'fas fa-random',
            type: 'danger',
            roles: ['system_admin'],
            statuses: ['pending', 'processing', 'reviewing'],
            action: () => this.forceReassign(),
            confirm: true,
            confirmMessage: '确定要强制改派此工单吗？'
        },
        
        // 废除工单
        {
            id: 'cancel',
            label: '废除工单',
            icon: 'fas fa-ban',
            type: 'danger',
            roles: ['system_admin'],
            statuses: ['draft', 'pending', 'processing'],
            action: () => this.cancelTicket(),
            confirm: true,
            confirmMessage: '确定要废除此工单吗？此操作不可撤销！'
        },

        // 撤回操作
        {
            id: 'withdraw',
            label: '撤回操作',
            icon: 'fas fa-undo-alt',
            type: 'warning',
            roles: ['admin', 'system_admin'],
            statuses: ['pending'],
            action: () => this.withdrawTicket(),
            confirm: true,
            confirmMessage: '确定要撤回此工单的指派吗？'
        },

        // 修改标签
        {
            id: 'modify_tags',
            label: '修改标签',
            icon: 'fas fa-tags',
            type: 'secondary',
            roles: ['processor', 'admin', 'system_admin'],
            statuses: ['processing', 'suspended'],
            action: () => this.modifyTags()
        },

        // 解挂/恢复处理
        {
            id: 'resume_processing',
            label: '解挂/恢复处理',
            icon: 'fas fa-play-circle',
            type: 'success',
            roles: ['processor', 'admin'],
            statuses: ['suspended'],
            action: () => this.resumeProcessing(),
            confirm: true,
            confirmMessage: '确定要恢复处理此工单吗？'
        },

        // 审核延期申请
        {
            id: 'review_extension',
            label: '审核延期申请',
            icon: 'fas fa-clock',
            type: 'primary',
            roles: ['admin', 'system_admin'],
            statuses: ['processing'],
            action: () => this.reviewExtensionRequest()
        },

        // 审核挂起申请
        {
            id: 'review_suspension',
            label: '审核挂起申请',
            icon: 'fas fa-pause-circle',
            type: 'primary',
            roles: ['admin', 'system_admin'],
            statuses: ['processing'],
            action: () => this.reviewSuspensionRequest()
        },

        // 改派工单（管理者）
        {
            id: 'reassign',
            label: '改派工单',
            icon: 'fas fa-user-edit',
            type: 'warning',
            roles: ['admin'],
            statuses: ['processing', 'suspended'],
            action: () => this.reassignTicket(),
            confirm: true,
            confirmMessage: '确定要改派此工单吗？'
        },

        // 接收协办任务
        {
            id: 'accept_collaboration',
            label: '接收协办任务',
            icon: 'fas fa-handshake',
            type: 'primary',
            roles: ['collaborator', 'admin'],
            statuses: ['processing'],
            action: () => this.acceptCollaboration()
        },

        // 提交协办意见
        {
            id: 'submit_collaboration',
            label: '提交协办意见',
            icon: 'fas fa-file-upload',
            type: 'success',
            roles: ['collaborator'],
            statuses: ['processing'],
            action: () => this.submitCollaborationOpinion()
        },

        // 退回协办任务
        {
            id: 'reject_collaboration',
            label: '退回协办任务',
            icon: 'fas fa-times-circle',
            type: 'danger',
            roles: ['collaborator'],
            statuses: ['processing'],
            action: () => this.rejectCollaboration(),
            confirm: true,
            confirmMessage: '确定要拒绝此协办任务吗？请填写拒绝原因。'
        },

        // 汇总办结
        {
            id: 'summary_complete',
            label: '汇总办结',
            icon: 'fas fa-clipboard-check',
            type: 'success',
            roles: ['processor', 'admin'],
            statuses: ['processing'],
            action: () => this.summaryComplete()
        },

        // 强制办结
        {
            id: 'force_complete',
            label: '强制办结',
            icon: 'fas fa-gavel',
            type: 'danger',
            roles: ['system_admin'],
            statuses: ['processing', 'reviewing', 'suspended'],
            action: () => this.forceComplete(),
            confirm: true,
            confirmMessage: '确定要强制办结此工单吗？此操作将跳过正常流程！'
        },

        // 置顶工单
        {
            id: 'pin_ticket',
            label: '置顶工单',
            icon: 'fas fa-thumbtack',
            type: 'secondary',
            roles: ['system_admin'],
            statuses: ['draft', 'pending', 'processing', 'reviewing'],
            action: () => this.pinTicket()
        },

        // 标记督办
        {
            id: 'mark_supervision',
            label: '标记督办',
            icon: 'fas fa-flag',
            type: 'danger',
            roles: ['system_admin'],
            statuses: ['pending', 'processing', 'reviewing'],
            action: () => this.markSupervision()
        },

        // 批量处理
        {
            id: 'batch_process',
            label: '批量处理',
            icon: 'fas fa-layer-group',
            type: 'primary',
            roles: ['system_admin'],
            statuses: ['draft', 'pending', 'processing', 'reviewing'],
            action: () => this.batchProcess()
        },

        // 导出工单
        {
            id: 'export',
            label: '导出',
            icon: 'fas fa-download',
            type: 'secondary',
            roles: ['operator', 'admin', 'processor', 'reviewer', 'callback', 'collaborator', 'system_admin'],
            statuses: ['draft', 'pending', 'processing', 'reviewing', 'suspended', 'callback', 'closed', 'cancelled'],
            action: () => this.exportTicket()
        },

        // 打印工单
        {
            id: 'print',
            label: '打印',
            icon: 'fas fa-print',
            type: 'secondary',
            roles: ['operator', 'admin', 'processor', 'reviewer', 'callback', 'collaborator', 'system_admin'],
            statuses: ['draft', 'pending', 'processing', 'reviewing', 'suspended', 'callback', 'closed', 'cancelled'],
            action: () => this.printTicket()
        }
    ],
    
    /**
     * 初始化操作按钮组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'actionButtons') {
        console.log('初始化操作按钮组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到操作按钮容器元素:', containerId);
            return;
        }
        
        // 渲染操作按钮
        this.render();
    },
    
    /**
     * 渲染操作按钮
     */
    render() {
        if (!this.container) return;

        // 清空容器
        this.container.innerHTML = '';

        // 获取当前用户角色和工单状态
        const currentRole = AppState.currentUser?.role || 'processor';
        const currentStatus = AppState.currentTicket?.status || 'processing';

        // 过滤可用操作
        const availableActions = this.actionConfigs.filter(config => {
            // 检查角色权限
            const hasRolePermission = config.roles.includes(currentRole);
            // 检查状态权限
            const hasStatusPermission = config.statuses.includes(currentStatus);

            return hasRolePermission && hasStatusPermission;
        });

        // 生成按钮元素
        availableActions.forEach(config => {
            const button = this.createActionButton(config);
            this.container.appendChild(button);
        });

        // 如果没有可用操作，显示提示
        if (availableActions.length === 0) {
            const noActionTip = document.createElement('div');
            noActionTip.className = 'no-actions-tip';
            noActionTip.innerHTML = '<i class="fas fa-info-circle"></i> 当前状态下暂无可用操作';
            this.container.appendChild(noActionTip);
        }
    },
    
    /**
     * 创建操作按钮元素
     * @param {ActionConfig} config - 操作配置
     * @returns {HTMLElement} 按钮元素
     */
    createActionButton(config) {
        const button = document.createElement('button');
        button.className = `btn btn-${config.type}`;
        button.innerHTML = `
            <i class="${config.icon}"></i>
            <span>${config.label}</span>
        `;
        
        // 添加点击事件
        button.addEventListener('click', () => {
            if (config.confirm) {
                this.showConfirmDialog(config.confirmMessage, () => {
                    config.action();
                });
            } else {
                config.action();
            }
        });
        
        return button;
    },
    
    /**
     * 显示确认对话框
     * @param {string} message - 确认消息
     * @param {Function} onConfirm - 确认回调
     */
    showConfirmDialog(message, onConfirm) {
        if (confirm(message)) {
            onConfirm();
        }
    },
    
    // 以下是各种操作的实现方法
    
    /**
     * 编辑工单
     */
    editTicket() {
        console.log('编辑工单');
        Utils.showToast('进入编辑模式', 'info');
        AppState.pageState.editMode = true;
        
        // 启用编辑模式
        const editableElements = document.querySelectorAll('.editable');
        editableElements.forEach(element => {
            element.contentEditable = true;
            element.style.backgroundColor = '#fff3cd';
        });
    },
    
    /**
     * 即时办结
     */
    completeImmediately() {
        console.log('即时办结');
        Utils.showToast('正在办结工单...', 'info');
        
        // 模拟API调用
        setTimeout(() => {
            AppState.currentTicket.status = 'closed';
            this.render();
            Utils.showToast('工单已即时办结', 'success');
        }, 1500);
    },
    
    /**
     * 工单合并
     */
    mergeTicket() {
        console.log('工单合并');
        Utils.showToast('打开工单合并界面', 'info');
        // 这里应该打开工单合并的模态框
    },
    
    /**
     * 工单分拆
     */
    splitTicket() {
        console.log('工单分拆');
        Utils.showToast('打开工单分拆界面', 'info');
        // 这里应该打开工单分拆的模态框
    },
    
    /**
     * 接收工单
     */
    receiveTicket() {
        console.log('接收工单');
        Utils.showToast('正在接收工单...', 'info');
        
        setTimeout(() => {
            AppState.currentTicket.status = 'processing';
            this.render();
            Utils.showToast('工单已接收', 'success');
        }, 1000);
    },
    
    /**
     * 退回工单
     */
    returnTicket() {
        console.log('退回工单');
        const reason = prompt('请输入退回原因:');
        if (reason) {
            Utils.showToast('正在退回工单...', 'info');
            setTimeout(() => {
                Utils.showToast('工单已退回', 'success');
            }, 1000);
        }
    },
    
    /**
     * 转办工单
     */
    transferTicket() {
        console.log('转办工单');
        Utils.showToast('打开转办界面', 'info');
        // 这里应该打开转办的模态框
    },
    
    /**
     * 指派工单
     */
    assignTicket() {
        console.log('指派工单');
        Utils.showToast('打开指派界面', 'info');
        // 这里应该打开指派的模态框
    },
    
    /**
     * 工单补记
     */
    addNote() {
        console.log('工单补记');
        // 调用补记组件的添加方法
        if (window.NotesComponent) {
            window.NotesComponent.showAddNoteDialog();
        }
    },
    
    /**
     * 联系客户
     */
    contactCustomer() {
        console.log('联系客户');
        Utils.showToast('正在拨打客户电话...', 'info');
        // 这里应该集成电话系统
    },
    
    /**
     * 邀请协办
     */
    inviteCollaboration() {
        console.log('邀请协办');
        Utils.showToast('打开协办邀请界面', 'info');
        // 这里应该打开协办邀请的模态框
    },
    
    /**
     * 申请延期
     */
    requestExtension() {
        console.log('申请延期');
        Utils.showToast('打开延期申请界面', 'info');
        // 这里应该打开延期申请的模态框
    },
    
    /**
     * 申请挂起
     */
    requestSuspension() {
        console.log('申请挂起');
        Utils.showToast('打开挂起申请界面', 'info');
        // 这里应该打开挂起申请的模态框
    },
    
    /**
     * 申请申诉
     */
    requestAppeal() {
        console.log('申请申诉');
        Utils.showToast('打开申诉申请界面', 'info');
        // 这里应该打开申诉申请的模态框
    },
    
    /**
     * 办结工单
     */
    completeTicket() {
        console.log('办结工单');
        Utils.showToast('打开办结界面', 'info');
        // 这里应该打开办结的模态框
    },
    
    /**
     * 审核通过
     */
    approveTicket() {
        console.log('审核通过');
        Utils.showToast('正在审核通过...', 'info');
        
        setTimeout(() => {
            AppState.currentTicket.status = 'callback';
            this.render();
            Utils.showToast('审核通过，工单进入回访阶段', 'success');
        }, 1000);
    },
    
    /**
     * 审核退回
     */
    rejectTicket() {
        console.log('审核退回');
        const reason = prompt('请输入退回原因:');
        if (reason) {
            Utils.showToast('正在退回工单...', 'info');
            setTimeout(() => {
                AppState.currentTicket.status = 'processing';
                this.render();
                Utils.showToast('工单已退回重新处理', 'warning');
            }, 1000);
        }
    },
    
    /**
     * 执行回访
     */
    executeCallback() {
        console.log('执行回访');
        Utils.showToast('打开回访界面', 'info');
        // 这里应该打开回访的模态框
    },
    
    /**
     * 关闭工单
     */
    closeTicket() {
        console.log('关闭工单');
        Utils.showToast('正在关闭工单...', 'info');
        
        setTimeout(() => {
            AppState.currentTicket.status = 'closed';
            this.render();
            Utils.showToast('工单已关闭', 'success');
        }, 1000);
    },
    
    /**
     * 重启工单
     */
    reopenTicket() {
        console.log('重启工单');
        const reason = prompt('请输入重启原因:');
        if (reason) {
            Utils.showToast('正在重启工单...', 'info');
            setTimeout(() => {
                AppState.currentTicket.status = 'processing';
                this.render();
                Utils.showToast('工单已重启', 'warning');
            }, 1000);
        }
    },
    
    /**
     * 督办催办
     */
    superviseTicket() {
        console.log('督办催办');
        Utils.showToast('打开督办界面', 'info');
        // 这里应该打开督办的模态框
    },
    
    /**
     * 强制改派
     */
    forceReassign() {
        console.log('强制改派');
        Utils.showToast('打开强制改派界面', 'info');
        // 这里应该打开强制改派的模态框
    },
    
    /**
     * 废除工单
     */
    cancelTicket() {
        console.log('废除工单');
        Utils.showToast('正在废除工单...', 'info');

        setTimeout(() => {
            AppState.currentTicket.status = 'cancelled';
            this.render();
            Utils.showToast('工单已废除', 'danger');
        }, 1000);
    },

    /**
     * 撤回操作
     */
    withdrawTicket() {
        console.log('撤回操作');
        const reason = prompt('请输入撤回原因:');
        if (reason) {
            Utils.showToast('正在撤回工单指派...', 'info');
            setTimeout(() => {
                AppState.currentTicket.status = 'draft';
                this.render();
                Utils.showToast('工单指派已撤回', 'warning');
            }, 1000);
        }
    },

    /**
     * 修改标签
     */
    modifyTags() {
        console.log('修改标签');
        Utils.showToast('打开标签修改界面', 'info');
        // 这里应该打开标签修改的模态框
        // 可以调用标签组件的编辑方法
        if (window.TagsComponent) {
            window.TagsComponent.showEditDialog();
        }
    },

    /**
     * 解挂/恢复处理
     */
    resumeProcessing() {
        console.log('解挂/恢复处理');
        Utils.showToast('正在恢复工单处理...', 'info');

        setTimeout(() => {
            AppState.currentTicket.status = 'processing';
            this.render();
            Utils.showToast('工单已恢复处理', 'success');
        }, 1000);
    },

    /**
     * 审核延期申请
     */
    reviewExtensionRequest() {
        console.log('审核延期申请');
        Utils.showToast('打开延期申请审核界面', 'info');
        // 这里应该打开延期申请审核的模态框
        const action = confirm('是否批准延期申请？\n点击"确定"批准，点击"取消"拒绝');
        if (action !== null) {
            Utils.showToast(action ? '延期申请已批准' : '延期申请已拒绝', action ? 'success' : 'warning');
        }
    },

    /**
     * 审核挂起申请
     */
    reviewSuspensionRequest() {
        console.log('审核挂起申请');
        Utils.showToast('打开挂起申请审核界面', 'info');
        // 这里应该打开挂起申请审核的模态框
        const action = confirm('是否批准挂起申请？\n点击"确定"批准，点击"取消"拒绝');
        if (action !== null) {
            if (action) {
                AppState.currentTicket.status = 'suspended';
                this.render();
                Utils.showToast('挂起申请已批准，工单已挂起', 'warning');
            } else {
                Utils.showToast('挂起申请已拒绝', 'info');
            }
        }
    },

    /**
     * 改派工单（管理者）
     */
    reassignTicket() {
        console.log('改派工单');
        Utils.showToast('打开改派界面', 'info');
        // 这里应该打开改派的模态框，与指派类似但权限不同
        const newAssignee = prompt('请输入新的处理人员:');
        if (newAssignee) {
            Utils.showToast(`正在改派给 ${newAssignee}...`, 'info');
            setTimeout(() => {
                Utils.showToast('工单已改派', 'success');
            }, 1000);
        }
    },

    /**
     * 接收协办任务
     */
    acceptCollaboration() {
        console.log('接收协办任务');
        Utils.showToast('正在接收协办任务...', 'info');

        setTimeout(() => {
            Utils.showToast('协办任务已接收', 'success');
            // 更新协办状态
            if (AppState.currentTicket.collaboration) {
                AppState.currentTicket.collaboration.status = 'accepted';
            }
        }, 1000);
    },

    /**
     * 提交协办意见
     */
    submitCollaborationOpinion() {
        console.log('提交协办意见');
        const opinion = prompt('请输入协办意见:');
        if (opinion) {
            Utils.showToast('正在提交协办意见...', 'info');
            setTimeout(() => {
                Utils.showToast('协办意见已提交', 'success');
                // 更新协办状态
                if (AppState.currentTicket.collaboration) {
                    AppState.currentTicket.collaboration.status = 'completed';
                    AppState.currentTicket.collaboration.opinion = opinion;
                }
            }, 1000);
        }
    },

    /**
     * 退回协办任务
     */
    rejectCollaboration() {
        console.log('退回协办任务');
        const reason = prompt('请输入拒绝协办的原因:');
        if (reason) {
            Utils.showToast('正在退回协办任务...', 'info');
            setTimeout(() => {
                Utils.showToast('协办任务已退回', 'warning');
                // 更新协办状态
                if (AppState.currentTicket.collaboration) {
                    AppState.currentTicket.collaboration.status = 'rejected';
                    AppState.currentTicket.collaboration.rejectReason = reason;
                }
            }, 1000);
        }
    },

    /**
     * 汇总办结
     */
    summaryComplete() {
        console.log('汇总办结');
        Utils.showToast('打开汇总办结界面', 'info');
        // 这里应该打开汇总办结的模态框，显示所有协办意见
        const confirmComplete = confirm('确定要汇总办结此工单吗？\n请确认已收集所有协办意见。');
        if (confirmComplete) {
            Utils.showToast('正在汇总办结...', 'info');
            setTimeout(() => {
                AppState.currentTicket.status = 'reviewing';
                this.render();
                Utils.showToast('工单已汇总办结，进入审核阶段', 'success');
            }, 1500);
        }
    },

    /**
     * 强制办结
     */
    forceComplete() {
        console.log('强制办结');
        const reason = prompt('请输入强制办结的原因:');
        if (reason) {
            Utils.showToast('正在强制办结工单...', 'info');
            setTimeout(() => {
                AppState.currentTicket.status = 'closed';
                this.render();
                Utils.showToast('工单已强制办结', 'danger');
            }, 1000);
        }
    },

    /**
     * 置顶工单
     */
    pinTicket() {
        console.log('置顶工单');
        const isPinned = AppState.currentTicket.isPinned || false;

        Utils.showToast(isPinned ? '正在取消置顶...' : '正在置顶工单...', 'info');

        setTimeout(() => {
            AppState.currentTicket.isPinned = !isPinned;
            Utils.showToast(
                AppState.currentTicket.isPinned ? '工单已置顶' : '工单已取消置顶',
                'success'
            );

            // 更新页面显示
            const pinIndicator = document.getElementById('pinIndicator');
            if (pinIndicator) {
                pinIndicator.style.display = AppState.currentTicket.isPinned ? 'block' : 'none';
            }
        }, 500);
    },

    /**
     * 标记督办
     */
    markSupervision() {
        console.log('标记督办');
        const isSupervised = AppState.currentTicket.isSupervised || false;

        Utils.showToast(isSupervised ? '正在取消督办标记...' : '正在标记督办...', 'info');

        setTimeout(() => {
            AppState.currentTicket.isSupervised = !isSupervised;
            Utils.showToast(
                AppState.currentTicket.isSupervised ? '工单已标记为督办' : '工单已取消督办标记',
                AppState.currentTicket.isSupervised ? 'warning' : 'success'
            );

            // 更新页面显示
            const supervisionMark = document.getElementById('supervisionMark');
            if (supervisionMark) {
                supervisionMark.style.display = AppState.currentTicket.isSupervised ? 'block' : 'none';
            }
        }, 500);
    },

    /**
     * 批量处理
     */
    batchProcess() {
        console.log('批量处理');
        Utils.showToast('打开批量处理界面', 'info');
        // 这里应该打开批量处理的模态框
        // 允许选择多个工单并执行相同操作
        alert('批量处理功能\n\n可以选择多个工单并执行以下操作：\n- 批量指派\n- 批量转办\n- 批量标记督办\n- 批量修改标签\n- 批量导出\n\n此功能需要在工单列表页面实现。');
    },

    /**
     * 导出工单
     */
    exportTicket() {
        console.log('导出工单');
        Utils.showToast('正在准备导出文件...', 'info');

        // 模拟导出过程
        setTimeout(() => {
            // 创建导出数据
            const exportData = {
                ticketNumber: AppState.currentTicket?.number || 'GD202412220001',
                title: document.getElementById('ticketTitle')?.textContent || '工单标题',
                description: document.getElementById('ticketDescription')?.textContent || '工单描述',
                status: AppState.currentTicket?.status || 'processing',
                createTime: document.getElementById('createTime')?.textContent || new Date().toLocaleString(),
                creator: document.getElementById('creator')?.textContent || '创建人',
                exportTime: new Date().toLocaleString(),
                exportUser: AppState.currentUser.name
            };

            // 创建下载链接
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `工单_${exportData.ticketNumber}_${new Date().getTime()}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            Utils.showToast('工单已导出', 'success');
        }, 1500);
    },

    /**
     * 打印工单
     */
    printTicket() {
        console.log('打印工单');
        Utils.showToast('正在准备打印...', 'info');

        // 创建打印内容
        const printContent = this.generatePrintContent();

        // 创建新窗口进行打印
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>工单打印 - ${AppState.currentTicket?.number || 'GD202412220001'}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                    .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
                    .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 20px; }
                    .info-item { padding: 5px; border-bottom: 1px solid #eee; }
                    .label { font-weight: bold; color: #333; }
                    .content-section { margin-bottom: 20px; }
                    .content-title { font-weight: bold; color: #333; margin-bottom: 10px; }
                    .content-text { padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9; }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                ${printContent}
                <div class="no-print" style="text-align: center; margin-top: 30px;">
                    <button onclick="window.print()" style="padding: 10px 20px; font-size: 16px;">打印</button>
                    <button onclick="window.close()" style="padding: 10px 20px; font-size: 16px; margin-left: 10px;">关闭</button>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();

        // 延迟一下确保内容加载完成
        setTimeout(() => {
            Utils.showToast('打印窗口已打开', 'success');
        }, 500);
    },

    /**
     * 生成打印内容
     * @returns {string} HTML格式的打印内容
     */
    generatePrintContent() {
        const ticketNumber = AppState.currentTicket?.number || 'GD202412220001';
        const title = document.getElementById('ticketTitle')?.textContent || '工单标题';
        const description = document.getElementById('ticketDescription')?.textContent || '工单描述';
        const createTime = document.getElementById('createTime')?.textContent || new Date().toLocaleString();
        const creator = document.getElementById('creator')?.textContent || '创建人';
        const location = document.getElementById('location')?.textContent || '涉及地点';
        const status = AppState.currentTicket?.status || 'processing';

        const statusNames = {
            draft: '草稿/暂存',
            pending: '待接收',
            processing: '处理中',
            reviewing: '待审核',
            suspended: '挂起',
            callback: '待回访',
            closed: '已关闭',
            cancelled: '已废除'
        };

        return `
            <div class="header">
                <h1>12345政务服务热线系统</h1>
                <h2>工单详情</h2>
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <span class="label">工单编号：</span>${ticketNumber}
                </div>
                <div class="info-item">
                    <span class="label">当前状态：</span>${statusNames[status] || status}
                </div>
                <div class="info-item">
                    <span class="label">创建时间：</span>${createTime}
                </div>
                <div class="info-item">
                    <span class="label">创建人：</span>${creator}
                </div>
                <div class="info-item">
                    <span class="label">涉及地点：</span>${location}
                </div>
                <div class="info-item">
                    <span class="label">打印时间：</span>${new Date().toLocaleString()}
                </div>
            </div>

            <div class="content-section">
                <div class="content-title">诉求标题</div>
                <div class="content-text">${title}</div>
            </div>

            <div class="content-section">
                <div class="content-title">详细描述</div>
                <div class="content-text">${description}</div>
            </div>

            <div class="content-section">
                <div class="content-title">备注</div>
                <div class="content-text">本工单由12345政务服务热线系统生成，打印时间：${new Date().toLocaleString()}</div>
            </div>
        `;
    }
};

// 将ActionsComponent暴露到全局作用域
window.ActionsComponent = ActionsComponent;

// 重写全局的初始化操作按钮函数
function initializeActionButtons() {
    ActionsComponent.initialize();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    ActionsComponent.initialize();
});
