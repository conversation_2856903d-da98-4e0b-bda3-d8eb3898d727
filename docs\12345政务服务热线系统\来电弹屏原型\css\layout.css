/**
 * 布局相关样式
 * 包含容器、面板布局、响应式设计
 */

/* 调整容器顶部边距以适应状态栏 */
.container {
    margin-top: 60px;
    display: flex;
    height: calc(100vh - 60px);
    gap: 16px;
    padding: 16px;
    backdrop-filter: blur(10px);
    overflow: hidden;
}

/* 左栏样式 - 客户身份与核心信息 (30%) */
.left-panel {
    width: 30%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow-y: auto;
    transition: all 0.3s ease;
    height: calc(100vh - 60px - 32px);
    max-height: calc(100vh - 60px - 32px);
}

.left-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 中栏样式 - 交互历史与当前操作区 (45%) */
.center-panel {
    width: 45%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    height: calc(100vh - 60px - 32px);
    max-height: calc(100vh - 60px - 32px);
    overflow-y: auto;
}

.center-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 右栏样式 - 智能辅助与知识库 (25%) */
.right-panel {
    width: 25%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow-y: auto;
    transition: all 0.3s ease;
    height: calc(100vh - 60px - 32px);
    max-height: calc(100vh - 60px - 32px);
}

.right-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .container {
        gap: 12px;
        padding: 12px;
    }
    
    .left-panel, .right-panel {
        width: 28%;
    }
    
    .center-panel {
        width: 44%;
    }
}

@media (max-width: 1200px) {
    .dual-info-display {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .voice-transcript-box,
    .conversation-summary-box {
        height: 250px;
    }
}

@media (max-width: 1600px) {
    .customer-name {
        font-size: 24px;
    }
    
    .phone-number {
        font-size: 18px;
    }
}

@media (max-width: 1400px) {
    .tag {
        padding: 4px 8px;
        font-size: 11px;
    }
    
    .btn {
        padding: 10px 16px;
        font-size: 13px;
    }
}
