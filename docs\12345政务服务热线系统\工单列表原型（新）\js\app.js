/**
 * 主应用程序文件
 * 负责应用初始化、模块协调和全局事件处理
 */

window.App = {
    // 应用配置
    config: {
        version: '1.0.0',
        title: '12345政务服务热线 - 工单管理系统',
        apiBaseUrl: '/api',
        pageSize: 50,
        autoRefreshInterval: 30000, // 30秒自动刷新
        enableRealtime: true
    },
    
    // 应用状态
    state: {
        isInitialized: false,
        currentUser: null,
        isLoading: false,
        lastRefreshTime: null
    },
    
    // 模块引用
    modules: {},
    
    /**
     * 应用初始化
     */
    init: function() {
        console.log('初始化应用...', this.config.title);
        
        try {
            // 检查依赖
            this.checkDependencies();
            
            // 初始化用户信息
            this.initUser();
            
            // 初始化模块
            this.initModules();
            
            // 绑定全局事件
            this.bindGlobalEvents();
            
            // 启动自动刷新
            this.startAutoRefresh();
            
            // 设置初始化完成标志
            this.state.isInitialized = true;
            this.state.lastRefreshTime = new Date();
            
            console.log('应用初始化完成');
            Utils.showMessage('系统加载完成', 'success', 2000);
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            Utils.showMessage('系统初始化失败，请刷新页面重试', 'error', 0);
        }
    },
    
    /**
     * 检查依赖
     */
    checkDependencies: function() {
        const requiredModules = ['Utils', 'mockAPI'];
        const missingModules = [];
        
        requiredModules.forEach(module => {
            if (!window[module]) {
                missingModules.push(module);
            }
        });
        
        if (missingModules.length > 0) {
            throw new Error(`缺少必要模块: ${missingModules.join(', ')}`);
        }
    },
    
    /**
     * 初始化用户信息
     */
    initUser: function() {
        // 模拟用户信息，实际项目中应该从服务器获取
        this.state.currentUser = {
            id: 'user001',
            name: '张三',
            role: 'operator', // operator, manager, admin, executor, callback
            department: '市级话务中心',
            permissions: ['view_tickets', 'create_tickets', 'assign_tickets'],
            avatar: null
        };
        
        // 设置全局用户对象
        window.currentUser = this.state.currentUser;
        
        // 更新用户界面
        this.updateUserInterface();
    },
    
    /**
     * 更新用户界面
     */
    updateUserInterface: function() {
        const user = this.state.currentUser;
        if (!user) return;
        
        // 更新用户名显示
        const userNameEl = document.querySelector('.user-name');
        if (userNameEl) {
            userNameEl.textContent = user.name;
        }
        
        // 更新用户角色显示
        const userRoleEl = document.querySelector('.user-role');
        if (userRoleEl) {
            userRoleEl.textContent = this.getRoleText(user.role);
        }
        
        // 根据权限显示/隐藏功能
        this.updatePermissionBasedUI();
    },
    
    /**
     * 根据权限更新UI
     */
    updatePermissionBasedUI: function() {
        const user = this.state.currentUser;
        if (!user) return;
        
        // 根据角色显示/隐藏批量操作按钮
        const batchButtons = document.querySelectorAll('.batch-btn');
        batchButtons.forEach(btn => {
            const operation = btn.dataset.operation;
            const hasPermission = this.checkPermission(operation);
            
            if (hasPermission) {
                btn.style.display = '';
            } else {
                btn.style.display = 'none';
            }
        });
        
        // 根据角色显示/隐藏表格列
        this.updateTableColumns();
    },
    
    /**
     * 更新表格列显示
     */
    updateTableColumns: function() {
        const user = this.state.currentUser;
        if (!user) return;
        
        // 根据用户角色显示不同的列
        const roleColumnMap = {
            operator: ['checkbox', 'ticketNo', 'status', 'urgency', 'citizen', 'content', 'timeLimit', 'createTime', 'actions'],
            manager: ['checkbox', 'ticketNo', 'status', 'mode', 'urgency', 'supervise', 'citizen', 'content', 'timeLimit', 'createTime', 'actions'],
            admin: ['checkbox', 'ticketNo', 'status', 'mode', 'urgency', 'supervise', 'citizen', 'content', 'timeLimit', 'createTime', 'actions'],
            executor: ['ticketNo', 'status', 'urgency', 'citizen', 'content', 'timeLimit', 'actions'],
            callback: ['ticketNo', 'status', 'citizen', 'content', 'createTime', 'actions']
        };
        
        const visibleColumns = roleColumnMap[user.role] || roleColumnMap.operator;
        
        // 隐藏不需要的列
        document.querySelectorAll('th[data-column], td[data-column]').forEach(cell => {
            const column = cell.dataset.column;
            if (visibleColumns.includes(column)) {
                cell.style.display = '';
            } else {
                cell.style.display = 'none';
            }
        });
    },
    
    /**
     * 检查权限
     * @param {string} operation 操作名称
     * @returns {boolean} 是否有权限
     */
    checkPermission: function(operation) {
        const user = this.state.currentUser;
        if (!user) return false;
        
        // 简化的权限检查逻辑
        const rolePermissions = {
            operator: ['export', 'print'],
            manager: ['assign', 'export', 'print'],
            admin: ['assign', 'approve', 'reject', 'export', 'print', 'delete'],
            executor: ['export', 'print'],
            callback: ['export', 'print']
        };
        
        const permissions = rolePermissions[user.role] || [];
        return permissions.includes(operation);
    },
    
    /**
     * 获取角色文本
     * @param {string} role 角色代码
     * @returns {string} 角色文本
     */
    getRoleText: function(role) {
        const roleMap = {
            operator: '市级话务员',
            manager: '区级管理者',
            admin: '系统管理员',
            executor: '执行人员',
            callback: '回访员'
        };
        return roleMap[role] || role;
    },
    
    /**
     * 初始化模块
     */
    initModules: function() {
        console.log('初始化模块...');
        
        // 初始化筛选管理器
        if (window.FilterManager) {
            window.FilterManager.init();
            this.modules.filter = window.FilterManager;
        }
        
        // 初始化表格管理器
        if (window.TableManager) {
            window.TableManager.init();
            this.modules.table = window.TableManager;
        }
        
        // 初始化批量操作管理器
        if (window.BatchManager) {
            window.BatchManager.init();
            this.modules.batch = window.BatchManager;
        }
        
        console.log('模块初始化完成');
    },
    
    /**
     * 绑定全局事件
     */
    bindGlobalEvents: function() {
        // 窗口大小变化事件
        window.addEventListener('resize', Utils.debounce(() => {
            this.handleWindowResize();
        }, 250));
        
        // 页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // 全局错误处理
        window.addEventListener('error', (e) => {
            this.handleGlobalError(e);
        });
        
        // 侧边栏关闭事件
        const overlay = document.getElementById('overlay');
        if (overlay) {
            overlay.addEventListener('click', () => {
                this.closeSidebar();
            });
        }
        
        // 退出登录事件
        const logoutBtn = document.querySelector('.logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }
    },
    
    /**
     * 处理窗口大小变化
     */
    handleWindowResize: function() {
        // 更新表格布局
        if (this.modules.table) {
            // 可以在这里添加响应式处理逻辑
        }
    },
    
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange: function() {
        if (document.hidden) {
            // 页面隐藏时暂停自动刷新
            this.pauseAutoRefresh();
        } else {
            // 页面显示时恢复自动刷新
            this.resumeAutoRefresh();
            
            // 如果离开时间超过5分钟，自动刷新数据
            const now = new Date();
            const timeDiff = now - this.state.lastRefreshTime;
            if (timeDiff > 5 * 60 * 1000) { // 5分钟
                this.refreshData();
            }
        }
    },
    
    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e 键盘事件
     */
    handleKeyboardShortcuts: function(e) {
        // Ctrl/Cmd + R: 刷新数据
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            this.refreshData();
            return;
        }
        
        // Ctrl/Cmd + F: 聚焦搜索框
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
            return;
        }
        
        // Escape: 关闭侧边栏和对话框
        if (e.key === 'Escape') {
            this.closeSidebar();
            this.closeDialogs();
            return;
        }
        
        // Ctrl/Cmd + A: 全选（在表格区域内）
        if ((e.ctrlKey || e.metaKey) && e.key === 'a' && e.target.closest('.ticket-list-section')) {
            e.preventDefault();
            if (this.modules.table) {
                this.modules.table.toggleSelectAll(true);
            }
            return;
        }
    },
    
    /**
     * 处理全局错误
     * @param {ErrorEvent} e 错误事件
     */
    handleGlobalError: function(e) {
        console.error('全局错误:', e.error);
        
        // 显示用户友好的错误消息
        Utils.showMessage('系统出现错误，请稍后重试', 'error');
        
        // 可以在这里添加错误上报逻辑
    },
    
    /**
     * 关闭侧边栏
     */
    closeSidebar: function() {
        const sidebar = document.getElementById('ticketDetailSidebar');
        const overlay = document.getElementById('overlay');
        
        if (sidebar) sidebar.classList.remove('show');
        if (overlay) overlay.classList.remove('show');
    },
    
    /**
     * 关闭对话框
     */
    closeDialogs: function() {
        document.querySelectorAll('.dialog').forEach(dialog => {
            dialog.classList.remove('show');
        });
        
        const overlay = document.getElementById('overlay');
        if (overlay) overlay.classList.remove('show');
    },
    
    /**
     * 启动自动刷新
     */
    startAutoRefresh: function() {
        if (!this.config.enableRealtime) return;
        
        this.autoRefreshTimer = setInterval(() => {
            if (!document.hidden) {
                this.refreshData(true); // 静默刷新
            }
        }, this.config.autoRefreshInterval);
    },
    
    /**
     * 暂停自动刷新
     */
    pauseAutoRefresh: function() {
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
            this.autoRefreshTimer = null;
        }
    },
    
    /**
     * 恢复自动刷新
     */
    resumeAutoRefresh: function() {
        if (!this.autoRefreshTimer && this.config.enableRealtime) {
            this.startAutoRefresh();
        }
    },
    
    /**
     * 刷新数据
     * @param {boolean} silent 是否静默刷新
     */
    refreshData: function(silent = false) {
        if (!silent) {
            Utils.showMessage('正在刷新数据...', 'info', 1000);
        }
        
        if (this.modules.filter) {
            this.modules.filter.applyFilters();
        }
        
        this.state.lastRefreshTime = new Date();
    },
    
    /**
     * 退出登录
     */
    logout: function() {
        Utils.confirm('确定要退出登录吗？', () => {
            // 清理定时器
            this.pauseAutoRefresh();
            
            // 清理本地数据
            this.cleanup();
            
            // 显示退出消息
            Utils.showMessage('正在退出...', 'info');
            
            // 模拟跳转到登录页
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 1000);
        });
    },
    
    /**
     * 清理资源
     */
    cleanup: function() {
        // 清理定时器
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
        }
        
        // 清理事件监听器
        // 实际项目中可能需要更详细的清理逻辑
        
        console.log('应用资源清理完成');
    },
    
    /**
     * 获取应用信息
     * @returns {Object} 应用信息
     */
    getAppInfo: function() {
        return {
            version: this.config.version,
            title: this.config.title,
            isInitialized: this.state.isInitialized,
            currentUser: this.state.currentUser,
            lastRefreshTime: this.state.lastRefreshTime
        };
    }
};

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保所有模块都已加载
    setTimeout(() => {
        window.App.init();
    }, 100);
});

// 页面卸载前清理资源
window.addEventListener('beforeunload', function() {
    if (window.App) {
        window.App.cleanup();
    }
});

// 导出到全局
window.App = App;
