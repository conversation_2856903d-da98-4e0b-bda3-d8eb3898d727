<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12345政务服务热线 - 工单管理系统</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/table.css">
    <link rel="stylesheet" href="css/responsive.css">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 页面头部 -->
    <header class="page-header">
        <div class="header-content">
            <div class="header-left">
                <h1 class="system-title">
                    <i class="fas fa-phone-alt"></i>
                    12345政务服务热线 - 工单管理系统
                </h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-role" id="userRole">市级话务员</span>
                    <span class="user-name" id="userName">张三</span>
                    <button class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        退出
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 快速筛选区域 -->
        <section class="quick-filter-section">
            <div class="quick-filter-tabs">
                <button class="filter-tab active" data-filter="all">
                    <i class="fas fa-list"></i>
                    全部工单
                    <span class="count">1,234</span>
                </button>
                <button class="filter-tab" data-filter="my-todo">
                    <i class="fas fa-tasks"></i>
                    我的待办
                    <span class="count">23</span>
                </button>
                <button class="filter-tab" data-filter="urgent">
                    <i class="fas fa-exclamation-triangle"></i>
                    紧急工单
                    <span class="count">5</span>
                </button>
                <button class="filter-tab" data-filter="overtime">
                    <i class="fas fa-clock"></i>
                    超时工单
                    <span class="count">12</span>
                </button>
                <button class="filter-tab" data-filter="supervise">
                    <i class="fas fa-eye"></i>
                    督办工单
                    <span class="count">8</span>
                </button>
            </div>
        </section>

        <!-- 搜索筛选区域 -->
        <section class="search-filter-section">
            <div class="search-bar">
                <div class="search-input-group">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="搜索工单编号、市民姓名、联系电话...">
                    <button class="search-btn" onclick="performSearch()">搜索</button>
                </div>
                <button class="advanced-search-btn" onclick="toggleAdvancedSearch()">
                    <i class="fas fa-filter"></i>
                    高级筛选
                </button>
            </div>

            <!-- 高级筛选面板 -->
            <div class="advanced-filter-panel" id="advancedFilterPanel">
                <div class="filter-row">
                    <div class="filter-group">
                        <label>工单状态</label>
                        <select id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="draft">草稿/暂存</option>
                            <option value="pending">待接收</option>
                            <option value="processing">处理中</option>
                            <option value="reviewing">待审核</option>
                            <option value="callback">待回访</option>
                            <option value="closed">已关闭</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>紧急程度</label>
                        <select id="urgencyFilter">
                            <option value="">全部</option>
                            <option value="normal">一般</option>
                            <option value="urgent">紧急</option>
                            <option value="critical">特急</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>处理模式</label>
                        <select id="modeFilter">
                            <option value="">全部模式</option>
                            <option value="instant">即时办结</option>
                            <option value="normal">普通流转</option>
                            <option value="cooperation">主协办模式</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>时间范围</label>
                        <input type="date" id="startDate">
                        <span>至</span>
                        <input type="date" id="endDate">
                    </div>
                </div>
                <div class="filter-actions">
                    <button class="apply-filter-btn" onclick="applyAdvancedFilter()">应用筛选</button>
                    <button class="reset-filter-btn" onclick="resetAdvancedFilter()">重置</button>
                </div>
            </div>
        </section>

        <!-- 工具栏区域 -->
        <section class="toolbar-section">
            <div class="toolbar-left">
                <button class="toolbar-btn primary" onclick="createTicket()">
                    <i class="fas fa-plus"></i>
                    新建工单
                </button>
                <button class="toolbar-btn" onclick="refreshList()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
                <button class="toolbar-btn" onclick="toggleColumnSettings()">
                    <i class="fas fa-columns"></i>
                    列设置
                </button>
            </div>
            <div class="toolbar-center">
                <div class="batch-operations" id="batchOperations" style="display: none;">
                    <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                    <button class="batch-btn" onclick="batchAssign()">
                        <i class="fas fa-share"></i>
                        批量派单
                    </button>
                    <button class="batch-btn" onclick="batchMerge()">
                        <i class="fas fa-compress-alt"></i>
                        批量合并
                    </button>
                    <button class="batch-btn" onclick="batchExport()">
                        <i class="fas fa-download"></i>
                        批量导出
                    </button>
                </div>
            </div>
            <div class="toolbar-right">
                <div class="view-toggle">
                    <button class="view-btn active" data-view="table">
                        <i class="fas fa-table"></i>
                    </button>
                    <button class="view-btn" data-view="card">
                        <i class="fas fa-th-large"></i>
                    </button>
                </div>
                <button class="toolbar-btn" onclick="exportData()">
                    <i class="fas fa-file-export"></i>
                    导出
                </button>
            </div>
        </section>

        <!-- 工单列表区域 -->
        <section class="ticket-list-section">
            <div class="table-container">
                <table class="ticket-table" id="ticketTable">
                    <thead>
                        <tr>
                            <th class="checkbox-col">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th class="sortable" data-sort="ticketNo">
                                工单编号
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="status">
                                状态
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="mode">
                                处理模式
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="urgency">
                                紧急程度
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="supervise">
                                督办标识
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="citizen">
                                市民信息
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="content">
                                工单内容
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="timeLimit">
                                时限状态
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="sortable" data-sort="createTime">
                                创建时间
                                <i class="fas fa-sort"></i>
                            </th>
                            <th class="actions-col">操作</th>
                        </tr>
                    </thead>
                    <tbody id="ticketTableBody">
                        <!-- 工单数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 加载状态 -->
            <div class="loading-state" id="loadingState" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <i class="fas fa-inbox"></i>
                <h3>暂无工单数据</h3>
                <p>当前筛选条件下没有找到相关工单</p>
            </div>

            <!-- 移动端卡片视图 -->
            <div class="mobile-card-view" id="mobileCardView" style="display: none;">
                <!-- 卡片内容将通过JavaScript动态生成 -->
            </div>
        </section>

        <!-- 分页区域 -->
        <section class="pagination-section">
            <div class="pagination-info">
                <span>共 <span id="totalCount">1,234</span> 条记录，每页显示</span>
                <select id="pageSize" onchange="changePageSize()">
                    <option value="20">20</option>
                    <option value="50" selected>50</option>
                    <option value="100">100</option>
                </select>
                <span>条</span>
            </div>
            <div class="pagination-controls">
                <button class="page-btn" onclick="goToPage(1)" id="firstPageBtn">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="page-btn" onclick="goToPrevPage()" id="prevPageBtn">
                    <i class="fas fa-angle-left"></i>
                </button>
                <div class="page-numbers" id="pageNumbers">
                    <!-- 页码将通过JavaScript动态生成 -->
                </div>
                <button class="page-btn" onclick="goToNextPage()" id="nextPageBtn">
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="page-btn" onclick="goToLastPage()" id="lastPageBtn">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </div>
            <div class="pagination-jump">
                <span>跳转到</span>
                <input type="number" id="jumpPageInput" min="1">
                <button onclick="jumpToPage()">确定</button>
            </div>
        </section>
    </main>

    <!-- 工单详情侧边栏 -->
    <aside class="ticket-detail-sidebar" id="ticketDetailSidebar">
        <div class="sidebar-header">
            <h3>工单详情</h3>
            <button class="close-sidebar-btn" onclick="closeSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="sidebar-content" id="sidebarContent">
            <!-- 详情内容将通过JavaScript动态加载 -->
        </div>
    </aside>

    <!-- 遮罩层 -->
    <div class="overlay" id="overlay" onclick="closeSidebar()"></div>

    <!-- 批量派单对话框 -->
    <div class="dialog" id="assignDialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批量派单</h3>
                <button class="dialog-close" onclick="closeDialog()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="dialog-body">
                <div class="selected-tickets-list">
                    <!-- 选中的工单列表 -->
                </div>
                <div class="form-group">
                    <label>承办单位</label>
                    <select id="assignUnit">
                        <option value="">请选择承办单位</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>处理人</label>
                    <select id="assignHandler">
                        <option value="">请先选择承办单位</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>备注</label>
                    <textarea id="assignRemark" placeholder="请输入派单说明（可选）"></textarea>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn-confirm" onclick="confirmAssign()">确认派单</button>
            </div>
        </div>
    </div>

    <!-- 批量审核对话框 -->
    <div class="dialog" id="approveDialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批量审核</h3>
                <button class="dialog-close" onclick="closeDialog()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="dialog-body">
                <div class="selected-tickets-list">
                    <!-- 选中的工单列表 -->
                </div>
                <div class="form-group">
                    <label>审核意见</label>
                    <textarea id="approveRemark" placeholder="请输入审核意见（可选）"></textarea>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn-confirm" onclick="confirmApprove()">确认审核</button>
            </div>
        </div>
    </div>

    <!-- 批量退回对话框 -->
    <div class="dialog" id="rejectDialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批量退回</h3>
                <button class="dialog-close" onclick="closeDialog()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="dialog-body">
                <div class="selected-tickets-list">
                    <!-- 选中的工单列表 -->
                </div>
                <div class="form-group">
                    <label>退回原因</label>
                    <select id="rejectReason">
                        <option value="">请选择退回原因</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>详细说明</label>
                    <textarea id="rejectRemark" placeholder="请详细说明退回原因"></textarea>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn-confirm" onclick="confirmReject()">确认退回</button>
            </div>
        </div>
    </div>

    <!-- 批量删除对话框 -->
    <div class="dialog" id="deleteDialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批量删除</h3>
                <button class="dialog-close" onclick="closeDialog()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="dialog-body">
                <div class="delete-warning">
                    <!-- 删除警告信息 -->
                </div>
                <div class="selected-tickets-list">
                    <!-- 选中的工单列表 -->
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="data/mock-data.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/status-indicator.js"></script>
    <script src="js/filter.js"></script>
    <script src="js/table.js"></script>
    <script src="js/batch.js"></script>
    <script src="js/permission.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
