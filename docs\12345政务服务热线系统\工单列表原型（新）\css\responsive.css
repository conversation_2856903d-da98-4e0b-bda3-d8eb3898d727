/**
 * 响应式样式文件 - 适配不同设备和屏幕尺寸
 */

/* 断点定义 */
/* xs: 0-575px (手机) */
/* sm: 576-767px (大手机) */
/* md: 768-991px (平板) */
/* lg: 992-1199px (小桌面) */
/* xl: 1200px+ (大桌面) */

/* 大桌面优化 (1200px+) */
@media (min-width: 1200px) {
    .main-content {
        max-width: 1600px;
    }
    
    .ticket-table {
        font-size: var(--font-size-md);
    }
    
    .filter-row {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* 小桌面 (992-1199px) */
@media (max-width: 1199px) {
    .main-content {
        max-width: 1200px;
    }
    
    .filter-row {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .ticket-table .hide-lg {
        display: none;
    }
}

/* 平板 (768-991px) */
@media (max-width: 991px) {
    .main-content {
        padding: var(--spacing-md);
    }
    
    .header-content {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .system-title {
        font-size: var(--font-size-md) !important;
    }
    
    .quick-filter-tabs {
        justify-content: center;
    }
    
    .filter-tab {
        flex: 1;
        min-width: 0;
        justify-content: center;
    }
    
    .filter-tab span {
        display: none;
    }
    
    .search-bar {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .search-input-group {
        width: 100%;
    }
    
    .advanced-search-btn {
        width: 100%;
        justify-content: center;
    }
    
    .filter-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .toolbar-section {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .batch-operations {
        width: 100%;
        justify-content: center;
    }
    
    .table-container {
        max-height: 50vh;
    }
    
    .ticket-table .hide-md {
        display: none;
    }
    
    .pagination-section {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }
    
    .pagination-controls {
        order: 1;
    }
    
    .pagination-info {
        order: 2;
    }
    
    .pagination-jump {
        order: 3;
    }
}

/* 大手机 (576-767px) */
@media (max-width: 767px) {
    .main-content {
        padding: var(--spacing-sm);
        gap: var(--spacing-md);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    .header-left,
    .header-right {
        width: 100%;
        justify-content: center;
    }
    
    .system-title {
        font-size: var(--font-size-sm) !important;
        text-align: center;
    }
    
    .user-info {
        justify-content: center;
    }
    
    .quick-filter-section {
        padding: var(--spacing-sm);
    }
    
    .quick-filter-tabs {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .filter-tab {
        width: 100%;
        justify-content: space-between;
        padding: var(--spacing-sm);
    }
    
    .filter-tab .count {
        display: block;
    }
    
    .search-filter-section {
        padding: var(--spacing-sm);
    }
    
    .filter-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .filter-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .apply-filter-btn,
    .reset-filter-btn {
        width: 100%;
    }
    
    .toolbar-section {
        padding: var(--spacing-sm);
    }
    
    .toolbar-btn {
        flex: 1;
        justify-content: center;
    }
    
    .batch-btn {
        flex-direction: column;
        gap: 2px;
        font-size: 10px;
    }
    
    .table-container {
        max-height: 40vh;
    }
    
    .ticket-table {
        font-size: var(--font-size-xs);
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: var(--spacing-xs);
    }
    
    .ticket-table .hide-sm {
        display: none;
    }
    
    /* 简化表格显示 */
    .citizen-info,
    .ticket-content,
    .time-limit-info,
    .flow-info {
        gap: 1px;
    }
    
    .content-title {
        -webkit-line-clamp: 1;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
    
    .action-btn {
        font-size: 10px;
        padding: 1px 4px;
    }
    
    .ticket-detail-sidebar {
        width: 100%;
        right: -100%;
    }
}

/* 小手机 (0-575px) */
@media (max-width: 575px) {
    :root {
        --spacing-xs: 2px;
        --spacing-sm: 4px;
        --spacing-md: 8px;
        --spacing-lg: 12px;
        --spacing-xl: 16px;
    }
    
    .main-content {
        padding: var(--spacing-xs);
        gap: var(--spacing-sm);
    }
    
    .header-content {
        padding: var(--spacing-xs);
    }
    
    .system-title {
        font-size: var(--font-size-xs) !important;
    }
    
    .system-title i {
        display: none;
    }
    
    .user-role {
        font-size: 10px;
        padding: 2px 4px;
    }
    
    .user-name {
        font-size: var(--font-size-xs);
    }
    
    .logout-btn {
        padding: 2px 4px;
        font-size: var(--font-size-xs);
    }
    
    .quick-filter-section,
    .search-filter-section,
    .toolbar-section,
    .ticket-list-section,
    .pagination-section {
        padding: var(--spacing-xs);
        border-radius: var(--border-radius-sm);
    }
    
    .filter-tab {
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
    }
    
    .search-input-group input {
        padding: var(--spacing-xs) var(--spacing-xs) var(--spacing-xs) 24px;
        font-size: var(--font-size-xs);
    }
    
    .search-btn,
    .advanced-search-btn {
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
    }
    
    .filter-group label {
        font-size: var(--font-size-xs);
    }
    
    .filter-group select,
    .filter-group input {
        padding: 2px 4px;
        font-size: var(--font-size-xs);
    }
    
    .toolbar-btn {
        padding: 2px 4px;
        font-size: var(--font-size-xs);
    }
    
    .table-container {
        max-height: 35vh;
    }
    
    .ticket-table {
        font-size: 10px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 2px;
    }
    
    .ticket-table .hide-xs {
        display: none;
    }
    
    /* 极简表格布局 */
    .ticket-table thead th:not(.checkbox-col):not(:first-of-type):not(:nth-of-type(2)):not(.actions-col) {
        display: none;
    }
    
    .ticket-table tbody td:not(.checkbox-col):not(:first-of-type):not(:nth-of-type(2)):not(.actions-col) {
        display: none;
    }
    
    /* 卡片式布局替代表格 */
    .mobile-card-view {
        display: none;
    }

    @media (max-width: 480px) {
        .table-container {
            display: none;
        }

        .mobile-card-view {
            display: block;
            padding: var(--spacing-xs);
        }

        .ticket-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            position: relative;
            transition: all 0.3s ease;
        }

        .ticket-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .ticket-card.selected {
            border-color: var(--primary-color);
            background: rgba(24, 144, 255, 0.05);
        }

        .ticket-card.urgent {
            border-left-color: var(--warning-color);
        }

        .ticket-card.critical {
            border-left-color: var(--error-color);
            animation: pulse 2s infinite;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-sm);
        }

        .card-title {
            font-weight: 600;
            font-size: 14px;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--spacing-xs);
        }

        .card-checkbox {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            width: 18px;
            height: 18px;
        }

        .card-badges {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
            margin-top: 4px;
        }

        .card-content {
            margin-bottom: var(--spacing-sm);
            padding-right: 30px; /* 为复选框留出空间 */
        }

        .card-field {
            display: flex;
            margin-bottom: var(--spacing-xs);
            font-size: 12px;
            align-items: flex-start;
        }

        .card-field-label {
            color: var(--text-secondary);
            width: 60px;
            flex-shrink: 0;
            font-weight: 500;
        }

        .card-field-value {
            color: var(--text-primary);
            flex: 1;
            word-break: break-all;
        }

        .card-field-value.citizen-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .card-field-value.content {
            max-height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--spacing-sm);
            border-top: 1px solid var(--border-color);
        }

        .card-time {
            font-size: 11px;
            color: var(--text-tertiary);
        }

        .card-actions {
            display: flex;
            gap: var(--spacing-xs);
            flex-wrap: wrap;
        }

        .card-action-btn {
            padding: 4px 8px;
            font-size: 10px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background: white;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .card-action-btn:hover {
            background: var(--hover-bg);
            transform: translateY(-1px);
        }

        .card-action-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .card-action-btn.danger {
            background: var(--error-color);
            color: white;
            border-color: var(--error-color);
        }

        .card-action-btn i {
            font-size: 9px;
        }

        /* 新工单指示器 */
        .ticket-card.new-ticket::before {
            content: 'NEW';
            position: absolute;
            top: -6px;
            right: 8px;
            background: var(--error-color);
            color: white;
            font-size: 8px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1;
        }

        /* 督办指示器 */
        .ticket-card.supervise::after {
            content: '';
            position: absolute;
            top: 8px;
            left: -2px;
            width: 8px;
            height: 8px;
            background: var(--error-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        /* VIP客户标识 */
        .ticket-card.vip .card-title::before {
            content: '👑';
            margin-right: 4px;
        }

        /* 时限警告 */
        .ticket-card.time-warning {
            border-left-color: var(--warning-color);
        }

        .ticket-card.time-danger {
            border-left-color: var(--error-color);
        }

        /* 加载状态 */
        .card-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
            color: var(--text-secondary);
        }

        .card-loading i {
            margin-right: 8px;
            animation: spin 1s linear infinite;
        }

        /* 空状态 */
        .card-empty {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .card-empty i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .card-empty h3 {
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        /* 下拉刷新 */
        .pull-refresh {
            text-align: center;
            padding: 20px;
            color: var(--text-secondary);
            font-size: 12px;
        }

        .pull-refresh.active {
            color: var(--primary-color);
        }

        .pull-refresh i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }

        .pull-refresh.active i {
            transform: rotate(180deg);
        }
    }
    
    .pagination-controls {
        gap: 2px;
    }
    
    .page-btn,
    .page-number {
        width: 24px;
        height: 24px;
        font-size: var(--font-size-xs);
        padding: 2px;
    }
    
    .pagination-info {
        font-size: var(--font-size-xs);
    }
    
    .pagination-jump {
        font-size: var(--font-size-xs);
    }
    
    .pagination-jump input {
        width: 40px;
        font-size: var(--font-size-xs);
    }
}

/* 横屏手机优化 */
@media (max-width: 767px) and (orientation: landscape) {
    .table-container {
        max-height: 30vh;
    }
    
    .quick-filter-tabs {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .filter-tab {
        flex: 1;
        min-width: 120px;
    }
}

/* 打印样式 */
@media print {
    .page-header,
    .quick-filter-section,
    .search-filter-section,
    .toolbar-section,
    .pagination-section,
    .ticket-detail-sidebar,
    .overlay {
        display: none !important;
    }
    
    .main-content {
        max-width: none;
        padding: 0;
        gap: 0;
    }
    
    .ticket-list-section {
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
    
    .table-container {
        max-height: none;
        overflow: visible;
    }
    
    .ticket-table {
        font-size: 12px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 4px;
        border: 1px solid var(--border-color);
    }
    
    .action-buttons {
        display: none;
    }
    
    /* 确保所有重要列在打印时显示 */
    .ticket-table .hide-print {
        display: none !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-primary: #000000;
        --text-secondary: #333333;
        --bg-primary: #ffffff;
        --bg-secondary: #f8f8f8;
    }
    
    .status-badge,
    .urgency-badge,
    .mode-badge,
    .supervise-badge {
        border-width: 2px;
        font-weight: 600;
    }
}

/* 对话框响应式样式 */
@media (max-width: 768px) {
    .dialog {
        padding: var(--spacing-sm);
    }

    .dialog-content {
        width: 100%;
        max-width: none;
        margin: 0;
        border-radius: var(--border-radius-sm);
    }

    .dialog-header {
        padding: var(--spacing-md);
    }

    .dialog-body {
        padding: var(--spacing-md);
        max-height: 60vh;
        overflow-y: auto;
    }

    .dialog-footer {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .dialog-footer button {
        width: 100%;
        order: 2;
    }

    .dialog-footer .btn-confirm {
        order: 1;
    }

    .form-group {
        margin-bottom: var(--spacing-md);
    }

    .form-group label {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-xs);
    }

    .form-group select,
    .form-group textarea,
    .form-group input {
        font-size: var(--font-size-sm);
        padding: var(--spacing-sm);
    }

    .selected-tickets-list {
        max-height: 150px;
        overflow-y: auto;
    }
}

@media (max-width: 480px) {
    .dialog {
        padding: var(--spacing-xs);
    }

    .dialog-content {
        border-radius: var(--border-radius-xs);
    }

    .dialog-header h3 {
        font-size: var(--font-size-md);
    }

    .dialog-body {
        padding: var(--spacing-sm);
    }

    .dialog-footer {
        padding: var(--spacing-sm);
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .critical {
        animation: none !important;
    }

    .time-limit-badge.danger {
        animation: none !important;
    }
}
