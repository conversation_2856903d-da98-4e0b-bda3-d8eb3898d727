/* 筛选器样式 */

/* 筛选器容器 */
.filters-container {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

/* 筛选器头部 */
.filters-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--table-header-bg);
    border-bottom: 1px solid var(--border-light);
}

.filters-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin: 0;
}

.filters-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    background: none;
    border: none;
    cursor: pointer;
    transition: color var(--transition-normal);
}

.filters-toggle:hover {
    color: var(--primary-hover);
}

.filters-toggle-icon {
    font-size: var(--font-size-sm);
    transition: transform var(--transition-normal);
}

.filters-toggle.collapsed .filters-toggle-icon {
    transform: rotate(-90deg);
}

/* 筛选器内容 */
.filters-content {
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
}

.filters-content.collapsed {
    max-height: 0;
    padding: 0 var(--spacing-lg);
    overflow: hidden;
}

/* 筛选器行 */
.filter-row {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-row:last-child {
    margin-bottom: 0;
}

/* 筛选器组 */
.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 200px;
    flex: 1;
}

.filter-group.compact {
    min-width: 150px;
}

.filter-group.wide {
    min-width: 300px;
}

.filter-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
}

.filter-label.required::after {
    content: '*';
    color: var(--error-color);
    margin-left: 2px;
}

/* 筛选器输入 */
.filter-input {
    width: 100%;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
    transition: all var(--transition-normal);
}

.filter-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.filter-input::placeholder {
    color: var(--text-placeholder);
}

/* 筛选器选择框 */
.filter-select {
    width: 100%;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

/* 多选筛选器 */
.filter-multiselect {
    position: relative;
}

.multiselect-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.multiselect-trigger:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.multiselect-placeholder {
    color: var(--text-placeholder);
}

.multiselect-selected {
    color: var(--text-color);
}

.multiselect-arrow {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    transition: transform var(--transition-normal);
}

.multiselect-trigger.open .multiselect-arrow {
    transform: rotate(180deg);
}

.multiselect-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 200px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-dropdown);
    overflow-y: auto;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.multiselect-trigger.open + .multiselect-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.multiselect-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-color);
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.multiselect-option:hover {
    background-color: var(--bg-light);
}

.multiselect-option.selected {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

/* 日期范围筛选器 */
.date-range-filter {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.date-range-input {
    flex: 1;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
}

.date-range-separator {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 快速日期选择 */
.quick-date-filters {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
    margin-top: var(--spacing-xs);
}

.quick-date-btn {
    padding: 2px var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    background-color: var(--bg-light);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
}

.quick-date-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.quick-date-btn.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

/* 筛选器操作按钮 */
.filter-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-light);
}

.filter-reset-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-reset-btn:hover {
    color: var(--text-color);
    background-color: var(--bg-light);
}

.filter-apply-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    color: #ffffff;
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.filter-apply-btn:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

/* 活跃筛选器显示 */
.active-filters {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--primary-light);
    border-bottom: 1px solid var(--primary-color);
    flex-wrap: wrap;
}

.active-filters-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
    white-space: nowrap;
}

.active-filter-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--primary-color);
    background-color: var(--bg-white);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius-sm);
}

.active-filter-remove {
    font-size: var(--font-size-xs);
    color: var(--primary-color);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.active-filter-remove:hover {
    color: var(--error-color);
}

.clear-all-filters {
    padding: 2px var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--error-color);
    background: none;
    border: 1px solid var(--error-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.clear-all-filters:hover {
    background-color: var(--error-light);
}

/* 筛选器统计 */
.filter-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--bg-gray);
    border-top: 1px solid var(--border-light);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.filter-result-count {
    font-weight: var(--font-weight-medium);
    color: var(--primary-color);
}

/* 保存筛选器 */
.save-filter {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    background: none;
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.save-filter:hover {
    background-color: var(--primary-light);
}

/* 响应式筛选器 */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .date-range-filter {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .active-filters {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}
