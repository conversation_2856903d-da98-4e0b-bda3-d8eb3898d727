/**
 * 抽屉组件样式
 * 用于新建工单的抽屉界面，包含语音转写、对话摘要和工单表单
 */

/* 抽屉遮罩层 */
.drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.drawer-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 抽屉容器 */
.drawer {
    position: fixed;
    top: 0;
    right: 0;
    width: 70%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: -8px 0 32px rgba(0, 0, 0, 0.15);
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 2001;
    display: flex;
    flex-direction: column;
}

.drawer.active {
    transform: translateX(0);
}

/* 抽屉标题栏 */
.drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 2px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    flex-shrink: 0;
}

.drawer-title {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.drawer-title::before {
    content: '📝';
    font-size: 24px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.drawer-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.drawer-close:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    transform: scale(1.1);
}

/* 抽屉主体内容 */
.drawer-body {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 2fr 3fr;
    gap: 24px;
    min-height: 0;
}

/* 左侧：实时信息区域 */
.drawer-realtime-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-height: 0;
}

.drawer-realtime-section h3 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
}

/* 抽屉中的语音转写框 */
.drawer-voice-transcript {
    flex: 1;
    border: 2px solid #6f42c1;
    border-radius: 12px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);
    min-height: 300px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 16px rgba(111, 66, 193, 0.15);
    position: relative;
    overflow: hidden;
}

.drawer-voice-transcript::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6f42c1 0%, #e83e8c 100%);
}

.drawer-voice-transcript .transcript-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.drawer-voice-transcript .transcript-header h4 {
    margin: 0;
    color: #6f42c1;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.drawer-voice-transcript .transcript-header h4::before {
    content: '🎤';
    font-size: 16px;
}

.drawer-voice-transcript .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #dc3545;
    animation: pulse-dot 2s infinite;
}

.drawer-voice-transcript .status-indicator.active {
    background: #28a745;
}

.drawer-voice-transcript .transcript-content {
    flex: 1;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.6;
}

/* 抽屉中的对话摘要框 */
.drawer-conversation-summary {
    flex: 1;
    border: 2px solid #17a2b8;
    border-radius: 12px;
    padding: 16px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    min-height: 300px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 16px rgba(23, 162, 184, 0.15);
    position: relative;
    overflow: hidden;
}

.drawer-conversation-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #17a2b8 0%, #20c997 100%);
}

.drawer-conversation-summary .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.drawer-conversation-summary .summary-header h4 {
    margin: 0;
    color: #17a2b8;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.drawer-conversation-summary .summary-header h4::before {
    content: '🧠';
    font-size: 16px;
}

.drawer-conversation-summary .summary-content {
    flex: 1;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.6;
}

/* 右侧：工单表单区域 */
.drawer-form-section {
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.drawer-form-section h3 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
}

.drawer-form-section h3::before {
    content: '📋';
    font-size: 18px;
}

/* 工单表单样式 */
.drawer-ticket-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
    padding-right: 8px;
}

/* 表单分区样式 */
.form-section {
    background: rgba(248, 249, 250, 0.5);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e9ecef;
}

.section-title {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 15px;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title::before {
    content: '📋';
    font-size: 16px;
}

/* 表单行布局 */
.form-row {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.half-width {
    flex: 1;
}

.third-width {
    flex: 1;
}

.drawer-form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 16px;
}

.drawer-form-group:last-child {
    margin-bottom: 0;
}

.drawer-form-group label {
    font-weight: 600;
    color: #495057;
    font-size: 13px;
}

.drawer-form-group input,
.drawer-form-group textarea,
.drawer-form-group select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 13px;
    transition: all 0.2s ease;
    background: white;
}

.drawer-form-group input:focus,
.drawer-form-group textarea:focus,
.drawer-form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.drawer-form-group textarea {
    resize: vertical;
}

.drawer-form-group .auto-filled {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    border-color: #28a745;
}

.drawer-form-group input[readonly] {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

/* 标签输入容器 */
.tag-input-container {
    position: relative;
}

.suggested-tags {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.tag-suggestion {
    display: inline-block;
    padding: 4px 8px;
    background: #e9ecef;
    color: #495057;
    border-radius: 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tag-suggestion:hover {
    background: #007bff;
    color: white;
    transform: translateY(-1px);
}

.tag-suggestion.selected {
    background: #28a745;
    color: white;
}

/* 主协办信息样式 */
.collaboration-section {
    background: rgba(240, 248, 255, 0.5);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #e3f2fd;
}

.subsection-title {
    margin: 0 0 12px 0;
    color: #1976d2;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.subsection-title::before {
    content: '🤝';
    font-size: 14px;
}

/* 部门输入容器 */
.department-input-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.department-input-container input {
    flex: 1;
}

.btn-select-department {
    padding: 10px 16px;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn-select-department:hover {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

/* 协办方容器 */
.collaborative-departments-container {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    background: white;
}

.selected-departments {
    min-height: 40px;
    margin-bottom: 12px;
}

.no-departments {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px dashed #dee2e6;
}

.department-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    padding: 6px 12px;
    border-radius: 16px;
    margin: 4px 6px 4px 0;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #2196f3;
}

.department-tag .remove-department {
    background: none;
    border: none;
    color: #1976d2;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    padding: 0;
    margin-left: 4px;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.department-tag .remove-department:hover {
    background: #1976d2;
    color: white;
}

.btn-add-department {
    padding: 8px 16px;
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-add-department:hover {
    background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

/* 部门选择弹窗样式 */
.department-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.department-modal.active {
    opacity: 1;
    visibility: visible;
}

.department-modal-content {
    background: white;
    border-radius: 12px;
    padding: 24px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.department-modal.active .department-modal-content {
    transform: scale(1);
}

.department-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e9ecef;
}

.department-modal-title {
    margin: 0;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
}

.department-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.department-modal-close:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.department-search {
    margin-bottom: 16px;
}

.department-search input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
}

.department-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.department-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.department-item:hover {
    background: #f8f9fa;
}

.department-item:last-child {
    border-bottom: none;
}

.department-item.selected {
    background: #e3f2fd;
    color: #1976d2;
    font-weight: 600;
}

.department-item-info {
    flex: 1;
}

.department-item-name {
    font-weight: 500;
    margin-bottom: 4px;
}

.department-item-desc {
    font-size: 12px;
    color: #6c757d;
}

/* 抽屉底部操作按钮 */
.drawer-actions {
    padding: 20px 24px;
    border-top: 2px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    flex-shrink: 0;
}

.drawer-actions .btn {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.drawer-actions .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.drawer-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.drawer-actions .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.drawer-actions .btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.drawer-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 动画效果 */
@keyframes pulse-dot {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .drawer {
        width: 80%;
    }
    
    .drawer-body {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .drawer-realtime-section {
        order: 2;
    }
    
    .drawer-form-section {
        order: 1;
    }
}

@media (max-width: 768px) {
    .drawer {
        width: 95%;
    }
    
    .drawer-header {
        padding: 16px 20px;
    }
    
    .drawer-body {
        padding: 20px;
    }
    
    .drawer-actions {
        padding: 16px 20px;
        flex-direction: column;
    }
}
