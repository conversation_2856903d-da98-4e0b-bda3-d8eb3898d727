/**
 * 主样式文件 - 12345政务服务热线工单管理系统
 * 包含全局样式、布局、颜色主题等基础样式
 */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --primary-active: #096dd9;
    
    /* 状态颜色 */
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;
    
    /* 紧急程度颜色 */
    --normal-color: #8c8c8c;
    --urgent-color: #faad14;
    --critical-color: #ff4d4f;
    
    /* 工单状态颜色 */
    --draft-color: #8c8c8c;
    --pending-color: #fa8c16;
    --processing-color: #1890ff;
    --reviewing-color: #722ed1;
    --callback-color: #52c41a;
    --closed-color: #8c8c8c;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f5f5f5;
    --bg-hover: #f0f0f0;
    
    /* 文字颜色 */
    --text-primary: #262626;
    --text-secondary: #595959;
    --text-tertiary: #8c8c8c;
    --text-disabled: #bfbfbf;
    
    /* 边框颜色 */
    --border-color: #d9d9d9;
    --border-light: #f0f0f0;
    --border-dark: #bfbfbf;
    
    /* 阴影 */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.15);
    
    /* 圆角 */
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 字体大小 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    
    /* 动画时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 14px;
    line-height: 1.5;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    overflow-x: hidden;
}

/* 页面布局 */
.page-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
}

.header-left .system-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.system-title i {
    color: var(--primary-color);
}

.header-right .user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-role {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
}

.logout-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.logout-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 主内容区域 */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 快速筛选区域 */
.quick-filter-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-light);
}

.quick-filter-tabs {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.filter-tab {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.filter-tab:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.filter-tab.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.filter-tab .count {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.filter-tab:not(.active) .count {
    background: var(--bg-tertiary);
    color: var(--text-tertiary);
}

/* 搜索筛选区域 */
.search-filter-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-light);
}

.search-bar {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.search-input-group {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-group i {
    position: absolute;
    left: var(--spacing-sm);
    color: var(--text-tertiary);
    z-index: 1;
}

.search-input-group input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 32px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    transition: border-color var(--transition-fast);
}

.search-input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: background-color var(--transition-fast);
    margin-left: var(--spacing-sm);
}

.search-btn:hover {
    background: var(--primary-hover);
}

.advanced-search-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.advanced-search-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 高级筛选面板 */
.advanced-filter-panel {
    border-top: 1px solid var(--border-light);
    padding-top: var(--spacing-md);
    display: none;
}

.advanced-filter-panel.show {
    display: block;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.filter-group select,
.filter-group input {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    transition: border-color var(--transition-fast);
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.apply-filter-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: background-color var(--transition-fast);
}

.apply-filter-btn:hover {
    background: var(--primary-hover);
}

.reset-filter-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.reset-filter-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 工具栏区域 */
.toolbar-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.toolbar-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.toolbar-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.toolbar-btn.primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.toolbar-btn.primary:hover {
    background: var(--primary-hover);
}

/* 批量操作 */
.batch-operations {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
}

.selected-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.batch-btn {
    background: none;
    border: none;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
}

.batch-btn:hover {
    background: var(--primary-color);
    color: white;
}

/* 视图切换 */
.view-toggle {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.view-btn {
    background: none;
    border: none;
    padding: var(--spacing-xs);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    border-right: 1px solid var(--border-color);
}

.view-btn:last-child {
    border-right: none;
}

.view-btn:hover {
    background: var(--bg-hover);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        padding: var(--spacing-sm);
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .main-content {
        padding: var(--spacing-sm);
    }
    
    .search-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
        justify-content: center;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
}
