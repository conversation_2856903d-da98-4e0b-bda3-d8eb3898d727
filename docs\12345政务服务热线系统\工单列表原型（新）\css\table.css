/**
 * 表格样式文件 - 工单列表表格相关样式
 */

/* 工单列表区域 */
.ticket-list-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

/* 表格容器 */
.table-container {
    overflow-x: auto;
    max-height: 70vh;
    position: relative;
}

/* 工单表格 */
.ticket-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
    background: var(--bg-primary);
}

/* 表头样式 */
.ticket-table thead {
    background: var(--bg-tertiary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.ticket-table th {
    padding: var(--spacing-sm) var(--spacing-xs);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
    user-select: none;
    position: relative;
}

/* 可排序列样式 */
.ticket-table th.sortable {
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.ticket-table th.sortable:hover {
    background: var(--bg-hover);
}

.ticket-table th.sortable i {
    margin-left: var(--spacing-xs);
    color: var(--text-tertiary);
    transition: color var(--transition-fast);
}

.ticket-table th.sortable:hover i {
    color: var(--text-secondary);
}

.ticket-table th.sortable.sorted-asc i::before {
    content: '\f0de'; /* fa-sort-up */
    color: var(--primary-color);
}

.ticket-table th.sortable.sorted-desc i::before {
    content: '\f0dd'; /* fa-sort-down */
    color: var(--primary-color);
}

/* 列宽设置 */
.ticket-table .checkbox-col {
    width: 40px;
    text-align: center;
}

.ticket-table .actions-col {
    width: 120px;
    text-align: center;
}

/* 表体样式 */
.ticket-table tbody tr {
    border-bottom: 1px solid var(--border-light);
    transition: background-color var(--transition-fast);
    cursor: pointer;
}

.ticket-table tbody tr:hover {
    background: var(--bg-hover);
}

.ticket-table tbody tr.selected {
    background: rgba(24, 144, 255, 0.05);
    border-color: var(--primary-color);
}

.ticket-table tbody tr.urgent {
    background: rgba(250, 173, 20, 0.02);
    border-left: 3px solid var(--urgent-color);
}

.ticket-table tbody tr.critical {
    background: rgba(255, 77, 79, 0.02);
    border-left: 3px solid var(--critical-color);
    animation: highlight 3s ease-in-out infinite;
}

/* 特急工单高亮动画 */
@keyframes highlight {
    0%, 100% { background: rgba(255, 77, 79, 0.02); }
    50% { background: rgba(255, 77, 79, 0.05); }
}

/* 表格单元格样式 */
.ticket-table td {
    padding: var(--spacing-sm) var(--spacing-xs);
    vertical-align: top;
    border-bottom: 1px solid var(--border-light);
    max-width: 200px;
    word-wrap: break-word;
}

.ticket-table td.checkbox-col,
.ticket-table td.actions-col {
    text-align: center;
    vertical-align: middle;
}

/* 工单编号列 */
.ticket-no {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
    cursor: pointer;
    text-decoration: none;
}

.ticket-no:hover {
    text-decoration: underline;
}

/* 市民信息列 */
.citizen-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.citizen-name {
    font-weight: 500;
    color: var(--text-primary);
}

.citizen-phone {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

.citizen-extra {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: 2px;
}

/* 工单内容列 */
.ticket-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.content-title {
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.content-category {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

.content-address {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.content-tags {
    margin-top: 2px;
}

/* 时限状态列 */
.time-limit-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: var(--font-size-xs);
}

.time-remaining {
    font-weight: 500;
}

.time-total {
    color: var(--text-secondary);
}

.time-current {
    color: var(--text-tertiary);
}

/* 流转信息列 */
.flow-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: var(--font-size-xs);
}

.current-step {
    font-weight: 500;
    color: var(--text-primary);
}

.flow-path {
    color: var(--text-secondary);
}

.assigned-unit {
    color: var(--text-tertiary);
}

/* 复选框样式 */
.ticket-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

/* 分页区域 */
.pagination-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.pagination-info select {
    padding: 2px 4px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.page-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: var(--spacing-xs);
}

.page-number {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
}

.page-number:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-number.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-number.ellipsis {
    border: none;
    cursor: default;
    color: var(--text-tertiary);
}

.page-number.ellipsis:hover {
    border: none;
    color: var(--text-tertiary);
}

.pagination-jump {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.pagination-jump input {
    width: 60px;
    padding: 2px 4px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    text-align: center;
    font-size: var(--font-size-sm);
}

.pagination-jump button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: background-color var(--transition-fast);
}

.pagination-jump button:hover {
    background: var(--primary-hover);
}

/* 加载状态 */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading-state i {
    font-size: 24px;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    color: var(--text-tertiary);
}

.empty-state h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.empty-state p {
    color: var(--text-tertiary);
}

/* 工单详情侧边栏 */
.ticket-detail-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--bg-primary);
    box-shadow: var(--shadow-heavy);
    z-index: 1000;
    transition: right var(--transition-normal);
    display: flex;
    flex-direction: column;
}

.ticket-detail-sidebar.show {
    right: 0;
}

.sidebar-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-tertiary);
}

.sidebar-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.close-sidebar-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.close-sidebar-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.sidebar-content {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
}

/* 遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .table-container {
        max-height: 60vh;
    }
    
    .ticket-table {
        font-size: var(--font-size-xs);
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: var(--spacing-xs);
    }
    
    .pagination-section {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .pagination-controls {
        justify-content: center;
    }
    
    .ticket-detail-sidebar {
        width: 100%;
        right: -100%;
    }
    
    /* 隐藏部分列在移动端 */
    .ticket-table .hide-mobile {
        display: none;
    }
}
