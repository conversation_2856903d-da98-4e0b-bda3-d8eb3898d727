/**
 * 12345政务服务热线系统 - 附件管理组件
 * @description 工单附件和证据材料管理组件
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 附件管理组件
 */
const AttachmentsComponent = {
    /**
     * 附件容器
     */
    container: null,
    
    /**
     * 附件数据列表
     * @type {Array<Object>}
     */
    attachments: [],
    
    /**
     * 初始化附件管理组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'attachmentGrid') {
        console.log('初始化附件管理组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到附件容器元素:', containerId);
            return;
        }
        
        // 加载附件数据
        this.loadAttachmentsData();
        
        // 渲染附件列表
        this.render();
    },
    
    /**
     * 加载附件数据
     */
    loadAttachmentsData() {
        // 使用统一的Mock数据
        if (window.MockData && window.MockData.attachments) {
            const mockAttachments = window.MockData.attachments;
            this.attachments = [];

            // 合并所有类型的附件（包括办结附件）
            ['original', 'evidence', 'documents'].forEach(category => {
                if (mockAttachments[category]) {
                    mockAttachments[category].forEach(att => {
                        this.attachments.push({
                            id: att.id,
                            name: att.name,
                            type: att.type,
                            size: att.size,
                            uploadTime: att.uploadTime,
                            uploader: att.uploader,
                            category: category,
                            description: att.description,
                            url: '#',
                            thumbnail: att.type === 'image' ? this.generateImageThumbnail(att.name) : null,
                            isPublic: !att.isInternal
                        });
                    });
                }
            });

            // 添加办结附件
            if (window.MockData.completionInfo && window.MockData.completionInfo.completionAttachments) {
                window.MockData.completionInfo.completionAttachments.forEach(att => {
                    this.attachments.push({
                        id: att.id,
                        name: att.name,
                        type: att.type,
                        size: att.size,
                        uploadTime: att.uploadTime,
                        uploader: att.uploader,
                        category: 'completion',
                        description: att.description,
                        url: '#',
                        thumbnail: att.type === 'image' ? this.generateImageThumbnail(att.name) : null,
                        isPublic: true
                    });
                });
            }
        } else {
            // 备用数据
            this.attachments = [
            {
                id: 'att001',
                name: '市民投诉录音.mp3',
                type: 'audio',
                size: '2.5MB',
                uploadTime: '2024-12-22 09:30:15',
                uploader: '张三 (工号: 001)',
                category: 'original',
                description: '市民来电投诉录音',
                url: '#',
                thumbnail: null,
                isPublic: true
            },
            {
                id: 'att002',
                name: '现场照片1.jpg',
                type: 'image',
                size: '1.2MB',
                uploadTime: '2024-12-24 15:30:21',
                uploader: '赵六 (工号: A005)',
                category: 'evidence',
                description: '小区垃圾堆积现场照片',
                url: '#',
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuePsOWcuueFp+eJhzE8L3RleHQ+PC9zdmc+',
                isPublic: true
            },
            {
                id: 'att003',
                name: '现场照片2.jpg',
                type: 'image',
                size: '0.8MB',
                uploadTime: '2024-12-24 15:32:45',
                uploader: '赵六 (工号: A005)',
                category: 'evidence',
                description: '小区公共设施损坏照片',
                url: '#',
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuePsOWcuueFp+eJhzI8L3RleHQ+PC9zdmc+',
                isPublic: true
            },
            {
                id: 'att004',
                name: '检查记录表.pdf',
                type: 'document',
                size: '0.5MB',
                uploadTime: '2024-12-24 16:00:12',
                uploader: '赵六 (工号: A005)',
                category: 'document',
                description: '现场检查记录表',
                url: '#',
                thumbnail: null,
                isPublic: true
            },
            {
                id: 'att005',
                name: '物业整改承诺书.pdf',
                type: 'document',
                size: '0.3MB',
                uploadTime: '2024-12-25 10:15:30',
                uploader: '赵六 (工号: A005)',
                category: 'document',
                description: '物业公司整改承诺书',
                url: '#',
                thumbnail: null,
                isPublic: false
            }
        ];
        }
    },

    /**
     * 生成图片缩略图
     */
    generateImageThumbnail(filename) {
        // 使用简单的占位符，避免中文字符编码问题
        const simpleName = filename.replace(/[^\x00-\x7F]/g, "file");
        return `data:image/svg+xml;base64,${btoa(`<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#ddd"/><text x="50%" y="50%" font-family="Arial" font-size="12" fill="#999" text-anchor="middle" dy=".3em">${simpleName}</text></svg>`)}`;
    },

    /**
     * 渲染附件列表
     */
    render() {
        if (!this.container) return;
        
        // 清空容器
        this.container.innerHTML = '';
        
        if (this.attachments.length === 0) {
            const emptyElement = document.createElement('div');
            emptyElement.className = 'attachments-empty';
            emptyElement.innerHTML = `
                <div class="empty-icon">
                    <i class="fas fa-paperclip"></i>
                </div>
                <div class="empty-text">暂无附件</div>
                <button class="btn btn-primary" onclick="AttachmentsComponent.showUploadDialog()">
                    <i class="fas fa-upload"></i> 上传附件
                </button>
            `;
            this.container.appendChild(emptyElement);
            return;
        }
        
        // 按类别分组
        const groupedAttachments = this.groupAttachmentsByCategory();
        
        // 渲染各个类别
        Object.keys(groupedAttachments).forEach(category => {
            const categorySection = this.createCategorySection(category, groupedAttachments[category]);
            this.container.appendChild(categorySection);
        });
    },
    
    /**
     * 按类别分组附件
     * @returns {Object} 分组后的附件
     */
    groupAttachmentsByCategory() {
        const groups = {
            original: [],
            evidence: [],
            documents: []
        };
        
        this.attachments.forEach(attachment => {
            const category = attachment.category;
            if (groups[category]) {
                groups[category].push(attachment);
            }
        });
        
        // 移除空分组
        Object.keys(groups).forEach(key => {
            if (groups[key].length === 0) {
                delete groups[key];
            }
        });
        
        return groups;
    },
    
    /**
     * 创建类别区域
     * @param {string} category - 类别名称
     * @param {Array} attachments - 该类别的附件列表
     * @returns {HTMLElement} 类别区域元素
     */
    createCategorySection(category, attachments) {
        const section = document.createElement('div');
        section.className = 'attachment-category';
        
        const categoryInfo = this.getCategoryInfo(category);
        
        const header = document.createElement('div');
        header.className = 'category-header';
        header.innerHTML = `
            <h4>
                <i class="${categoryInfo.icon}"></i>
                ${categoryInfo.label}
                <span class="category-count">(${attachments.length})</span>
            </h4>
            <button class="btn btn-sm btn-outline" onclick="AttachmentsComponent.toggleCategory('${category}')">
                <i class="fas fa-chevron-up"></i>
            </button>
        `;
        
        const content = document.createElement('div');
        content.className = 'category-content';
        content.id = `category-${category}`;
        
        const grid = document.createElement('div');
        grid.className = 'attachment-items';
        
        attachments.forEach(attachment => {
            const attachmentElement = this.createAttachmentElement(attachment);
            grid.appendChild(attachmentElement);
        });
        
        content.appendChild(grid);
        section.appendChild(header);
        section.appendChild(content);
        
        return section;
    },
    
    /**
     * 获取类别信息
     * @param {string} category - 类别名称
     * @returns {Object} 类别信息
     */
    getCategoryInfo(category) {
        const categoryMap = {
            original: { label: '原始材料', icon: 'fas fa-file-import' },
            evidence: { label: '证据材料', icon: 'fas fa-camera' },
            documents: { label: '文档材料', icon: 'fas fa-file-alt' },
            completion: { label: '办结材料', icon: 'fas fa-check-circle' }
        };
        
        return categoryMap[category] || { label: '其他附件', icon: 'fas fa-paperclip' };
    },
    
    /**
     * 创建附件元素
     * @param {Object} attachment - 附件数据
     * @returns {HTMLElement} 附件元素
     */
    createAttachmentElement(attachment) {
        const element = document.createElement('div');
        element.className = 'attachment-item';
        element.dataset.attachmentId = attachment.id;
        element.dataset.type = attachment.type; // 添加类型标识
        
        // 附件预览
        const preview = document.createElement('div');
        preview.className = 'attachment-preview';

        if (attachment.type === 'image' && attachment.thumbnail) {
            preview.innerHTML = `
                <img src="${attachment.thumbnail}" alt="${attachment.name}" onclick="AttachmentsComponent.previewAttachment('${attachment.id}')">
            `;
        } else if (attachment.type === 'audio') {
            // 音频文件显示播放器
            preview.innerHTML = `
                <div class="audio-player">
                    <audio controls preload="metadata">
                        <source src="#" type="audio/mpeg">
                        您的浏览器不支持音频播放
                    </audio>
                    <div class="audio-info">
                        <i class="fas fa-volume-up"></i>
                        <span>点击播放录音</span>
                    </div>
                </div>
            `;
        } else {
            const iconClass = this.getAttachmentIcon(attachment.type);
            preview.innerHTML = `
                <div class="attachment-icon" onclick="AttachmentsComponent.previewAttachment('${attachment.id}')">
                    <i class="${iconClass}"></i>
                </div>
            `;
        }
        
        // 附件信息
        const info = document.createElement('div');
        info.className = 'attachment-info';
        info.innerHTML = `
            <div class="attachment-name" title="${attachment.name}">${attachment.name}</div>
            <div class="attachment-meta">
                <span class="attachment-size">${attachment.size}</span>
                <span class="attachment-time">${attachment.uploadTime}</span>
            </div>
            <div class="attachment-uploader">${attachment.uploader}</div>
            ${attachment.description ? `<div class="attachment-description">${attachment.description}</div>` : ''}
            ${!attachment.isPublic ? '<div class="attachment-private"><i class="fas fa-lock"></i> 内部</div>' : ''}
        `;
        
        // 附件操作
        const actions = document.createElement('div');
        actions.className = 'attachment-actions';
        actions.innerHTML = `
            <button class="btn-attachment-action" onclick="AttachmentsComponent.previewAttachment('${attachment.id}')" title="预览">
                <i class="fas fa-eye"></i>
            </button>
            <button class="btn-attachment-action" onclick="AttachmentsComponent.downloadAttachment('${attachment.id}')" title="下载">
                <i class="fas fa-download"></i>
            </button>
            <button class="btn-attachment-action" onclick="AttachmentsComponent.editAttachment('${attachment.id}')" title="编辑">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn-attachment-action" onclick="AttachmentsComponent.deleteAttachment('${attachment.id}')" title="删除">
                <i class="fas fa-trash"></i>
            </button>
        `;
        
        element.appendChild(preview);
        element.appendChild(info);
        element.appendChild(actions);
        
        return element;
    },
    
    /**
     * 获取附件图标
     * @param {string} type - 附件类型
     * @returns {string} 图标类名
     */
    getAttachmentIcon(type) {
        const iconMap = {
            image: 'fas fa-image',
            document: 'fas fa-file-pdf',
            audio: 'fas fa-file-audio',
            video: 'fas fa-file-video',
            excel: 'fas fa-file-excel',
            word: 'fas fa-file-word'
        };
        
        return iconMap[type] || 'fas fa-file';
    },
    
    /**
     * 显示上传对话框
     */
    showUploadDialog() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3>上传附件</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="uploadForm">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">
                                <p>拖拽文件到此处或<span class="upload-link">点击选择文件</span></p>
                                <p class="upload-hint">支持图片、文档、音频、视频等格式，单个文件不超过10MB</p>
                            </div>
                            <input type="file" id="fileInput" multiple accept="*/*" style="display: none;">
                        </div>
                        <div class="form-group">
                            <label for="attachmentCategory">附件类别</label>
                            <select id="attachmentCategory" class="form-control">
                                <option value="evidence">证据材料</option>
                                <option value="document">相关文档</option>
                                <option value="other">其他附件</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="attachmentDescription">附件描述</label>
                            <textarea id="attachmentDescription" class="form-control" rows="3" placeholder="请输入附件描述..."></textarea>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="attachmentIsPublic" checked>
                                公开显示（取消勾选为内部附件）
                            </label>
                        </div>
                        <div class="selected-files" id="selectedFiles"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="AttachmentsComponent.uploadFiles()">上传</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 绑定事件
        this.bindUploadEvents(modal);
        
        // 显示模态框
        setTimeout(() => {
            modal.style.display = 'flex';
        }, 100);
    },
    
    /**
     * 绑定上传事件
     * @param {HTMLElement} modal - 模态框元素
     */
    bindUploadEvents(modal) {
        const uploadArea = modal.querySelector('#uploadArea');
        const fileInput = modal.querySelector('#fileInput');
        const selectedFiles = modal.querySelector('#selectedFiles');
        
        // 点击上传区域
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // 文件选择
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files, selectedFiles);
        });
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            this.handleFileSelection(e.dataTransfer.files, selectedFiles);
        });
    },
    
    /**
     * 处理文件选择
     * @param {FileList} files - 选择的文件列表
     * @param {HTMLElement} container - 显示容器
     */
    handleFileSelection(files, container) {
        container.innerHTML = '';
        
        Array.from(files).forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'selected-file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="${this.getFileIcon(file.name)}"></i>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(file.size)}</span>
                </div>
                <button type="button" class="btn-remove-file" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(fileItem);
        });
    },
    
    /**
     * 获取文件图标
     * @param {string} fileName - 文件名
     * @returns {string} 图标类名
     */
    getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        
        if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(extension)) {
            return 'fas fa-image';
        } else if (['pdf'].includes(extension)) {
            return 'fas fa-file-pdf';
        } else if (['mp3', 'wav', 'ogg'].includes(extension)) {
            return 'fas fa-file-audio';
        } else if (['mp4', 'avi', 'mov'].includes(extension)) {
            return 'fas fa-file-video';
        } else if (['xls', 'xlsx'].includes(extension)) {
            return 'fas fa-file-excel';
        } else if (['doc', 'docx'].includes(extension)) {
            return 'fas fa-file-word';
        } else {
            return 'fas fa-file';
        }
    },
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },
    
    /**
     * 上传文件
     */
    uploadFiles() {
        const fileInput = document.getElementById('fileInput');
        const category = document.getElementById('attachmentCategory').value;
        const description = document.getElementById('attachmentDescription').value;
        const isPublic = document.getElementById('attachmentIsPublic').checked;
        
        if (fileInput.files.length === 0) {
            Utils.showToast('请选择要上传的文件', 'warning');
            return;
        }
        
        Utils.showToast('正在上传文件...', 'info');
        
        // 模拟上传过程
        setTimeout(() => {
            Array.from(fileInput.files).forEach(file => {
                const newAttachment = {
                    id: 'att' + Date.now() + Math.random().toString(36).substr(2, 9),
                    name: file.name,
                    type: this.getFileTypeFromName(file.name),
                    size: this.formatFileSize(file.size),
                    uploadTime: Utils.formatDateTime(new Date()),
                    uploader: `${AppState.currentUser.name} (工号: ${AppState.currentUser.id})`,
                    category: category,
                    description: description,
                    url: '#',
                    thumbnail: null,
                    isPublic: isPublic
                };
                
                this.attachments.push(newAttachment);
            });
            
            // 重新渲染
            this.render();
            
            // 关闭模态框
            document.querySelector('.modal').remove();
            
            Utils.showToast('文件上传成功', 'success');
        }, 2000);
    },
    
    /**
     * 从文件名获取文件类型
     * @param {string} fileName - 文件名
     * @returns {string} 文件类型
     */
    getFileTypeFromName(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        
        if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(extension)) {
            return 'image';
        } else if (['pdf'].includes(extension)) {
            return 'document';
        } else if (['mp3', 'wav', 'ogg'].includes(extension)) {
            return 'audio';
        } else if (['mp4', 'avi', 'mov'].includes(extension)) {
            return 'video';
        } else if (['xls', 'xlsx'].includes(extension)) {
            return 'excel';
        } else if (['doc', 'docx'].includes(extension)) {
            return 'word';
        } else {
            return 'document';
        }
    },
    
    /**
     * 切换类别显示/隐藏
     * @param {string} category - 类别名称
     */
    toggleCategory(category) {
        const content = document.getElementById(`category-${category}`);
        const button = content.parentElement.querySelector('.category-header button i');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            button.className = 'fas fa-chevron-up';
        } else {
            content.style.display = 'none';
            button.className = 'fas fa-chevron-down';
        }
    },
    
    /**
     * 预览附件
     * @param {string} attachmentId - 附件ID
     */
    previewAttachment(attachmentId) {
        const attachment = this.attachments.find(att => att.id === attachmentId);
        if (!attachment) return;
        
        Utils.showToast('正在加载附件预览...', 'info');
        // 这里实现附件预览逻辑
    },
    
    /**
     * 下载附件
     * @param {string} attachmentId - 附件ID
     */
    downloadAttachment(attachmentId) {
        const attachment = this.attachments.find(att => att.id === attachmentId);
        if (!attachment) return;
        
        Utils.showToast(`正在下载 ${attachment.name}...`, 'info');
        // 这里实现附件下载逻辑
    },
    
    /**
     * 编辑附件
     * @param {string} attachmentId - 附件ID
     */
    editAttachment(attachmentId) {
        const attachment = this.attachments.find(att => att.id === attachmentId);
        if (!attachment) return;
        
        Utils.showToast('编辑功能开发中...', 'info');
    },
    
    /**
     * 删除附件
     * @param {string} attachmentId - 附件ID
     */
    deleteAttachment(attachmentId) {
        const attachment = this.attachments.find(att => att.id === attachmentId);
        if (!attachment) return;
        
        if (!confirm(`确定要删除附件 "${attachment.name}" 吗？`)) return;
        
        this.attachments = this.attachments.filter(att => att.id !== attachmentId);
        this.render();
        Utils.showToast('附件已删除', 'success');
    }
};

// 将AttachmentsComponent暴露到全局作用域
window.AttachmentsComponent = AttachmentsComponent;

// 全局函数 - 上传附件
function uploadAttachment() {
    AttachmentsComponent.showUploadDialog();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    AttachmentsComponent.initialize();
});
