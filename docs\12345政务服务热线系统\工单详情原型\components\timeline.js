/**
 * 12345政务服务热线系统 - 工单时间轴组件
 * @description 工单处理历史时间轴组件
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 时间轴数据模型
 * @typedef {Object} TimelineEvent
 * @property {string} id - 事件唯一标识
 * @property {string} type - 事件类型
 * @property {string} time - 事件时间
 * @property {string} actor - 执行人
 * @property {string} content - 事件内容
 * @property {string} [department] - 相关部门
 * @property {Object} [details] - 详细信息
 * @property {Array<Object>} [attachments] - 附件列表
 */

/**
 * 时间轴组件
 */
const TimelineComponent = {
    /**
     * 时间轴容器元素
     */
    container: null,
    
    /**
     * 时间轴事件数据
     * @type {Array<TimelineEvent>}
     */
    events: [],
    
    /**
     * 初始化时间轴组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'processTimeline') {
        console.log('初始化时间轴组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到时间轴容器元素:', containerId);
            return;
        }
        
        // 加载模拟数据
        this.loadMockData();
        
        // 渲染时间轴
        this.render();
    },
    
    /**
     * 加载模拟数据
     */
    loadMockData() {
        this.events = [
            {
                id: 'event001',
                type: 'create',
                time: '2024-12-22 09:30:15',
                actor: '张三 (工号: 001)',
                content: '创建工单',
                department: '市级话务中心',
                details: {
                    source: '电话',
                    callDuration: '5分钟',
                    callRecording: 'recording001.mp3'
                }
            },
            {
                id: 'event002',
                type: 'assign',
                time: '2024-12-22 10:15:22',
                actor: '李四 (工号: 002)',
                content: '指派工单至城市管理局',
                department: '市级话务中心',
                details: {
                    targetDepartment: '城市管理局',
                    assignReason: '根据工单内容，该问题属于城市管理局职责范围',
                    assignMode: '普通指派'
                }
            },
            {
                id: 'event003',
                type: 'receive',
                time: '2024-12-22 11:05:47',
                actor: '王五 (工号: A001)',
                content: '接收工单',
                department: '城市管理局',
                details: {
                    receiveComment: '已收到工单，将安排专人处理',
                    processPlan: '1. 联系物业公司了解情况；2. 安排人员现场检查；3. 督促整改'
                }
            },
            {
                id: 'event004',
                type: 'note',
                time: '2024-12-23 09:12:33',
                actor: '赵六 (工号: A005)',
                content: '已联系物业公司，对方表示将立即整改',
                department: '城市管理局',
                details: {
                    contactPerson: '物业公司张经理',
                    contactPhone: '13900001111',
                    responseContent: '已了解情况，将立即安排人员处理垃圾清理问题，并加强日常管理'
                }
            },
            {
                id: 'event005',
                type: 'note',
                time: '2024-12-24 15:30:21',
                actor: '赵六 (工号: A005)',
                content: '现场检查情况：物业已完成垃圾清理，但公共设施维护仍有不足',
                department: '城市管理局',
                details: {
                    inspectionTime: '2024-12-24 14:00',
                    inspectionLocation: '某某小区',
                    inspectionResult: '垃圾已清理完毕，但部分公共设施仍需维修',
                    followUpPlan: '要求物业公司一周内完成所有公共设施维修'
                },
                attachments: [
                    { name: '现场照片1.jpg', type: 'image', size: '1.2MB' },
                    { name: '现场照片2.jpg', type: 'image', size: '0.8MB' },
                    { name: '检查记录.pdf', type: 'document', size: '0.5MB' }
                ]
            }
        ];
    },
    
    /**
     * 渲染时间轴
     */
    render() {
        if (!this.container || !this.events.length) return;
        
        // 清空容器
        this.container.innerHTML = '';
        
        // 创建时间轴元素
        const timelineElement = document.createElement('div');
        timelineElement.className = 'timeline-wrapper';
        
        // 添加事件元素
        this.events.forEach((event, index) => {
            const eventElement = this.createEventElement(event, index);
            timelineElement.appendChild(eventElement);
        });
        
        // 将时间轴添加到容器
        this.container.appendChild(timelineElement);
    },
    
    /**
     * 创建事件元素
     * @param {TimelineEvent} event - 事件数据
     * @param {number} index - 事件索引
     * @returns {HTMLElement} 事件元素
     */
    createEventElement(event, index) {
        const eventElement = document.createElement('div');
        eventElement.className = 'timeline-event';
        eventElement.dataset.eventId = event.id;
        
        // 事件图标
        const iconElement = document.createElement('div');
        iconElement.className = `timeline-icon ${this.getEventIconClass(event.type)}`;
        iconElement.innerHTML = `<i class="${this.getEventIconClass(event.type)}"></i>`;
        
        // 事件内容容器
        const contentElement = document.createElement('div');
        contentElement.className = 'timeline-content';
        
        // 事件头部
        const headerElement = document.createElement('div');
        headerElement.className = 'timeline-header';
        
        // 事件标题
        const titleElement = document.createElement('div');
        titleElement.className = 'timeline-title';
        titleElement.innerHTML = `
            <span class="event-type">${this.getEventTypeLabel(event.type)}</span>
            <span class="event-time">${event.time}</span>
        `;
        
        // 事件操作
        const actionsElement = document.createElement('div');
        actionsElement.className = 'timeline-actions';
        actionsElement.innerHTML = `
            <button class="btn-timeline-action" onclick="TimelineComponent.toggleEventDetails('${event.id}')">
                <i class="fas fa-chevron-down"></i>
            </button>
        `;
        
        // 组装头部
        headerElement.appendChild(titleElement);
        headerElement.appendChild(actionsElement);
        
        // 事件主体
        const bodyElement = document.createElement('div');
        bodyElement.className = 'timeline-body';
        bodyElement.innerHTML = `
            <div class="event-content">${event.content}</div>
            <div class="event-actor">
                <span class="actor-label">操作人：</span>
                <span class="actor-name">${event.actor}</span>
                <span class="actor-department">${event.department}</span>
            </div>
        `;
        
        // 事件详情（默认隐藏）
        const detailsElement = document.createElement('div');
        detailsElement.className = 'timeline-details';
        detailsElement.id = `details-${event.id}`;
        detailsElement.style.display = 'none';
        
        // 填充详情内容
        if (event.details) {
            const detailsContent = document.createElement('div');
            detailsContent.className = 'details-content';
            
            // 根据事件类型生成不同的详情内容
            detailsContent.innerHTML = this.generateDetailsContent(event);
            
            detailsElement.appendChild(detailsContent);
        }
        
        // 附件列表
        if (event.attachments && event.attachments.length > 0) {
            const attachmentsElement = document.createElement('div');
            attachmentsElement.className = 'event-attachments';
            
            const attachmentsList = document.createElement('div');
            attachmentsList.className = 'attachments-list';
            
            event.attachments.forEach(attachment => {
                const attachmentItem = document.createElement('div');
                attachmentItem.className = 'attachment-item';
                attachmentItem.innerHTML = `
                    <i class="${this.getAttachmentIconClass(attachment.type)}"></i>
                    <span class="attachment-name">${attachment.name}</span>
                    <span class="attachment-size">${attachment.size}</span>
                    <div class="attachment-actions">
                        <button class="btn-attachment-action" onclick="previewAttachment('${attachment.name}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-attachment-action" onclick="downloadAttachment('${attachment.name}')">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                `;
                attachmentsList.appendChild(attachmentItem);
            });
            
            const attachmentsHeader = document.createElement('div');
            attachmentsHeader.className = 'attachments-header';
            attachmentsHeader.innerHTML = `
                <i class="fas fa-paperclip"></i>
                <span>附件（${event.attachments.length}）</span>
            `;
            
            attachmentsElement.appendChild(attachmentsHeader);
            attachmentsElement.appendChild(attachmentsList);
            detailsElement.appendChild(attachmentsElement);
        }
        
        // 组装内容
        contentElement.appendChild(headerElement);
        contentElement.appendChild(bodyElement);
        contentElement.appendChild(detailsElement);
        
        // 组装事件元素
        eventElement.appendChild(iconElement);
        eventElement.appendChild(contentElement);
        
        return eventElement;
    },
    
    /**
     * 获取事件类型标签
     * @param {string} type - 事件类型
     * @returns {string} 事件类型标签
     */
    getEventTypeLabel(type) {
        const typeLabels = {
            create: '创建工单',
            assign: '指派工单',
            receive: '接收工单',
            transfer: '转办工单',
            reassign: '改派工单',
            note: '处理记录',
            complete: '办结工单',
            review: '审核工单',
            callback: '回访记录',
            reopen: '重启工单',
            supervise: '督办记录',
            extend: '延期申请',
            suspend: '挂起申请',
            appeal: '申诉申请',
            merge: '合并工单',
            split: '分拆工单'
        };
        
        return typeLabels[type] || '操作记录';
    },
    
    /**
     * 获取事件图标类名
     * @param {string} type - 事件类型
     * @returns {string} 图标类名
     */
    getEventIconClass(type) {
        const iconClasses = {
            create: 'fas fa-plus-circle',
            assign: 'fas fa-share',
            receive: 'fas fa-inbox',
            transfer: 'fas fa-exchange-alt',
            reassign: 'fas fa-random',
            note: 'fas fa-comment',
            complete: 'fas fa-check-circle',
            review: 'fas fa-clipboard-check',
            callback: 'fas fa-phone',
            reopen: 'fas fa-redo',
            supervise: 'fas fa-eye',
            extend: 'fas fa-clock',
            suspend: 'fas fa-pause-circle',
            appeal: 'fas fa-exclamation-circle',
            merge: 'fas fa-object-group',
            split: 'fas fa-object-ungroup'
        };
        
        return iconClasses[type] || 'fas fa-circle';
    },
    
    /**
     * 获取附件图标类名
     * @param {string} type - 附件类型
     * @returns {string} 图标类名
     */
    getAttachmentIconClass(type) {
        const iconClasses = {
            image: 'fas fa-image',
            document: 'fas fa-file-pdf',
            audio: 'fas fa-file-audio',
            video: 'fas fa-file-video',
            excel: 'fas fa-file-excel',
            word: 'fas fa-file-word'
        };
        
        return iconClasses[type] || 'fas fa-file';
    },
    
    /**
     * 生成详情内容
     * @param {TimelineEvent} event - 事件数据
     * @returns {string} 详情HTML内容
     */
    generateDetailsContent(event) {
        let content = '<div class="details-grid">';
        
        // 根据事件类型生成不同的详情内容
        switch (event.type) {
            case 'create':
                content += `
                    <div class="details-item">
                        <span class="details-label">来源：</span>
                        <span class="details-value">${event.details.source}</span>
                    </div>
                    <div class="details-item">
                        <span class="details-label">通话时长：</span>
                        <span class="details-value">${event.details.callDuration}</span>
                    </div>
                    <div class="details-item">
                        <span class="details-label">录音：</span>
                        <span class="details-value">
                            <button class="btn-audio" onclick="playRecording('${event.details.callRecording}')">
                                <i class="fas fa-play"></i> 播放录音
                            </button>
                        </span>
                    </div>
                `;
                break;
                
            case 'assign':
                content += `
                    <div class="details-item">
                        <span class="details-label">指派目标：</span>
                        <span class="details-value">${event.details.targetDepartment}</span>
                    </div>
                    <div class="details-item">
                        <span class="details-label">指派原因：</span>
                        <span class="details-value">${event.details.assignReason}</span>
                    </div>
                    <div class="details-item">
                        <span class="details-label">指派模式：</span>
                        <span class="details-value">${event.details.assignMode}</span>
                    </div>
                `;
                break;
                
            case 'receive':
                content += `
                    <div class="details-item">
                        <span class="details-label">接收说明：</span>
                        <span class="details-value">${event.details.receiveComment}</span>
                    </div>
                    <div class="details-item">
                        <span class="details-label">处理计划：</span>
                        <span class="details-value">${event.details.processPlan}</span>
                    </div>
                `;
                break;
                
            case 'note':
                if (event.details.contactPerson) {
                    content += `
                        <div class="details-item">
                            <span class="details-label">联系人：</span>
                            <span class="details-value">${event.details.contactPerson}</span>
                        </div>
                        <div class="details-item">
                            <span class="details-label">联系电话：</span>
                            <span class="details-value">${event.details.contactPhone}</span>
                        </div>
                        <div class="details-item">
                            <span class="details-label">回复内容：</span>
                            <span class="details-value">${event.details.responseContent}</span>
                        </div>
                    `;
                }
                
                if (event.details.inspectionTime) {
                    content += `
                        <div class="details-item">
                            <span class="details-label">检查时间：</span>
                            <span class="details-value">${event.details.inspectionTime}</span>
                        </div>
                        <div class="details-item">
                            <span class="details-label">检查地点：</span>
                            <span class="details-value">${event.details.inspectionLocation}</span>
                        </div>
                        <div class="details-item">
                            <span class="details-label">检查结果：</span>
                            <span class="details-value">${event.details.inspectionResult}</span>
                        </div>
                        <div class="details-item">
                            <span class="details-label">后续计划：</span>
                            <span class="details-value">${event.details.followUpPlan}</span>
                        </div>
                    `;
                }
                break;
                
            default:
                // 通用详情展示
                for (const [key, value] of Object.entries(event.details)) {
                    const label = this.formatDetailLabel(key);
                    content += `
                        <div class="details-item">
                            <span class="details-label">${label}：</span>
                            <span class="details-value">${value}</span>
                        </div>
                    `;
                }
        }
        
        content += '</div>';
        return content;
    },
    
    /**
     * 格式化详情标签
     * @param {string} key - 详情键名
     * @returns {string} 格式化后的标签
     */
    formatDetailLabel(key) {
        const labelMap = {
            targetDepartment: '目标部门',
            assignReason: '指派原因',
            assignMode: '指派模式',
            receiveComment: '接收说明',
            processPlan: '处理计划',
            contactPerson: '联系人',
            contactPhone: '联系电话',
            responseContent: '回复内容',
            inspectionTime: '检查时间',
            inspectionLocation: '检查地点',
            inspectionResult: '检查结果',
            followUpPlan: '后续计划',
            source: '来源',
            callDuration: '通话时长',
            callRecording: '录音'
        };
        
        return labelMap[key] || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    },
    
    /**
     * 切换事件详情显示/隐藏
     * @param {string} eventId - 事件ID
     */
    toggleEventDetails(eventId) {
        const detailsElement = document.getElementById(`details-${eventId}`);
        const button = detailsElement.parentElement.querySelector('.timeline-actions button i');
        
        if (detailsElement.style.display === 'none') {
            detailsElement.style.display = 'block';
            button.className = 'fas fa-chevron-up';
        } else {
            detailsElement.style.display = 'none';
            button.className = 'fas fa-chevron-down';
        }
    },
    
    /**
     * 添加新事件
     * @param {TimelineEvent} event - 事件数据
     */
    addEvent(event) {
        this.events.unshift(event);
        this.render();
    }
};

// 将TimelineComponent暴露到全局作用域
window.TimelineComponent = TimelineComponent;

// 初始化时间轴组件
document.addEventListener('DOMContentLoaded', function() {
    TimelineComponent.initialize();
});
