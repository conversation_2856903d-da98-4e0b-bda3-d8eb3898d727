/* 角色页面专用样式 */

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 角色统计区域 */
.roles-overview {
    padding: 3rem 0;
    background: white;
}

.roles-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.external {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stat-icon.core {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.support {
    background: linear-gradient(135deg, #26de81 0%, #20bf6b 100%);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* 角色分类区域 */
.roles-categories {
    padding: 2rem 0 4rem;
    background: #f8f9fa;
}

.role-category {
    margin-bottom: 4rem;
}

.category-title {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.category-title i {
    color: #667eea;
}

.category-subtitle {
    font-size: 1rem;
    color: #666;
    font-weight: normal;
    margin-left: auto;
}

.roles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

/* 角色卡片 */
.role-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.role-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.role-card.external {
    border-left: 4px solid #ff6b6b;
}

.role-card.core {
    border-left: 4px solid #667eea;
}

.role-card.support {
    border-left: 4px solid #26de81;
}

/* 系统管理员卡片独立居中样式 */
.roles-grid .role-card.support.system-admin-card {
    grid-column: 1 / -1;
    margin: 0 auto !important;
    justify-self: center !important;
    width: 100%;
    max-width: none;
}

.role-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.role-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.role-card.external .role-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.role-card.core .role-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.role-card.support .role-icon {
    background: linear-gradient(135deg, #26de81 0%, #20bf6b 100%);
}

.role-name {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
    color: #333;
    line-height: 1.3;
}

.role-subtitle {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

.role-content {
    padding: 1.5rem;
}

.role-position {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #667eea;
    font-size: 0.95rem;
    line-height: 1.5;
}

.role-responsibilities h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.role-responsibilities ul {
    list-style: none;
    padding: 0;
}

.role-responsibilities li {
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    position: relative;
    line-height: 1.5;
    font-size: 0.95rem;
    color: #555;
}

.role-responsibilities li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
    font-size: 1.2rem;
}

.role-responsibilities li strong {
    color: #333;
}

.role-distribution,
.role-examples {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #e8f4fd;
    border-radius: 8px;
    border-left: 3px solid #2196f3;
    font-size: 0.95rem;
    line-height: 1.5;
}

.role-examples ul {
    margin-top: 0.5rem;
    list-style: none;
    padding: 0;
}

.role-examples li {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
    font-size: 0.9rem;
}

.role-examples li::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: #2196f3;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .roles-stats {
        gap: 1.5rem;
    }
    
    .stat-card {
        padding: 1rem 1.5rem;
    }
    
    .category-title {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .category-subtitle {
        margin-left: 0;
    }
    
    .roles-grid {
        grid-template-columns: 1fr;
    }
    
    .role-header {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .role-name {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .roles-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-card {
        width: 100%;
        max-width: 300px;
    }
    
    .role-content {
        padding: 1rem;
    }
    
    .role-header {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.role-card {
    animation: slideInUp 0.6s ease-out;
}

.role-card:nth-child(even) {
    animation-delay: 0.1s;
}

.role-card:nth-child(3n) {
    animation-delay: 0.2s;
}

/* 打印样式 */
@media print {
    .header,
    .footer {
        display: none;
    }
    
    .role-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .page-header {
        background: none;
        color: #333;
    }
}
