/**
 * 12345政务服务热线系统 - SLA状态组件
 * @description SLA时限管理和状态展示组件
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * SLA状态组件
 */
const SLAComponent = {
    /**
     * SLA状态容器
     */
    container: null,
    
    /**
     * SLA数据
     */
    slaData: null,
    
    /**
     * 倒计时定时器
     */
    countdownTimer: null,
    
    /**
     * 初始化SLA状态组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'slaStatus') {
        console.log('初始化SLA状态组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到SLA状态容器元素:', containerId);
            return;
        }
        
        // 加载SLA数据
        this.loadSLAData();
        
        // 渲染SLA状态
        this.render();
        
        // 启动倒计时
        this.startCountdown();
    },
    
    /**
     * 加载SLA数据
     */
    loadSLAData() {
        // 使用统一的Mock数据
        let defaultSLAData;

        if (window.MockData && window.MockData.sla) {
            const sla = window.MockData.sla;
            defaultSLAData = {
                totalLimit: sla.overall.totalDays,
                currentStageLimit: sla.currentStage.limitDays,
                remainingTime: sla.overall.remainingTime.days,
                remainingHours: sla.overall.remainingTime.hours,
                remainingMinutes: sla.overall.remainingTime.minutes,
                isOverdue: false,
                overdueHours: 0,
                warningThreshold: 2,
                status: sla.overall.status,
                isCompleted: sla.overall.isCompleted || false,
                stages: sla.stages.map(stage => ({
                    name: stage.name,
                    limit: stage.limitDays,
                    status: stage.status,
                    startTime: stage.startTime,
                    endTime: stage.endTime || null,
                    actualDuration: stage.usedDays
                })),
                extensions: sla.extensions ? sla.extensions.map(ext => ({
                    requestTime: ext.applyTime,
                    requestReason: ext.reason,
                    requestDays: ext.days,
                    status: ext.status,
                    approver: ext.approver,
                    approveTime: ext.approveTime
                })) : [],
                suspensions: sla.suspensions || []
            };
        } else {
            // 备用数据（与mock数据保持一致）
            defaultSLAData = {
                totalLimit: 15,
                currentStageLimit: 7,
                remainingTime: 10,
                remainingHours: 2,
                remainingMinutes: 45,
                isOverdue: false,
                overdueHours: 0,
                warningThreshold: 2,
                stages: [
                    {
                        name: '接收处理',
                        limit: 1,
                        status: 'completed',
                        startTime: '2024-12-20 09:15:30',
                        endTime: '2024-12-20 14:30:00',
                        actualDuration: 0.2
                    },
                    {
                        name: '现场处理',
                        limit: 7,
                        status: 'processing',
                        startTime: '2024-12-21 14:30:00',
                        endTime: null,
                        actualDuration: 3
                    },
                    {
                        name: '审核办结',
                        limit: 2,
                        status: 'pending',
                        startTime: null,
                        endTime: null,
                        actualDuration: 0
                    },
                    {
                        name: '回访确认',
                        limit: 3,
                        status: 'pending',
                        startTime: null,
                        endTime: null,
                        actualDuration: 0
                    }
                ],
                extensions: [
                    {
                        requestTime: '2024-12-22 16:30:00',
                        requestReason: '需要协调物业公司和业主委员会，情况较为复杂',
                        requestDays: 3,
                        status: 'approved',
                        approver: '陈主任',
                        approveTime: '2024-12-22 18:00:00'
                    }
                ],
                suspensions: []
            };
        }

        // 安全地获取SLA数据
        if (window.AppState && AppState.currentTicket && AppState.currentTicket.sla) {
            this.slaData = { ...defaultSLAData, ...AppState.currentTicket.sla };
        } else {
            this.slaData = defaultSLAData;
        }
    },
    
    /**
     * 渲染SLA状态
     */
    render() {
        if (!this.container || !this.slaData) return;
        
        // 清空容器
        this.container.innerHTML = '';
        
        // 创建总体时限区域
        const overallSection = this.createOverallSection();
        this.container.appendChild(overallSection);
        
        // 创建当前环节区域
        const currentStageSection = this.createCurrentStageSection();
        this.container.appendChild(currentStageSection);
        
        // 创建阶段进度区域
        const stagesSection = this.createStagesSection();
        this.container.appendChild(stagesSection);
        
        // 创建延期记录区域
        if (this.slaData.extensions.length > 0) {
            const extensionsSection = this.createExtensionsSection();
            this.container.appendChild(extensionsSection);
        }
        
        // 创建挂起记录区域
        if (this.slaData.suspensions.length > 0) {
            const suspensionsSection = this.createSuspensionsSection();
            this.container.appendChild(suspensionsSection);
        }
    },
    
    /**
     * 创建总体时限区域
     * @returns {HTMLElement} 总体时限元素
     */
    createOverallSection() {
        const section = document.createElement('div');
        section.className = 'sla-overall';
        
        const progressPercentage = Math.max(0, Math.min(100, 
            ((this.slaData.totalLimit - this.slaData.remainingTime) / this.slaData.totalLimit) * 100
        ));
        
        const statusClass = this.getSLAStatusClass();
        
        section.innerHTML = `
            <div class="sla-header">
                <h4><i class="fas fa-clock"></i> 总体时限</h4>
                <div class="sla-status ${statusClass}">
                    ${this.getSLAStatusText()}
                </div>
            </div>
            <div class="sla-progress">
                <div class="progress-bar">
                    <div class="progress-fill ${statusClass}" style="width: ${progressPercentage}%"></div>
                </div>
                <div class="progress-info">
                    <span class="progress-text">已用时: ${this.slaData.totalLimit - this.slaData.remainingTime}天</span>
                    <span class="progress-text">总时限: ${this.slaData.totalLimit}天</span>
                </div>
            </div>
            <div class="sla-countdown" id="slaCountdown">
                ${this.formatCountdown()}
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建当前环节区域
     * @returns {HTMLElement} 当前环节元素
     */
    createCurrentStageSection() {
        const section = document.createElement('div');
        section.className = 'sla-current-stage';
        
        const currentStage = this.slaData.stages.find(stage => stage.status === 'current');
        if (!currentStage) return section;
        
        const stageProgressPercentage = Math.max(0, Math.min(100,
            (currentStage.actualDuration / currentStage.limit) * 100
        ));
        
        section.innerHTML = `
            <div class="stage-header">
                <h4><i class="fas fa-tasks"></i> 当前环节</h4>
                <span class="stage-name">${currentStage.name}</span>
            </div>
            <div class="stage-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${stageProgressPercentage}%"></div>
                </div>
                <div class="progress-info">
                    <span class="progress-text">已用时: ${currentStage.actualDuration}天</span>
                    <span class="progress-text">限时: ${currentStage.limit}天</span>
                </div>
            </div>
            <div class="stage-details">
                <div class="detail-item">
                    <span class="detail-label">开始时间:</span>
                    <span class="detail-value">${currentStage.startTime}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">预计完成:</span>
                    <span class="detail-value">${this.calculateExpectedEndTime(currentStage)}</span>
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建阶段进度区域
     * @returns {HTMLElement} 阶段进度元素
     */
    createStagesSection() {
        const section = document.createElement('div');
        section.className = 'sla-stages';
        
        const header = document.createElement('div');
        header.className = 'stages-header';
        header.innerHTML = `
            <h4><i class="fas fa-list-ol"></i> 处理阶段</h4>
        `;
        
        const stagesList = document.createElement('div');
        stagesList.className = 'stages-list';
        
        this.slaData.stages.forEach((stage, index) => {
            const stageElement = document.createElement('div');
            stageElement.className = `stage-item stage-${stage.status}`;
            
            const statusIcon = this.getStageStatusIcon(stage.status);
            const statusText = this.getStageStatusText(stage.status);
            
            stageElement.innerHTML = `
                <div class="stage-indicator">
                    <div class="stage-number">${index + 1}</div>
                    <div class="stage-line"></div>
                </div>
                <div class="stage-content">
                    <div class="stage-title">
                        <span class="stage-name">${stage.name}</span>
                        <span class="stage-status-badge ${stage.status}">
                            <i class="${statusIcon}"></i>
                            ${statusText}
                        </span>
                    </div>
                    <div class="stage-info">
                        <span class="stage-limit">限时: ${stage.limit}天</span>
                        ${stage.actualDuration !== null ? 
                            `<span class="stage-duration">用时: ${stage.actualDuration}天</span>` : 
                            ''
                        }
                    </div>
                    ${stage.startTime ? 
                        `<div class="stage-time">开始: ${stage.startTime}</div>` : 
                        ''
                    }
                    ${stage.endTime ? 
                        `<div class="stage-time">结束: ${stage.endTime}</div>` : 
                        ''
                    }
                </div>
            `;
            
            stagesList.appendChild(stageElement);
        });
        
        section.appendChild(header);
        section.appendChild(stagesList);
        
        return section;
    },
    
    /**
     * 创建延期记录区域
     * @returns {HTMLElement} 延期记录元素
     */
    createExtensionsSection() {
        const section = document.createElement('div');
        section.className = 'sla-extensions';
        
        const header = document.createElement('div');
        header.className = 'extensions-header';
        header.innerHTML = `
            <h4><i class="fas fa-calendar-plus"></i> 延期记录</h4>
        `;
        
        const extensionsList = document.createElement('div');
        extensionsList.className = 'extensions-list';
        
        this.slaData.extensions.forEach(extension => {
            const extensionElement = document.createElement('div');
            extensionElement.className = 'extension-item';
            
            const statusClass = extension.status === 'approved' ? 'success' : 
                               extension.status === 'rejected' ? 'danger' : 'warning';
            
            extensionElement.innerHTML = `
                <div class="extension-header">
                    <span class="extension-date">${extension.requestTime}</span>
                    <span class="extension-status ${statusClass}">${this.getExtensionStatusText(extension.status)}</span>
                </div>
                <div class="extension-content">
                    <div class="extension-reason">${extension.requestReason}</div>
                    <div class="extension-details">
                        <span>申请延期: ${extension.requestDays}天</span>
                        ${extension.approver ? `<span>审批人: ${extension.approver}</span>` : ''}
                        ${extension.approveTime ? `<span>审批时间: ${extension.approveTime}</span>` : ''}
                    </div>
                </div>
            `;
            
            extensionsList.appendChild(extensionElement);
        });
        
        section.appendChild(header);
        section.appendChild(extensionsList);
        
        return section;
    },
    
    /**
     * 创建挂起记录区域
     * @returns {HTMLElement} 挂起记录元素
     */
    createSuspensionsSection() {
        const section = document.createElement('div');
        section.className = 'sla-suspensions';
        
        const header = document.createElement('div');
        header.className = 'suspensions-header';
        header.innerHTML = `
            <h4><i class="fas fa-pause-circle"></i> 挂起记录</h4>
        `;
        
        const suspensionsList = document.createElement('div');
        suspensionsList.className = 'suspensions-list';
        
        // 这里可以添加挂起记录的渲染逻辑
        
        section.appendChild(header);
        section.appendChild(suspensionsList);
        
        return section;
    },
    
    /**
     * 获取SLA状态类名
     * @returns {string} 状态类名
     */
    getSLAStatusClass() {
        if (this.slaData.isCompleted || this.slaData.status === 'completed') {
            return 'completed';
        } else if (this.slaData.isOverdue) {
            return 'overdue';
        } else if (this.slaData.remainingTime <= this.slaData.warningThreshold) {
            return 'warning';
        } else {
            return 'normal';
        }
    },
    
    /**
     * 获取SLA状态文本
     * @returns {string} 状态文本
     */
    getSLAStatusText() {
        if (this.slaData.isCompleted || this.slaData.status === 'completed') {
            return '已完成';
        } else if (this.slaData.isOverdue) {
            return `已超时 ${this.slaData.overdueHours}小时`;
        } else if (this.slaData.remainingTime <= this.slaData.warningThreshold) {
            return '即将超时';
        } else {
            return '正常';
        }
    },
    
    /**
     * 格式化倒计时显示
     * @returns {string} 倒计时文本
     */
    formatCountdown() {
        if (this.slaData.isCompleted || this.slaData.status === 'completed') {
            return `<span class="countdown-completed">已按时完成</span>`;
        } else if (this.slaData.isOverdue) {
            return `<span class="countdown-overdue">已超时 ${this.slaData.overdueHours}小时</span>`;
        } else {
            return `
                <span class="countdown-label">剩余时间:</span>
                <span class="countdown-time">
                    ${this.slaData.remainingTime}天
                    ${this.slaData.remainingHours}小时
                    ${this.slaData.remainingMinutes}分钟
                </span>
            `;
        }
    },
    
    /**
     * 计算预计结束时间
     * @param {Object} stage - 阶段数据
     * @returns {string} 预计结束时间
     */
    calculateExpectedEndTime(stage) {
        if (!stage.startTime) return '未开始';
        
        const startTime = new Date(stage.startTime);
        const expectedEndTime = new Date(startTime.getTime() + stage.limit * 24 * 60 * 60 * 1000);
        
        return Utils.formatDateTime(expectedEndTime);
    },
    
    /**
     * 获取阶段状态图标
     * @param {string} status - 阶段状态
     * @returns {string} 图标类名
     */
    getStageStatusIcon(status) {
        const icons = {
            completed: 'fas fa-check-circle',
            current: 'fas fa-clock',
            pending: 'fas fa-circle'
        };
        return icons[status] || 'fas fa-circle';
    },
    
    /**
     * 获取阶段状态文本
     * @param {string} status - 阶段状态
     * @returns {string} 状态文本
     */
    getStageStatusText(status) {
        const texts = {
            completed: '已完成',
            current: '进行中',
            pending: '待开始'
        };
        return texts[status] || '未知';
    },
    
    /**
     * 获取延期状态文本
     * @param {string} status - 延期状态
     * @returns {string} 状态文本
     */
    getExtensionStatusText(status) {
        const texts = {
            approved: '已批准',
            rejected: '已拒绝',
            pending: '待审批'
        };
        return texts[status] || '未知';
    },
    
    /**
     * 启动倒计时
     */
    startCountdown() {
        // 清除现有定时器
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }

        // 检查工单是否已完成，如果已完成则不启动倒计时
        if (this.slaData.isCompleted || this.slaData.status === 'completed') {
            console.log('工单已完成，不启动倒计时');
            return;
        }

        // 启动新定时器，每分钟更新一次
        this.countdownTimer = setInterval(() => {
            this.updateCountdown();
        }, 60000); // 60秒
    },
    
    /**
     * 更新倒计时
     */
    updateCountdown() {
        // 减少一分钟
        this.slaData.remainingMinutes--;
        
        if (this.slaData.remainingMinutes < 0) {
            this.slaData.remainingMinutes = 59;
            this.slaData.remainingHours--;
            
            if (this.slaData.remainingHours < 0) {
                this.slaData.remainingHours = 23;
                this.slaData.remainingTime--;
                
                if (this.slaData.remainingTime < 0) {
                    this.slaData.isOverdue = true;
                    this.slaData.overdueHours++;
                }
            }
        }
        
        // 更新显示
        const countdownElement = document.getElementById('slaCountdown');
        if (countdownElement) {
            countdownElement.innerHTML = this.formatCountdown();
        }
        
        // 更新状态指示器
        if (window.updateStatusIndicators) {
            updateStatusIndicators();
        }
    },
    
    /**
     * 停止倒计时
     */
    stopCountdown() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
    },
    
    /**
     * 更新SLA数据
     * @param {Object} newData - 新的SLA数据
     */
    updateSLAData(newData) {
        this.slaData = { ...this.slaData, ...newData };
        this.render();
    },
    
    /**
     * 获取SLA数据
     * @returns {Object} SLA数据
     */
    getSLAData() {
        return this.slaData;
    }
};

// 将SLAComponent暴露到全局作用域
window.SLAComponent = SLAComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    SLAComponent.initialize();
});

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    SLAComponent.stopCountdown();
});
