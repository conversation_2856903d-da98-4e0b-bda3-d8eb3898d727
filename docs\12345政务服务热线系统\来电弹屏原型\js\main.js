/**
 * 12345热线来电弹屏页面主文件
 * 负责初始化和协调各个模块
 */

class IncomingCallPopup {
    constructor() {
        // 基础状态
        this.isCallConnected = false;
        this.transcriptMessages = [];

        // 初始化各个模块
        this.agentStatus = new AgentStatusManager();
        this.softphone = new SoftphoneManager();
        this.dualInfo = new DualInfoManager();
        this.ticketManager = new TicketManager();
        this.uiEffects = new UIEffectsManager();
        this.notification = new NotificationManager();
        this.eventHandlers = new EventHandlersManager();
        this.drawerManager = new DrawerManager();

        this.init();
    }

    /**
     * 初始化页面功能
     */
    init() {
        // 确保页面滚动到顶部
        this.scrollToTop();

        // 初始化各个模块
        this.agentStatus.init();
        this.softphone.init();
        this.dualInfo.init();
        this.ticketManager.init();
        this.uiEffects.init();
        this.notification.init();
        this.eventHandlers.init(this);

        // 添加页面加载动画
        this.uiEffects.addLoadingAnimations();

        // 初始化模拟来电按钮
        this.initSimulateCallButton();

        // 初始化时隐藏接听按钮
        this.hideAnswerButton();

        // 初始化时隐藏展开全部按钮
        this.hideExpandHistoryButton();
    }

    /**
     * 滚动到页面顶部
     */
    scrollToTop() {
        window.scrollTo({
            top: 0,
            left: 0,
            behavior: 'instant'
        });

        // 确保页面完全加载后再次滚动到顶部
        setTimeout(() => {
            window.scrollTo({
                top: 0,
                left: 0,
                behavior: 'smooth'
            });
        }, 100);
    }

    /**
     * 初始化模拟来电按钮
     */
    initSimulateCallButton() {
        const simulateBtn = document.getElementById('simulateCallBtn');
        if (simulateBtn) {
            simulateBtn.addEventListener('click', () => {
                this.handleSimulateCall();
            });
        }
    }

    /**
     * 处理模拟来电按钮点击
     */
    handleSimulateCall() {
        const simulateBtn = document.getElementById('simulateCallBtn');

        // 检查当前状态
        if (this.softphone.callState === 'incoming') {
            this.notification.show('已有来电等待接听', 'warning');
            return;
        }

        if (this.isCallConnected) {
            this.notification.show('当前正在通话中', 'warning');
            return;
        }

        // 禁用按钮，防止重复点击
        if (simulateBtn) {
            simulateBtn.disabled = true;
            simulateBtn.innerHTML = '<span class="icon">⏳</span><span class="label">来电中...</span>';
        }

        // 延迟触发来电，模拟真实场景
        setTimeout(() => {
            this.simulateIncomingCall();

            // 恢复按钮状态（在来电被接听或挂断后）
            setTimeout(() => {
                if (simulateBtn && !this.isCallConnected) {
                    simulateBtn.disabled = false;
                    simulateBtn.innerHTML = '<span class="icon">📞</span><span class="label">模拟来电</span>';
                }
            }, 1000);
        }, 1000);
    }

    /**
     * 更新模拟来电按钮状态
     * @param {boolean} enabled - 是否启用按钮
     */
    updateSimulateCallButton(enabled) {
        const simulateBtn = document.getElementById('simulateCallBtn');
        if (simulateBtn) {
            simulateBtn.disabled = !enabled;
            if (enabled) {
                simulateBtn.innerHTML = '<span class="icon">📞</span><span class="label">模拟来电</span>';
            } else {
                simulateBtn.innerHTML = '<span class="icon">📵</span><span class="label">通话中</span>';
            }
        }
    }

    /**
     * 模拟来电流程
     */
    simulateIncomingCall() {
        // 设置来电状态
        this.softphone.setCallState('incoming');

        // 显示顶部来电指示器
        this.showIncomingCallIndicator();

        // 显示接听按钮
        this.showAnswerButton();

        // 有来电时立即显示客户信息（系统已识别来电号码）
        this.showCustomerData();

        // 显示通知
        this.notification.show('有来电，请点击接听按钮', 'info');
    }

    /**
     * 显示来电指示器
     */
    showIncomingCallIndicator() {
        const indicator = document.getElementById('incomingCallIndicator');
        if (indicator) {
            indicator.style.display = 'flex';
        }
    }

    /**
     * 隐藏来电指示器
     */
    hideIncomingCallIndicator() {
        const indicator = document.getElementById('incomingCallIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    /**
     * 显示接听按钮
     */
    showAnswerButton() {
        const answerBtn = document.getElementById('answerBtn');
        if (answerBtn) {
            answerBtn.style.display = 'flex';
        }
    }

    /**
     * 隐藏接听按钮
     */
    hideAnswerButton() {
        const answerBtn = document.getElementById('answerBtn');
        if (answerBtn) {
            answerBtn.style.display = 'none';
        }
    }

    /**
     * 隐藏展开全部按钮
     */
    hideExpandHistoryButton() {
        const expandBtn = document.querySelector('.expand-history');
        if (expandBtn) {
            expandBtn.style.display = 'none';
        }
    }

    /**
     * 显示展开全部按钮
     */
    showExpandHistoryButton() {
        const expandBtn = document.querySelector('.expand-history');
        if (expandBtn) {
            expandBtn.style.display = 'inline-block';
        }
    }

    /**
     * 接听电话
     */
    answerCall() {
        if (this.softphone.callState !== 'incoming') {
            this.notification.show('当前没有来电', 'warning');
            return;
        }

        this.softphone.answerCall();
        this.hideIncomingCallIndicator();
        this.connectCall();

        // 接通电话后，坐席状态变为通话中
        this.agentStatus.changeStatus('calling');

        // 接听后启用快捷操作按钮
        this.enableQuickActionButtons();

        this.notification.show('电话已接通', 'success');

        // 禁用模拟来电按钮
        this.updateSimulateCallButton(false);
    }

    /**
     * 挂断电话
     */
    hangupCall() {
        if (this.softphone.callState === 'idle') {
            this.notification.show('当前没有通话', 'warning');
            return;
        }

        this.softphone.hangupCall();
        this.agentStatus.changeStatus('available');
        this.isCallConnected = false;
        this.notification.show('通话已结束', 'info');

        // 重置页面到初始状态
        this.resetToInitialState();

        // 恢复模拟来电按钮
        this.updateSimulateCallButton(true);
    }

    /**
     * 重置页面到初始状态
     */
    resetToInitialState() {
        // 重置软电话到空闲状态
        this.resetSoftphoneToIdle();

        // 隐藏客户信息和历史工单，显示占位符
        this.hideCustomerData();

        // 重置双核信息显示区域
        this.dualInfo.hideRealTimeFeatures();
        this.dualInfo.clearSummaryData();

        // 重置语音转写区域
        this.resetTranscriptArea();

        // 重置右侧智能推荐区域
        this.resetRightPanel();

        // 重置工单管理区域
        this.resetTicketArea();

        // 清除所有通知
        this.notification.clearAll();

        // 重置软电话内部状态
        this.softphone.resetSoftphoneState();
    }

    /**
     * 重置软电话到空闲状态
     */
    resetSoftphoneToIdle() {
        // 隐藏来电指示器
        this.hideIncomingCallIndicator();

        // 设置软电话状态为空闲
        this.softphone.setCallState('idle');

        // 停止通话计时器
        this.softphone.stopCallTimer();

        // 隐藏所有通话控制按钮（包括接听按钮）
        this.hideAnswerButton();

        const hangupBtn = document.getElementById('hangupBtn');
        const muteBtn = document.getElementById('muteBtn');
        const holdBtn = document.getElementById('holdBtn');
        const transferBtn = document.getElementById('transferBtn');
        const recordBtn = document.getElementById('recordBtn');
        const callTimer = document.getElementById('callTimer');

        if (hangupBtn) hangupBtn.style.display = 'none';
        if (muteBtn) muteBtn.style.display = 'none';
        if (holdBtn) holdBtn.style.display = 'none';
        if (transferBtn) transferBtn.style.display = 'none';
        if (recordBtn) recordBtn.style.display = 'none';
        if (callTimer) callTimer.style.display = 'none';
    }

    /**
     * 重置语音转写区域
     */
    resetTranscriptArea() {
        // 隐藏转写内容，显示占位符
        const transcriptContent = document.getElementById('transcriptContent');
        const transcriptPlaceholder = document.getElementById('transcriptPlaceholder');

        if (transcriptContent) {
            transcriptContent.style.display = 'none';
            transcriptContent.innerHTML = '';
        }
        if (transcriptPlaceholder) {
            transcriptPlaceholder.style.display = 'flex';
        }
    }

    /**
     * 重置右侧智能推荐区域
     */
    resetRightPanel() {
        // 隐藏AI辅助内容，显示占位符
        const aiPlaceholder = document.getElementById('aiPlaceholder');
        const keywordsContent = document.getElementById('keywordsContent');
        const intentContent = document.getElementById('intentContent');

        if (aiPlaceholder) aiPlaceholder.style.display = 'block';
        if (keywordsContent) keywordsContent.style.display = 'none';
        if (intentContent) intentContent.style.display = 'none';

        // 隐藏知识库推荐内容，显示占位符
        const kbPlaceholder = document.getElementById('kbPlaceholder');
        const kbContent = document.getElementById('kbContent');

        if (kbPlaceholder) kbPlaceholder.style.display = 'block';
        if (kbContent) kbContent.style.display = 'none';

        // 隐藏话术推荐内容，显示占位符
        const scriptsPlaceholder = document.getElementById('scriptsPlaceholder');
        const scriptsContent = document.getElementById('scriptsContent');

        if (scriptsPlaceholder) scriptsPlaceholder.style.display = 'block';
        if (scriptsContent) scriptsContent.style.display = 'none';

        // 隐藏相似工单内容，显示占位符
        const similarPlaceholder = document.getElementById('similarPlaceholder');
        const similarContent = document.getElementById('similarContent');

        if (similarPlaceholder) similarPlaceholder.style.display = 'block';
        if (similarContent) similarContent.style.display = 'none';
    }

    /**
     * 重置工单管理区域
     */
    resetTicketArea() {
        // 隐藏智能工单草稿，显示双核信息
        const dualInfoDisplay = document.getElementById('dualInfoDisplay');
        const smartTicketDraft = document.getElementById('smartTicketDraft');
        const actionButtons = document.getElementById('actionButtons');

        if (dualInfoDisplay) dualInfoDisplay.style.display = 'grid';
        if (smartTicketDraft) smartTicketDraft.style.display = 'none';
        if (actionButtons) actionButtons.style.display = 'none';
    }

    /**
     * 显示客户信息和历史工单
     */
    showCustomerData() {
        // 显示客户信息
        const customerInfoPlaceholder = document.getElementById('customerInfoPlaceholder');
        const customerInfoContent = document.getElementById('customerInfoContent');

        if (customerInfoPlaceholder) customerInfoPlaceholder.style.display = 'none';
        if (customerInfoContent) customerInfoContent.style.display = 'flex';

        // 显示地址信息
        const addressPlaceholder = document.getElementById('addressPlaceholder');
        const addressContent = document.getElementById('addressContent');

        if (addressPlaceholder) addressPlaceholder.style.display = 'none';
        if (addressContent) addressContent.style.display = 'block';

        // 显示转人工信息
        const transferPlaceholder = document.getElementById('transferPlaceholder');
        const transferContent = document.getElementById('transferContent');

        if (transferPlaceholder) transferPlaceholder.style.display = 'none';
        if (transferContent) transferContent.style.display = 'block';

        // 显示历史工单
        const historyPlaceholder = document.getElementById('historyPlaceholder');
        const ticketSummary = document.getElementById('ticketSummary');

        if (historyPlaceholder) historyPlaceholder.style.display = 'none';
        if (ticketSummary) ticketSummary.style.display = 'block';

        // 显示展开全部按钮
        this.showExpandHistoryButton();

        // 根据模式显示不同的转人工信息内容
        this.showTransferInfo();
    }

    /**
     * 隐藏客户信息和历史工单，显示占位符
     */
    hideCustomerData() {
        // 隐藏客户信息，显示占位符
        const customerInfoPlaceholder = document.getElementById('customerInfoPlaceholder');
        const customerInfoContent = document.getElementById('customerInfoContent');

        if (customerInfoPlaceholder) customerInfoPlaceholder.style.display = 'block';
        if (customerInfoContent) customerInfoContent.style.display = 'none';

        // 隐藏地址信息，显示占位符
        const addressPlaceholder = document.getElementById('addressPlaceholder');
        const addressContent = document.getElementById('addressContent');

        if (addressPlaceholder) addressPlaceholder.style.display = 'block';
        if (addressContent) addressContent.style.display = 'none';

        // 隐藏转人工信息，显示占位符
        const transferPlaceholder = document.getElementById('transferPlaceholder');
        const transferContent = document.getElementById('transferContent');

        if (transferPlaceholder) transferPlaceholder.style.display = 'block';
        if (transferContent) transferContent.style.display = 'none';

        // 隐藏历史工单，显示占位符
        const historyPlaceholder = document.getElementById('historyPlaceholder');
        const ticketSummary = document.getElementById('ticketSummary');

        if (historyPlaceholder) historyPlaceholder.style.display = 'block';
        if (ticketSummary) ticketSummary.style.display = 'none';

        // 隐藏展开全部按钮
        this.hideExpandHistoryButton();

        // 禁用快捷操作按钮
        this.disableQuickActionButtons();
    }

    /**
     * 启用快捷操作按钮
     */
    enableQuickActionButtons() {
        const buttons = [
            document.getElementById('newTicketBtn'),
            document.getElementById('linkHistoryBtn'),
            document.getElementById('editTagsBtn')
        ];

        buttons.forEach(btn => {
            if (btn) {
                btn.disabled = false;
                btn.style.opacity = '1';
                btn.style.cursor = 'pointer';
            }
        });
    }

    /**
     * 禁用快捷操作按钮
     */
    disableQuickActionButtons() {
        const buttons = [
            document.getElementById('newTicketBtn'),
            document.getElementById('linkHistoryBtn'),
            document.getElementById('editTagsBtn')
        ];

        buttons.forEach(btn => {
            if (btn) {
                btn.disabled = true;
                btn.style.opacity = '0.5';
                btn.style.cursor = 'not-allowed';
            }
        });
    }

    /**
     * 接通电话
     */
    connectCall() {
        this.isCallConnected = true;
        
        // 更新坐席状态为通话中
        this.agentStatus.changeStatus('calling');

        // 显示双核信息呈现区域
        this.dualInfo.showRealTimeFeatures();

        // 开始双核信息更新
        this.dualInfo.startDisplay();

        // 显示通知
        this.notification.show('电话已接通，开始双核信息呈现', 'success');
    }

    /**
     * 创建新工单
     */
    createNewTicket() {
        if (!this.isCallConnected) {
            this.notification.show('请先接通电话后再创建工单', 'warning');
            return;
        }

        this.ticketManager.createNew();
    }

    /**
     * 提交工单
     */
    submitTicket() {
        this.ticketManager.submit();
    }

    /**
     * 显示转人工信息
     */
    showTransferInfo() {
        // 这里可以根据实际情况判断是传统模式还是智能模式
        // 可以根据系统配置、来电类型或用户设置来决定
        // 为了演示，我们可以随机选择或根据某些条件判断
        const isIntelligentMode = Math.random() > 0.5; // 随机选择模式用于演示

        const traditionalMode = document.getElementById('traditionalMode');
        const intelligentMode = document.getElementById('intelligentMode');

        if (isIntelligentMode) {
            if (traditionalMode) traditionalMode.style.display = 'none';
            if (intelligentMode) intelligentMode.style.display = 'block';

            // 可以在这里更新智能模式的具体数据
            this.updateIntelligentModeData();
        } else {
            if (traditionalMode) traditionalMode.style.display = 'block';
            if (intelligentMode) intelligentMode.style.display = 'none';

            // 可以在这里更新传统模式的具体数据
            this.updateTraditionalModeData();
        }
    }

    /**
     * 更新智能模式数据
     */
    updateIntelligentModeData() {
        // 这里可以根据实际的AI分析结果更新数据
        // 目前使用静态数据作为示例
    }

    /**
     * 更新传统模式数据
     */
    updateTraditionalModeData() {
        // 这里可以根据实际的IVR路径和排队信息更新数据
        // 目前使用静态数据作为示例

        // 确保显示历史排队信息
        this.updateQueueInfoForCallState();
    }

    /**
     * 根据通话状态更新排队信息
     */
    updateQueueInfoForCallState() {
        const queueLabel = document.querySelector('.queue-info label');
        const queueItems = document.querySelectorAll('.queue-item');

        if (!queueLabel || queueItems.length === 0) return;

        // 电话已经从队列出来转接到人工，始终显示历史排队信息
        queueLabel.textContent = '⏱️ 历史排队信息';

        // 更新为历史数据
        if (queueItems[0]) {
            queueItems[0].innerHTML = `
                <span class="queue-label">排队时长：</span>
                <span class="queue-value">2分15秒</span>
            `;
        }
        if (queueItems[1]) {
            queueItems[1].innerHTML = `
                <span class="queue-label">排队开始：</span>
                <span class="queue-value">12:25:30</span>
            `;
        }
        if (queueItems[2]) {
            queueItems[2].innerHTML = `
                <span class="queue-label">接通时间：</span>
                <span class="queue-value">12:27:45</span>
            `;
        }
        if (queueItems[3]) {
            queueItems[3].innerHTML = `
                <span class="queue-label">排队位置：</span>
                <span class="queue-value">第4位</span>
            `;
        }
    }

    /**
     * 获取各个管理器的引用（供其他模块使用）
     */
    getAgentStatus() { return this.agentStatus; }
    getSoftphone() { return this.softphone; }
    getDualInfo() { return this.dualInfo; }
    getTicketManager() { return this.ticketManager; }
    getUIEffects() { return this.uiEffects; }
    getNotification() { return this.notification; }
    getEventHandlers() { return this.eventHandlers; }
    getDrawerManager() { return this.drawerManager; }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 立即滚动到顶部
    window.scrollTo(0, 0);

    // 初始化应用
    window.incomingCallApp = new IncomingCallPopup();
});

// 页面完全加载后再次确保滚动到顶部
window.addEventListener('load', () => {
    window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'instant'
    });
});

// 导出主类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IncomingCallPopup;
}
