<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼容性测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-title {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        
        .test-badges {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .status-badge, .urgency-badge, .mode-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .status-badge.pending {
            background: rgba(24, 144, 255, 0.1);
            color: #1890ff;
            border: 1px solid rgba(24, 144, 255, 0.3);
        }
        
        .urgency-badge.urgent {
            background: rgba(250, 173, 20, 0.1);
            color: #faad14;
            border: 1px solid rgba(250, 173, 20, 0.3);
        }
        
        .urgency-badge.critical {
            background: rgba(255, 77, 79, 0.1);
            color: #ff4d4f;
            border: 1px solid rgba(255, 77, 79, 0.3);
            animation: pulse 2s infinite;
        }
        
        .mode-badge.cooperation {
            background: rgba(114, 46, 209, 0.1);
            color: #722ed1;
            border: 1px solid rgba(114, 46, 209, 0.3);
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }
        
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .test-result.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .test-result.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        
        .browser-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .test-log {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>12345政务服务热线系统 - 兼容性测试</h1>
    
    <div class="browser-info">
        <strong>浏览器信息：</strong>
        <span id="browserInfo"></span>
    </div>
    
    <div class="test-container">
        <h2 class="test-title">状态徽章悬停测试</h2>
        <p>请将鼠标悬停在下面的徽章上，应该会显示工具提示：</p>
        
        <div class="test-badges">
            <span class="status-badge pending">待接收</span>
            <span class="urgency-badge urgent">紧急</span>
            <span class="urgency-badge critical">特急</span>
            <span class="mode-badge cooperation">主协办</span>
        </div>
        
        <div id="tooltipTest" class="test-result"></div>
    </div>
    
    <div class="test-container">
        <h2 class="test-title">兼容性函数测试</h2>
        <p>测试各种兼容性函数是否正常工作：</p>
        
        <div id="compatibilityTest" class="test-result"></div>
    </div>
    
    <div class="test-container">
        <h2 class="test-title">测试日志</h2>
        <div id="testLog" class="test-log"></div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/status-indicator.js"></script>
    
    <script>
        // 显示浏览器信息
        document.getElementById('browserInfo').textContent = navigator.userAgent;
        
        // 测试日志
        const testLog = document.getElementById('testLog');
        function log(message) {
            const time = new Date().toLocaleTimeString();
            testLog.innerHTML += `[${time}] ${message}\n`;
            testLog.scrollTop = testLog.scrollHeight;
        }
        
        // 测试兼容性函数
        function testCompatibility() {
            const results = [];
            
            try {
                // 测试elementMatches函数
                const testElement = document.querySelector('.status-badge');
                const matchResult = Utils.elementMatches(testElement, '.status-badge');
                results.push(`elementMatches测试: ${matchResult ? '✓ 通过' : '✗ 失败'}`);
                log(`elementMatches测试结果: ${matchResult}`);
                
                // 测试arrayIncludes函数
                const testArray = ['a', 'b', 'c'];
                const includesResult = Utils.arrayIncludes(testArray, 'b');
                results.push(`arrayIncludes测试: ${includesResult ? '✓ 通过' : '✗ 失败'}`);
                log(`arrayIncludes测试结果: ${includesResult}`);
                
                // 测试状态指示器初始化
                if (window.StatusIndicator) {
                    results.push('StatusIndicator模块: ✓ 已加载');
                    log('StatusIndicator模块已成功加载');
                } else {
                    results.push('StatusIndicator模块: ✗ 未加载');
                    log('StatusIndicator模块加载失败');
                }
                
                // 显示结果
                const testDiv = document.getElementById('compatibilityTest');
                testDiv.className = 'test-result success';
                testDiv.innerHTML = results.join('<br>');
                
            } catch (error) {
                log(`兼容性测试出错: ${error.message}`);
                const testDiv = document.getElementById('compatibilityTest');
                testDiv.className = 'test-result error';
                testDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }
        
        // 测试工具提示功能
        function testTooltip() {
            let tooltipShown = false;
            
            // 监听工具提示显示
            const originalShowTooltip = window.StatusIndicator.showTooltip;
            window.StatusIndicator.showTooltip = function(element) {
                tooltipShown = true;
                log('工具提示显示成功');
                const testDiv = document.getElementById('tooltipTest');
                testDiv.className = 'test-result success';
                testDiv.innerHTML = '✓ 工具提示功能正常工作';
                
                // 调用原始函数
                originalShowTooltip.call(this, element);
            };
            
            // 5秒后检查是否显示了工具提示
            setTimeout(() => {
                if (!tooltipShown) {
                    log('工具提示未显示，可能需要手动测试');
                    const testDiv = document.getElementById('tooltipTest');
                    testDiv.className = 'test-result';
                    testDiv.innerHTML = '请手动将鼠标悬停在徽章上测试工具提示功能';
                }
            }, 5000);
        }
        
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始兼容性测试');
            
            // 等待模块初始化
            setTimeout(() => {
                testCompatibility();
                testTooltip();
            }, 500);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            log(`全局错误: ${e.error ? e.error.message : e.message}`);
            console.error('全局错误:', e);
        });
        
        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', function(e) {
            log(`未处理的Promise错误: ${e.reason}`);
            console.error('未处理的Promise错误:', e);
        });
    </script>
</body>
</html>
