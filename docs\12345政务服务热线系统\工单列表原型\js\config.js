/**
 * 系统配置文件
 */

// 系统配置
const CONFIG = {
    // API配置
    api: {
        baseUrl: '/api/v1',
        timeout: 30000,
        retryTimes: 3
    },
    
    // 分页配置
    pagination: {
        defaultPageSize: 50,
        pageSizeOptions: [20, 50, 100],
        maxPageSize: 100
    },
    
    // 表格配置
    table: {
        defaultSortField: 'createTime',
        defaultSortOrder: 'desc',
        maxSelectCount: 1000,
        autoRefreshInterval: 30000 // 30秒自动刷新
    },
    
    // 筛选配置
    filter: {
        maxHistoryCount: 10,
        debounceDelay: 300,
        cacheExpiry: 300000 // 5分钟缓存过期
    },
    
    // 用户角色配置
    roles: {
        MUNICIPAL_OPERATOR: 'municipal_operator',      // 市级话务员
        DISTRICT_OPERATOR: 'district_operator',        // 区级话务员
        STREET_OPERATOR: 'street_operator',            // 街镇话务员
        DEPARTMENT_STAFF: 'department_staff',          // 部门工作人员
        EXECUTOR: 'executor',                          // 执行人员
        COLLABORATOR: 'collaborator',                  // 协办人
        CALLBACK_STAFF: 'callback_staff',              // 回访员
        MANAGER: 'manager',                            // 管理者
        LEADER: 'leader',                              // 领导
        CITIZEN: 'citizen'                             // 市民
    },
    
    // 工单状态配置
    ticketStatus: {
        DRAFT: 'draft',                    // 草稿
        PENDING: 'pending',                // 待接收
        PROCESSING: 'processing',          // 处理中
        REVIEW: 'review',                  // 待审核
        CALLBACK: 'callback',              // 待回访
        CLOSED: 'closed',                  // 已关闭
        CANCELLED: 'cancelled',            // 已废除
        SUSPENDED: 'suspended'             // 挂起
    },
    
    // 紧急程度配置
    urgencyLevels: {
        NORMAL: 'normal',                  // 一般
        URGENT: 'urgent',                  // 紧急
        CRITICAL: 'critical'               // 特急
    },
    
    // 处理模式配置
    processingModes: {
        INSTANT: 'instant',                // 即时办结
        NORMAL: 'normal',                  // 普通流转
        COLLABORATIVE: 'collaborative',    // 主协办
        SUPERVISE: 'supervise'             // 督办
    },
    
    // 工单类型配置
    ticketTypes: {
        COMPLAINT: 'complaint',            // 投诉
        SUGGESTION: 'suggestion',          // 建议
        CONSULTATION: 'consultation',      // 咨询
        HELP: 'help',                      // 求助
        PRAISE: 'praise',                  // 表扬
        REPORT: 'report'                   // 举报
    },
    
    // 时限配置（小时）
    timeLimit: {
        normal: 72,                        // 一般工单72小时
        urgent: 24,                        // 紧急工单24小时
        critical: 4,                       // 特急工单4小时
        warningThreshold: 0.2,             // 预警阈值20%
        dangerThreshold: 0.1               // 危险阈值10%
    },
    
    // 本地存储键名
    storageKeys: {
        userSettings: 'ticket_list_user_settings',
        filterHistory: 'ticket_list_filter_history',
        columnSettings: 'ticket_list_column_settings',
        searchHistory: 'ticket_list_search_history',
        viewMode: 'ticket_list_view_mode'
    },
    
    // 消息配置
    messages: {
        success: {
            save: '保存成功',
            delete: '删除成功',
            export: '导出成功',
            merge: '合并成功',
            dispatch: '派单成功'
        },
        error: {
            network: '网络连接失败，请检查网络设置',
            timeout: '请求超时，请稍后重试',
            permission: '权限不足，无法执行此操作',
            validation: '数据验证失败，请检查输入内容',
            server: '服务器错误，请联系管理员'
        },
        confirm: {
            delete: '确定要删除选中的工单吗？',
            merge: '确定要合并选中的工单吗？',
            dispatch: '确定要派发选中的工单吗？',
            export: '确定要导出选中的数据吗？'
        }
    },
    
    // 动画配置
    animation: {
        duration: {
            fast: 150,
            normal: 300,
            slow: 500
        },
        easing: 'ease-in-out'
    },
    
    // 颜色配置
    colors: {
        status: {
            draft: '#8c8c8c',
            pending: '#fa8c16',
            processing: '#1890ff',
            review: '#722ed1',
            callback: '#13c2c2',
            closed: '#52c41a',
            cancelled: '#ff4d4f',
            suspended: '#faad14'
        },
        urgency: {
            normal: '#52c41a',
            urgent: '#faad14',
            critical: '#ff4d4f'
        },
        mode: {
            instant: '#faad14',
            normal: '#1890ff',
            collaborative: '#52c41a',
            supervise: '#ff4d4f'
        }
    },
    
    // 图标配置
    icons: {
        status: {
            draft: '📝',
            pending: '📥',
            processing: '⚙️',
            review: '🔍',
            callback: '📞',
            closed: '✅',
            cancelled: '❌',
            suspended: '⏸️'
        },
        urgency: {
            normal: '',
            urgent: '⚠️',
            critical: '🔥'
        },
        mode: {
            instant: '⚡',
            normal: '🔄',
            collaborative: '🤝',
            supervise: '🚨'
        },
        actions: {
            view: '👁️',
            edit: '✏️',
            dispatch: '📤',
            follow: '📝',
            close: '❌',
            more: '⋯'
        }
    },
    
    // 表格列配置
    columns: {
        default: [
            'checkbox',
            'ticketNumber',
            'status',
            'mode',
            'urgency',
            'title',
            'citizen',
            'department',
            'area',
            'createTime',
            'timeLimit',
            'actions'
        ],
        municipal_operator: [
            'checkbox',
            'ticketNumber',
            'status',
            'mode',
            'urgency',
            'title',
            'citizen',
            'area',
            'createTime',
            'timeLimit',
            'mergeOpportunity',
            'actions'
        ],
        manager: [
            'checkbox',
            'ticketNumber',
            'status',
            'urgency',
            'title',
            'department',
            'currentHandler',
            'area',
            'createTime',
            'timeLimit',
            'superviseLevel',
            'actions'
        ]
    },
    
    // 响应式断点
    breakpoints: {
        xs: 480,
        sm: 576,
        md: 768,
        lg: 992,
        xl: 1200,
        xxl: 1600
    }
};

// 工具函数
const UTILS = {
    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 计算时间差
     */
    getTimeDiff(timestamp) {
        const now = Date.now();
        const diff = timestamp - now;
        
        if (diff <= 0) {
            return { text: '已超时', type: 'timeout', value: Math.abs(diff) };
        }
        
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 24) {
            const days = Math.floor(hours / 24);
            return { text: `${days}天${hours % 24}小时`, type: 'normal', value: diff };
        } else if (hours > 0) {
            return { text: `${hours}小时${minutes}分钟`, type: hours > 4 ? 'normal' : 'warning', value: diff };
        } else {
            return { text: `${minutes}分钟`, type: 'danger', value: diff };
        }
    },
    
    /**
     * 本地存储操作
     */
    storage: {
        get(key, defaultValue = null) {
            try {
                const value = localStorage.getItem(key);
                return value ? JSON.parse(value) : defaultValue;
            } catch (error) {
                console.error('Storage get error:', error);
                return defaultValue;
            }
        },
        
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Storage set error:', error);
                return false;
            }
        },
        
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('Storage remove error:', error);
                return false;
            }
        }
    },
    
    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    /**
     * 深拷贝
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, UTILS };
}
