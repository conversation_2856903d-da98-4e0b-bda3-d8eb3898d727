/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    flex-direction: column;
    gap: 4px;
    padding: 8px;
    z-index: 1001;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background-color: white;
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* 菜单按钮动画 */
.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* 移动端菜单遮罩 */
.mobile-menu-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-menu-overlay.active {
    display: block;
    opacity: 1;
}

/* 主要内容样式 */
.main {
    min-height: calc(100vh - 120px);
}

/* 英雄区域 */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6rem 0 1.5rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.hero-main-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    font-weight: 300;
    opacity: 0.95;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    line-height: 1.6;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.hero-actions {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: rgba(255,255,255,0.2);
    color: white;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-primary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.5);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

/* 概览区域 */
.overview {
    padding: 4rem 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.overview-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.card-icon i {
    font-size: 1.5rem;
    color: white;
}

.overview-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.overview-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.card-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.card-link:hover {
    color: #764ba2;
    transform: translateX(5px);
}

/* 特色功能区域 */
.features {
    padding: 4rem 0;
    background: #f8f9fa;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-item h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-item p {
    color: #666;
    line-height: 1.6;
}

/* 流程预览区域 */
.workflow-preview {
    padding: 4rem 0;
    background: white;
}

.workflow-diagram {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
}

.workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 150px;
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.step-content h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.step-content p {
    font-size: 0.9rem;
    color: #666;
}

.workflow-arrow {
    font-size: 1.5rem;
    color: #667eea;
    font-weight: bold;
}

.workflow-cta {
    text-align: center;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 底部样式 */
.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header .container {
        position: relative;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .nav {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        flex-direction: column;
        gap: 0;
        padding: 0.5rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1000;
        border-radius: 0 0 10px 10px;
        backdrop-filter: blur(10px);
    }

    .nav.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-link {
        padding: 1rem 2rem;
        border-radius: 0;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    .nav-link:hover,
    .nav-link.active {
        background-color: rgba(255,255,255,0.15);
        transform: none;
    }

    .logo {
        font-size: 1.2rem;
    }
}

/* 更小屏幕的优化 */
@media (max-width: 480px) {
    .header {
        padding: 0.8rem 0;
    }

    .logo {
        font-size: 1rem;
    }

    .logo i {
        font-size: 0.9rem;
    }

    .mobile-menu-btn {
        padding: 6px;
    }

    .hamburger-line {
        width: 20px;
        height: 2px;
    }

    .nav-link {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        gap: 1.5rem;
    }
    
    .workflow-diagram {
        flex-direction: column;
    }
    
    .workflow-arrow {
        transform: rotate(90deg);
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.overview-card,
.feature-item {
    animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 项目目的样式 */
.project-purpose {
    padding: 4rem 0;
    background: white;
}

.purpose-content {
    max-width: 1200px;
    margin: 0 auto;
}

.purpose-header {
    text-align: center;
    margin-bottom: 3rem;
}

.purpose-header .section-title {
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.purpose-header .section-title i {
    color: #667eea;
}

.purpose-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.intro-text {
    font-size: 1.1rem;
    color: #555;
    line-height: 1.8;
    max-width: 900px;
    margin: 0 auto;
}

.purpose-goals {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.goal-item {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    border-left: 5px solid #667eea;
    transition: all 0.3s ease;
}

.goal-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.goal-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.5rem;
}

.goal-icon.citizen {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.goal-icon.staff {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.goal-icon.management {
    background: linear-gradient(135deg, #26de81 0%, #20bf6b 100%);
}

.goal-icon.future {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.goal-content h3 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.goal-content h4 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 500;
}

.goal-content ul {
    list-style: none;
    padding: 0;
}

.goal-content li {
    color: #666;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
    font-size: 0.9rem;
}

.goal-content li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.purpose-summary {
    text-align: center;
}

.summary-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    max-width: 600px;
    margin: 0 auto;
}

.summary-box h3 {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.summary-box p {
    font-size: 1.1rem;
    line-height: 1.6;
}

/* 产品愿景样式 */
.product-vision {
    padding: 4rem 0;
    background: #f8f9fa;
}

.vision-content {
    max-width: 1200px;
    margin: 0 auto;
}

.vision-header {
    text-align: center;
    margin-bottom: 3rem;
}

.vision-header .section-title {
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.vision-header .section-title i {
    color: #667eea;
}

.vision-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.intro-highlight {
    font-size: 1.1rem;
    color: #667eea;
    font-weight: 500;
    margin-top: 1rem;
}

.vision-scenarios {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    margin-bottom: 3rem;
}

.scenario-item {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.scenario-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.scenario-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 2rem;
    float: left;
    margin-right: 2rem;
}

.scenario-icon.citizen {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.scenario-icon.government {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.scenario-icon.city {
    background: linear-gradient(135deg, #26de81 0%, #20bf6b 100%);
}

.scenario-content h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.scenario-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    clear: both;
}

.feature {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.feature h4 {
    color: #333;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature h4 i {
    color: #667eea;
}

.feature p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

.vision-summary {
    text-align: center;
}

.summary-highlight {
    background: linear-gradient(135deg, #26de81 0%, #20bf6b 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    max-width: 700px;
    margin: 0 auto;
}

.summary-highlight h3 {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.summary-highlight p {
    font-size: 1.1rem;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .purpose-goals {
        grid-template-columns: 1fr;
    }

    .scenario-features {
        grid-template-columns: 1fr;
    }

    .scenario-icon {
        float: none;
        margin: 0 auto 1.5rem;
    }

    .scenario-content h3 {
        text-align: center;
    }
}

/* 挑战部分样式 */
.challenges {
    padding: 4rem 0;
    background: #f8f9fa;
}

.challenges-header {
    text-align: center;
    margin-bottom: 3rem;
}

.challenges-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.challenge-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 5px solid #dc3545;
}

.challenge-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.challenge-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.challenge-item h3 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.challenge-item h4 {
    color: #dc3545;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 500;
}

.challenge-item p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* 解决方案样式 */
.solution {
    padding: 4rem 0;
    background: white;
}

.solution-header {
    text-align: center;
    margin-bottom: 3rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.solution-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.feature-item {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    border-left: 5px solid #28a745;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.feature-content h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.feature-content p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* 角色赋能样式 */
.empowerment {
    padding: 4rem 0;
    background: #f8f9fa;
}

.empowerment-header {
    text-align: center;
    margin-bottom: 3rem;
}

.empowerment-tabs {
    max-width: 1000px;
    margin: 0 auto;
}

.tab-nav {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 1rem 2rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.tab-content {
    position: relative;
}

.tab-panel {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.empowerment-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 2rem;
}

.card-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    flex-shrink: 0;
}

.card-icon.citizen {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.card-icon.staff {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.manager {
    background: linear-gradient(135deg, #26de81 0%, #20bf6b 100%);
}

.card-icon.decision {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.feature-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.tag {
    background: #667eea;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.card-content p {
    color: #666;
    line-height: 1.6;
    font-size: 1rem;
}

/* 响应式设计更新 */
@media (max-width: 768px) {
    .hero-main-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.3rem;
    }

    .challenges-grid,
    .solution-features {
        grid-template-columns: 1fr;
    }

    .empowerment-card {
        flex-direction: column;
        text-align: center;
    }

    .tab-nav {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}
