/**
 * 12345政务服务热线系统 - 工单详情页面主脚本
 * @description 工单详情页面的核心交互逻辑
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 全局配置对象
 */
const CONFIG = {
    // API 基础路径
    API_BASE_URL: '/api/v1',
    
    // 工单状态映射
    TICKET_STATUS: {
        DRAFT: { key: 'draft', label: '草稿/暂存', class: 'status-draft' },
        PENDING: { key: 'pending', label: '待接收', class: 'status-pending' },
        PROCESSING: { key: 'processing', label: '处理中', class: 'status-processing' },
        REVIEWING: { key: 'reviewing', label: '待审核', class: 'status-reviewing' },
        SUSPENDED: { key: 'suspended', label: '挂起', class: 'status-suspended' },
        CALLBACK: { key: 'callback', label: '待回访', class: 'status-callback' },
        CLOSED: { key: 'closed', label: '已关闭', class: 'status-closed' },
        CANCELLED: { key: 'cancelled', label: '已废除', class: 'status-cancelled' }
    },
    
    // 优先级映射
    PRIORITY_LEVELS: {
        NORMAL: { key: 'normal', label: '普通', class: 'priority-normal' },
        URGENT: { key: 'urgent', label: '紧急', class: 'priority-urgent' },
        EMERGENCY: { key: 'emergency', label: '特急', class: 'priority-emergency' }
    },
    
    // 用户角色
    USER_ROLES: {
        OPERATOR: 'operator',           // 话务员
        ADMIN: 'admin',                // 派单人员
        PROCESSOR: 'processor',        // 处理人员
        REVIEWER: 'reviewer',          // 审核人员
        CALLBACK_STAFF: 'callback',    // 回访员
        COLLABORATOR: 'collaborator',  // 协办方
        SYSTEM_ADMIN: 'system_admin'   // 管理员
    }
};

/**
 * 全局状态管理
 */
const AppState = {
    // 演示模式标志
    demoMode: true, // 演示模式：显示所有功能，不受权限限制
    // 当前工单数据
    currentTicket: null,
    
    // 当前用户信息
    currentUser: {
        id: 'user001',
        name: '张丽华',
        role: CONFIG.USER_ROLES.CALLBACK_STAFF, // 设为回访员
        department: '市级话务中心'
    },
    
    // 页面状态
    pageState: {
        isLoading: false,
        editMode: false,
        expandedSections: new Set(['basicInfo'])
    }
};

/**
 * 工具函数集合
 */
const Utils = {
    /**
     * 格式化日期时间
     * @param {Date|string} date - 日期对象或字符串
     * @param {string} format - 格式化模式
     * @returns {string} 格式化后的日期字符串
     */
    formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 复制文本到剪贴板
     * @param {string} text - 要复制的文本
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('复制成功', 'success');
        } catch (err) {
            console.error('复制失败:', err);
            this.showToast('复制失败', 'error');
        }
    },
    
    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时长(毫秒)
     */
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            zIndex: '9999',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            backgroundColor: this.getToastColor(type)
        });
        
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, duration);
    },
    
    /**
     * 获取提示消息的背景色
     * @param {string} type - 消息类型
     * @returns {string} 背景色
     */
    getToastColor(type) {
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        return colors[type] || colors.info;
    },
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 时间限制
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('工单详情页面初始化开始');

    try {
        // 初始化页面数据
        initializePageData();

        // 初始化事件监听器
        initializeEventListeners();

        // 初始化组件
        initializeComponents();

        // 初始化测试控制器
        initializeTestControls();

        console.log('工单详情页面初始化完成');

        // 添加调试信息
        if (window.location.search.includes('debug=true')) {
            addDebugInfo();
        }
    } catch (error) {
        console.error('页面初始化失败:', error);
        showInitializationError(error);
    }
});

/**
 * 显示初始化错误
 * @param {Error} error - 错误对象
 */
function showInitializationError(error) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'initialization-error';
    errorDiv.innerHTML = `
        <div class="error-header">
            <i class="fas fa-exclamation-triangle"></i>
            页面初始化失败
        </div>
        <div class="error-message">${error.message}</div>
        <div class="error-actions">
            <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
        </div>
    `;

    document.body.insertBefore(errorDiv, document.body.firstChild);
}

/**
 * 添加调试信息
 */
function addDebugInfo() {
    const debugPanel = document.createElement('div');
    debugPanel.className = 'debug-panel';
    debugPanel.innerHTML = `
        <div class="debug-header">
            <h4>调试信息</h4>
            <button onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
        <div class="debug-content">
            <div><strong>AppState:</strong> ${JSON.stringify(AppState, null, 2)}</div>
        </div>
    `;

    document.body.appendChild(debugPanel);
}

/**
 * 初始化测试控制器
 */
function initializeTestControls() {
    // 设置用户角色选择器的默认值
    const userRoleSelect = document.getElementById('userRoleSelect');
    if (userRoleSelect) {
        userRoleSelect.value = AppState.currentUser.role;
    }

    // 设置工单状态选择器的默认值
    const ticketStatusSelect = document.getElementById('ticketStatusSelect');
    if (ticketStatusSelect) {
        ticketStatusSelect.value = AppState.currentTicket.status;
    }


}

/**
 * 初始化页面数据
 */
function initializePageData() {
    // 确保全局状态对象存在
    if (!window.AppState) {
        window.AppState = AppState;
    }

    // 工单数据使用MockData，只保留必要的状态信息
    AppState.currentTicket = {
        // 基础信息使用MockData
        status: 'closed', // 当前状态，用于控制界面显示
        // 其他信息都从MockData获取
    };

    // 确保全局状态可访问
    window.AppState = AppState;

    // 更新页面显示
    updatePageDisplay();
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 窗口大小变化事件
    window.addEventListener('resize', Utils.throttle(handleWindowResize, 250));
    
    // 键盘快捷键
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);
}

/**
 * 初始化组件
 */
function initializeComponents() {
    // 延迟初始化组件，确保DOM已完全加载
    setTimeout(() => {
        // 初始化主页签功能
        initializeMainTabs();

        // 初始化操作按钮
        initializeActionButtons();

        // 初始化状态指示器
        updateStatusIndicators();

        // 初始化可折叠区域
        initializeCollapsibleSections();

        // 确保所有组件都能获取到正确的数据
        refreshAllComponents();
    }, 100);
}

/**
 * 刷新所有组件
 */
function refreshAllComponents() {
    console.log('开始刷新所有组件');

    // 刷新客户信息组件
    if (window.CustomerComponent) {
        if (!CustomerComponent.container) {
            CustomerComponent.initialize();
        } else {
            CustomerComponent.loadCustomerData();
            CustomerComponent.render();
        }
    }

    // 刷新SLA组件
    if (window.SLAComponent) {
        if (!SLAComponent.container) {
            SLAComponent.initialize();
        } else {
            SLAComponent.loadSLAData();
            SLAComponent.render();
        }
    }

    // 刷新补记组件
    if (window.NotesComponent) {
        if (!NotesComponent.container) {
            NotesComponent.initialize();
        } else {
            NotesComponent.render();
        }
    }

    // 刷新附件组件
    if (window.AttachmentsComponent) {
        if (!AttachmentsComponent.container) {
            AttachmentsComponent.initialize();
        } else {
            AttachmentsComponent.render();
        }
    }

    // 刷新统计组件
    if (window.StatisticsComponent) {
        if (!StatisticsComponent.container) {
            StatisticsComponent.initialize();
        } else {
            StatisticsComponent.render();
        }
    }



    // 刷新回访组件
    if (window.CallbackComponent) {
        if (!CallbackComponent.container) {
            CallbackComponent.initialize();
        } else {
            CallbackComponent.render();
        }
    }

    // 刷新工单流转路径图组件
    if (window.WorkflowComponent) {
        if (!WorkflowComponent.container) {
            WorkflowComponent.initialize();
        } else {
            WorkflowComponent.render();
        }
    }

    console.log('所有组件刷新完成');
}

/**
 * 更新页面显示
 */
function updatePageDisplay() {
    console.log('updatePageDisplay 被调用');
    console.log('window.MockData:', window.MockData);

    // 使用MockData而不是AppState.currentTicket
    if (!window.MockData || !window.MockData.ticket) {
        console.log('MockData 或 MockData.ticket 不存在');
        return;
    }

    const ticket = window.MockData.ticket;
    console.log('ticket 数据:', ticket);

    // 更新基础信息 - 添加安全检查
    const updateElement = (id, value) => {
        const element = document.getElementById(id);
        if (element && value !== undefined && value !== null) {
            element.textContent = value;
        }
    };

    updateElement('ticketNumber', ticket.number);
    updateElement('createTime', ticket.createTime);
    updateElement('creator', `${ticket.creator.name} (工号: ${ticket.creator.id})`);
    updateElement('ticketSource', ticket.source);
    updateElement('ticketType', ticket.type);
    updateElement('ticketTitle', ticket.title);
    updateElement('ticketDescription', ticket.description);
    updateElement('location', ticket.location);

    // 更新标签
    updateTags();

    // 更新基础信息附件
    updateBasicAttachments();

    // 更新状态指示器
    updateStatusIndicators();
}

/**
 * 更新基础信息附件显示
 */
function updateBasicAttachments() {
    const ticket = window.MockData.ticket;
    if (!ticket || !ticket.attachments || ticket.attachments.length === 0) {
        return;
    }

    const basicAttachmentsContainer = document.getElementById('basicAttachments');
    const basicAttachmentsList = document.getElementById('basicAttachmentsList');

    if (!basicAttachmentsContainer || !basicAttachmentsList) {
        return;
    }

    // 显示附件容器
    basicAttachmentsContainer.style.display = 'block';

    // 清空现有内容
    basicAttachmentsList.innerHTML = '';

    // 生成附件列表
    ticket.attachments.forEach(attachment => {
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'attachment-item';
        attachmentItem.innerHTML = `
            <div class="attachment-info">
                <i class="${getAttachmentIcon(attachment.type)}"></i>
                <span class="attachment-name">${attachment.name}</span>
                <span class="attachment-size">${attachment.size}</span>
            </div>
            <div class="attachment-actions">
                <button class="btn-attachment-action" onclick="previewAttachment('${attachment.id}')" title="预览">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-attachment-action" onclick="downloadAttachment('${attachment.id}')" title="下载">
                    <i class="fas fa-download"></i>
                </button>
            </div>
        `;
        basicAttachmentsList.appendChild(attachmentItem);
    });
}

/**
 * 获取附件图标
 */
function getAttachmentIcon(type) {
    const iconMap = {
        'image': 'fas fa-image',
        'document': 'fas fa-file-pdf',
        'audio': 'fas fa-file-audio',
        'video': 'fas fa-file-video',
        'excel': 'fas fa-file-excel',
        'word': 'fas fa-file-word'
    };
    return iconMap[type] || 'fas fa-file';
}

/**
 * 预览附件
 */
function previewAttachment(attachmentId) {
    // 查找附件信息
    const ticket = window.MockData.ticket;
    if (!ticket || !ticket.attachments) {
        Utils.showToast('附件信息不存在', 'error');
        return;
    }

    const attachment = ticket.attachments.find(att => att.id === attachmentId);
    if (!attachment) {
        Utils.showToast('附件不存在', 'error');
        return;
    }

    // 根据附件类型进行不同的预览处理
    if (attachment.type === 'audio') {
        showAudioPlayer(attachment);
    } else if (attachment.type === 'image') {
        showImagePreview(attachment);
    } else if (attachment.type === 'document') {
        showDocumentPreview(attachment);
    } else {
        Utils.showToast('该类型文件暂不支持预览', 'warning');
    }
}

/**
 * 显示音频播放器
 */
function showAudioPlayer(attachment) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content audio-player-modal">
            <div class="modal-header">
                <h3><i class="fas fa-file-audio"></i> ${attachment.name}</h3>
                <button class="modal-close" onclick="closeModal(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="audio-info">
                    <div class="info-item">
                        <span class="label">文件大小：</span>
                        <span class="value">${attachment.size}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">上传时间：</span>
                        <span class="value">${attachment.uploadTime}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">上传者：</span>
                        <span class="value">${attachment.uploader}</span>
                    </div>
                    ${attachment.description ? `
                        <div class="info-item">
                            <span class="label">说明：</span>
                            <span class="value">${attachment.description}</span>
                        </div>
                    ` : ''}
                </div>
                <div class="audio-player-container">
                    <audio controls class="audio-player" preload="metadata">
                        <source src="#" type="audio/mpeg">
                        <p>您的浏览器不支持音频播放。</p>
                    </audio>
                    <div class="audio-controls">
                        <button class="btn btn-sm" onclick="downloadAttachment('${attachment.id}')">
                            <i class="fas fa-download"></i> 下载录音
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加关闭事件
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modal.querySelector('.modal-close'));
        }
    });

    Utils.showToast('录音加载完成，可以播放', 'success');
}

/**
 * 显示图片预览
 */
function showImagePreview(attachment) {
    Utils.showToast('正在加载图片预览...', 'info');
    // 这里实现图片预览逻辑
}

/**
 * 显示文档预览
 */
function showDocumentPreview(attachment) {
    Utils.showToast('正在加载文档预览...', 'info');
    // 这里实现文档预览逻辑
}

/**
 * 关闭模态框
 */
function closeModal(closeButton) {
    const modal = closeButton.closest('.modal-overlay');
    if (modal) {
        // 停止音频播放
        const audio = modal.querySelector('audio');
        if (audio) {
            audio.pause();
            audio.currentTime = 0;
        }
        modal.remove();
    }
}

/**
 * 下载附件
 */
function downloadAttachment(attachmentId) {
    Utils.showToast('正在下载附件...', 'info');
    // 这里实现附件下载逻辑
    console.log('下载附件:', attachmentId);
}

/**
 * 更新标签显示
 */
function updateTags() {
    // 使用MockData而不是AppState.currentTicket
    if (!window.MockData || !window.MockData.ticket || !window.MockData.ticket.tags) return;

    const ticket = window.MockData.ticket;

    // 更新业务标签
    const businessTagsContainer = document.getElementById('businessTags');
    if (businessTagsContainer && ticket.tags.business) {
        businessTagsContainer.innerHTML = '';
        ticket.tags.business.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'tag tag-business';
            tagElement.textContent = tag;
            businessTagsContainer.appendChild(tagElement);
        });
    }

    // 更新自定义标签
    const customTagsContainer = document.getElementById('customTags');
    if (customTagsContainer && ticket.tags.custom) {
        customTagsContainer.innerHTML = '';
        ticket.tags.custom.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'tag tag-custom';
            tagElement.textContent = tag;
            customTagsContainer.appendChild(tagElement);
        });

        // 添加"添加标签"按钮
        const addButton = document.createElement('button');
        addButton.className = 'btn-add-tag';
        addButton.innerHTML = '<i class="fas fa-plus"></i>';
        addButton.onclick = addCustomTag;
        customTagsContainer.appendChild(addButton);
    }
}

/**
 * 更新状态指示器
 */
function updateStatusIndicators() {
    // 使用MockData和AppState的组合
    if (!window.MockData || !window.MockData.ticket) return;

    const ticket = window.MockData.ticket;
    const currentStatus = AppState.currentTicket?.status || ticket.status;

    // 更新工单状态
    const statusBadge = document.getElementById('ticketStatus');
    if (statusBadge && currentStatus) {
        const statusConfig = CONFIG.TICKET_STATUS[currentStatus.toUpperCase()];
        if (statusConfig) {
            statusBadge.className = `status-badge ${statusConfig.class}`;
            const statusSpan = statusBadge.querySelector('span');
            if (statusSpan) {
                statusSpan.textContent = statusConfig.label;
            }
        }
    }

    // 更新优先级
    const priorityBadge = document.getElementById('priorityLevel');
    if (priorityBadge && ticket.priority) {
        const priorityConfig = CONFIG.PRIORITY_LEVELS[ticket.priority.toUpperCase()];
        if (priorityConfig) {
            priorityBadge.className = `priority-badge ${priorityConfig.class}`;
            const prioritySpan = priorityBadge.querySelector('span');
            if (prioritySpan) {
                prioritySpan.textContent = priorityConfig.label;
            }
        }
    }

    // 更新超时预警
    const warningBadge = document.getElementById('timeoutWarning');
    if (warningBadge && ticket.sla && typeof ticket.sla.remainingTime === 'number') {
        if (ticket.sla.remainingTime <= 2) {
            warningBadge.style.display = 'inline-flex';
        } else {
            warningBadge.style.display = 'none';
        }
    }
}

/**
 * 全局函数 - 复制到剪贴板
 * @param {string} elementId - 元素ID
 */
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    Utils.copyToClipboard(text);
}

/**
 * 全局函数 - 切换区域展开/折叠
 * @param {string} sectionId - 区域ID
 */
function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    const button = section.parentElement.querySelector('.card-header button i');
    
    if (AppState.pageState.expandedSections.has(sectionId)) {
        section.style.display = 'none';
        button.className = 'fas fa-chevron-down';
        AppState.pageState.expandedSections.delete(sectionId);
    } else {
        section.style.display = 'block';
        button.className = 'fas fa-chevron-up';
        AppState.pageState.expandedSections.add(sectionId);
    }
}

/**
 * 全局函数 - 导出工单（通过Actions组件调用）
 */
function exportTicket() {
    if (window.ActionsComponent) {
        ActionsComponent.exportTicket();
    }
}

/**
 * 全局函数 - 打印工单（通过Actions组件调用）
 */
function printTicket() {
    if (window.ActionsComponent) {
        ActionsComponent.printTicket();
    }
}

/**
 * 全局函数 - 显示地图
 */
function showMap() {
    Utils.showToast('正在加载地图...', 'info');
    // 这里实现地图显示逻辑
}

/**
 * 全局函数 - 添加自定义标签
 */
function addCustomTag() {
    const tagName = prompt('请输入标签名称:');
    if (tagName && tagName.trim()) {
        AppState.currentTicket.tags.custom.push(tagName.trim());
        updateTags();
        Utils.showToast('标签添加成功', 'success');
    }
}

/**
 * 处理窗口大小变化
 */
function handleWindowResize() {
    // 响应式布局调整逻辑
    console.log('窗口大小变化，当前宽度:', window.innerWidth);
}

/**
 * 处理键盘快捷键
 * @param {KeyboardEvent} event - 键盘事件
 */
function handleKeyboardShortcuts(event) {
    // Ctrl+P 打印
    if (event.ctrlKey && event.key === 'p') {
        event.preventDefault();
        printTicket();
    }
    
    // Ctrl+E 导出
    if (event.ctrlKey && event.key === 'e') {
        event.preventDefault();
        exportTicket();
    }
    
    // ESC 关闭模态框
    if (event.key === 'Escape') {
        closeAllModals();
    }
}

/**
 * 处理页面可见性变化
 */
function handleVisibilityChange() {
    if (document.hidden) {
        console.log('页面隐藏');
    } else {
        console.log('页面显示');
        // 页面重新显示时刷新数据
        refreshPageData();
    }
}

/**
 * 刷新页面数据
 */
function refreshPageData() {
    // 这里实现数据刷新逻辑
    console.log('刷新页面数据');
}

/**
 * 关闭所有模态框
 */
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

/**
 * 初始化操作按钮
 */
function initializeActionButtons() {
    // 这个函数将在 actions.js 中实现
    console.log('初始化操作按钮');
}

/**
 * 初始化可折叠区域
 */
function initializeCollapsibleSections() {
    // 设置初始展开状态
    AppState.pageState.expandedSections.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'block';
        }
    });
}

/**
 * 切换用户角色
 * @param {string} role - 新的用户角色
 */
function switchUserRole(role) {
    console.log('切换用户角色:', role);

    // 更新用户角色
    AppState.currentUser.role = role;

    // 更新用户信息显示
    updateUserInfo();

    // 重新渲染操作按钮
    if (window.ActionsComponent) {
        ActionsComponent.render();
    }

    // 显示提示
    const roleNames = {
        operator: '话务员',
        admin: '派单人员',
        processor: '处理人员',
        reviewer: '审核人员',
        callback: '回访员',
        system_admin: '管理员'
    };

    Utils.showToast(`已切换为${roleNames[role]}角色`, 'info');
}

/**
 * 切换工单状态
 * @param {string} status - 新的工单状态
 */
function switchTicketStatus(status) {
    console.log('切换工单状态:', status);

    // 更新工单状态
    AppState.currentTicket.status = status;

    // 更新状态指示器
    updateStatusIndicators();

    // 重新渲染操作按钮
    if (window.ActionsComponent) {
        ActionsComponent.render();
    }

    // 更新SLA状态
    if (window.SLAComponent) {
        // 根据状态调整SLA数据
        updateSLAForStatus(status);
        SLAComponent.render();
    }

    // 显示提示
    const statusNames = {
        draft: '草稿/暂存',
        pending: '待接收',
        processing: '处理中',
        reviewing: '待审核',
        suspended: '挂起',
        callback: '待回访',
        closed: '已关闭',
        cancelled: '已废除'
    };

    Utils.showToast(`工单状态已切换为：${statusNames[status]}`, 'info');
}



/**
 * 更新用户信息显示
 */
function updateUserInfo() {
    const role = AppState.currentUser.role;
    const roleNames = {
        operator: '话务员',
        admin: '派单人员',
        processor: '处理人员',
        reviewer: '审核人员',
        callback: '回访员',
        collaborator: '协办方',
        system_admin: '管理员'
    };

    // 更新用户名显示（如果有的话）
    const userElements = document.querySelectorAll('.current-user');
    userElements.forEach(element => {
        element.textContent = `${AppState.currentUser.name} (${roleNames[role]})`;
    });
}

/**
 * 根据状态更新SLA数据
 * @param {string} status - 工单状态
 */
function updateSLAForStatus(status) {
    if (!AppState.currentTicket.sla) return;

    const sla = AppState.currentTicket.sla;

    switch (status) {
        case 'draft':
            sla.remainingTime = 15;
            sla.isOverdue = false;
            sla.overdueHours = 0;
            break;
        case 'pending':
            sla.remainingTime = 14;
            sla.isOverdue = false;
            sla.overdueHours = 0;
            break;
        case 'processing':
            sla.remainingTime = 5;
            sla.isOverdue = false;
            sla.overdueHours = 0;
            break;
        case 'reviewing':
            sla.remainingTime = 1;
            sla.isOverdue = false;
            sla.overdueHours = 0;
            break;
        case 'callback':
            sla.remainingTime = 2;
            sla.isOverdue = false;
            sla.overdueHours = 0;
            break;
        case 'closed':
        case 'cancelled':
            sla.remainingTime = 0;
            sla.isOverdue = false;
            sla.overdueHours = 0;
            break;
    }

    // 更新剩余小时和分钟
    sla.remainingHours = Math.floor((sla.remainingTime % 1) * 24);
    sla.remainingMinutes = Math.floor(((sla.remainingTime % 1) * 24 % 1) * 60);
}

/**
 * 主页签切换功能
 */
function initializeMainTabs() {
    const tabButtons = document.querySelectorAll('.main-tab-button');
    const tabPanels = document.querySelectorAll('.main-tab-panel');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.mainTab;

            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));

            // 激活当前页签
            this.classList.add('active');
            const targetPanel = document.getElementById(`main-tab-${targetTab}`);
            if (targetPanel) {
                targetPanel.classList.add('active');
            }

            // 根据页签类型初始化对应组件
            initializeTabContent(targetTab);

            console.log('切换到页签:', targetTab);
        });
    });
}

/**
 * 初始化页签内容
 * @param {string} tabName - 页签名称
 */
function initializeTabContent(tabName) {
    switch(tabName) {
        case 'basic':
            // 工单详情页签 - 刷新所有基础组件
            refreshAllComponents();
            break;
        case 'notes':
            // 补记内容页签
            if (window.NotesComponent) {
                NotesComponent.initialize();
            }
            break;
        case 'related':
            // 相关工单页签
            if (window.RelatedTicketsComponent) {
                RelatedTicketsComponent.initialize();
            }
            break;
        case 'statistics':
            // 统计信息页签
            if (window.StatisticsComponent) {
                StatisticsComponent.initialize();
            }
            break;
        case 'collaboration':
            // 协办信息页签
            if (window.CollaborationComponent) {
                CollaborationComponent.initialize();
            }
            break;
        case 'callback':
            // 回访满意度页签
            if (window.CallbackComponent) {
                CallbackComponent.initialize();
            }
            break;
        case 'workflow':
            // 流转路径页签
            if (window.WorkflowComponent) {
                WorkflowComponent.initialize();
            }
            break;
    }
}
