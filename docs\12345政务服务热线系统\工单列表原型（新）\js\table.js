/**
 * 表格管理模块
 * 处理表格渲染、分页、选择等功能
 */

window.TableManager = {
    // 当前数据
    currentData: [],
    
    // 分页信息
    pagination: {
        current: 1,
        pageSize: 50,
        total: 0,
        totalPages: 0
    },
    
    // 选中的工单
    selectedTickets: new Set(),
    
    // 表格配置
    config: {
        pageSize: 50,
        showCheckbox: true,
        showActions: true
    },
    
    /**
     * 初始化表格
     */
    init: function() {
        this.bindEvents();
        this.loadInitialData();
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 全选复选框
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
        
        // 分页大小变更
        const pageSizeSelect = document.getElementById('pageSize');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.changePageSize(parseInt(e.target.value));
            });
        }
        
        // 跳转页面
        const jumpPageInput = document.getElementById('jumpPageInput');
        if (jumpPageInput) {
            jumpPageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.jumpToPage();
                }
            });
        }
        
        // 表格行点击事件
        this.bindRowEvents();
    },
    
    /**
     * 绑定表格行事件
     */
    bindRowEvents: function() {
        const tableBody = document.getElementById('ticketTableBody');
        if (tableBody) {
            // 使用事件委托
            tableBody.addEventListener('click', (e) => {
                const row = e.target.closest('tr');
                if (!row) return;
                
                const ticketId = row.dataset.ticketId;
                if (!ticketId) return;
                
                // 处理复选框点击
                if (e.target.type === 'checkbox') {
                    this.toggleRowSelection(ticketId, e.target.checked);
                    return;
                }
                
                // 处理操作按钮点击
                if (e.target.closest('.action-btn')) {
                    const action = e.target.closest('.action-btn').dataset.action;
                    this.handleRowAction(action, ticketId);
                    return;
                }
                
                // 处理工单编号点击
                if (e.target.classList.contains('ticket-no')) {
                    this.showTicketDetail(ticketId);
                    return;
                }
                
                // 处理行点击（显示详情预览）
                this.selectRow(ticketId);
            });
            
            // 右键菜单
            tableBody.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                const row = e.target.closest('tr');
                if (row) {
                    const ticketId = row.dataset.ticketId;
                    this.showContextMenu(e, ticketId);
                }
            });
        }
    },
    
    /**
     * 加载初始数据
     */
    loadInitialData: function() {
        if (window.mockAPI) {
            window.mockAPI.getTickets({
                page: 1,
                pageSize: this.config.pageSize
            }).then(result => {
                this.updateTable(result);
            }).catch(error => {
                console.error('加载初始数据失败:', error);
                this.showEmptyState();
            });
        }
    },
    
    /**
     * 更新表格数据
     * @param {Object} result API返回的结果
     */
    updateTable: function(result) {
        this.currentData = result.data || [];
        this.pagination = {
            current: result.page || 1,
            pageSize: result.pageSize || this.config.pageSize,
            total: result.total || 0,
            totalPages: result.totalPages || 0
        };
        
        // 清空选择
        this.selectedTickets.clear();
        this.updateSelectAllCheckbox();
        this.updateBatchOperations();
        
        // 渲染表格
        this.renderTable();
        this.renderPagination();
        
        // 更新总数显示
        this.updateTotalCount();
    },
    
    /**
     * 渲染表格
     */
    renderTable: function() {
        const tableBody = document.getElementById('ticketTableBody');
        if (!tableBody) return;
        
        if (this.currentData.length === 0) {
            this.showEmptyState();
            return;
        }
        
        this.hideEmptyState();
        
        const html = this.currentData.map(ticket => this.renderTicketRow(ticket)).join('');
        tableBody.innerHTML = html;
    },
    
    /**
     * 渲染工单行
     * @param {Object} ticket 工单数据
     * @returns {string} HTML字符串
     */
    renderTicketRow: function(ticket) {
        const isSelected = this.selectedTickets.has(ticket.id.toString());
        const urgencyClass = ticket.urgency === 'critical' ? 'critical' : ticket.urgency === 'urgent' ? 'urgent' : '';
        
        return `
            <tr data-ticket-id="${ticket.id}" class="${isSelected ? 'selected' : ''} ${urgencyClass}">
                <td class="checkbox-col">
                    <input type="checkbox" class="ticket-checkbox" ${isSelected ? 'checked' : ''}>
                </td>
                <td>
                    <a href="#" class="ticket-no" data-ticket-id="${ticket.id}">${ticket.ticketNo}</a>
                </td>
                <td>
                    ${this.renderStatusBadge(ticket.status)}
                </td>
                <td>
                    ${this.renderModeBadge(ticket.mode)}
                </td>
                <td>
                    ${this.renderUrgencyBadge(ticket.urgency)}
                </td>
                <td>
                    ${this.renderSuperviseBadge(ticket.supervise)}
                </td>
                <td>
                    ${this.renderCitizenInfo(ticket.citizen)}
                </td>
                <td>
                    ${this.renderTicketContent(ticket.content)}
                </td>
                <td>
                    ${this.renderTimeLimitInfo(ticket.timeLimit)}
                </td>
                <td class="hide-mobile">
                    ${Utils.formatDateTime(ticket.createTime, 'MM-DD HH:mm')}
                </td>
                <td class="actions-col">
                    ${this.renderActionButtons(ticket)}
                </td>
            </tr>
        `;
    },
    
    /**
     * 渲染状态徽章
     * @param {string} status 状态
     * @returns {string} HTML字符串
     */
    renderStatusBadge: function(status) {
        const statusMap = {
            draft: { text: '草稿', icon: 'edit' },
            pending: { text: '待接收', icon: 'inbox' },
            processing: { text: '处理中', icon: 'cog' },
            reviewing: { text: '待审核', icon: 'search' },
            callback: { text: '待回访', icon: 'phone' },
            closed: { text: '已关闭', icon: 'check' }
        };
        
        const statusInfo = statusMap[status] || { text: status, icon: 'question' };
        
        return `
            <span class="status-badge ${status}">
                <i class="fas fa-${statusInfo.icon}"></i>
                ${statusInfo.text}
            </span>
        `;
    },
    
    /**
     * 渲染处理模式徽章
     * @param {string} mode 处理模式
     * @returns {string} HTML字符串
     */
    renderModeBadge: function(mode) {
        const modeMap = {
            instant: { text: '即时办结', icon: 'bolt' },
            normal: { text: '普通流转', icon: 'share' },
            cooperation: { text: '主协办', icon: 'handshake' }
        };
        
        const modeInfo = modeMap[mode] || { text: mode, icon: 'question' };
        
        return `
            <span class="mode-badge ${mode}">
                <i class="fas fa-${modeInfo.icon}"></i>
                ${modeInfo.text}
            </span>
        `;
    },
    
    /**
     * 渲染紧急程度徽章
     * @param {string} urgency 紧急程度
     * @returns {string} HTML字符串
     */
    renderUrgencyBadge: function(urgency) {
        const urgencyMap = {
            normal: { text: '一般', icon: 'circle' },
            urgent: { text: '紧急', icon: 'exclamation-triangle' },
            critical: { text: '特急', icon: 'fire' }
        };
        
        const urgencyInfo = urgencyMap[urgency] || { text: urgency, icon: 'question' };
        
        return `
            <span class="urgency-badge ${urgency}">
                <i class="fas fa-${urgencyInfo.icon}"></i>
                ${urgencyInfo.text}
            </span>
        `;
    },
    
    /**
     * 渲染督办徽章
     * @param {string} supervise 督办级别
     * @returns {string} HTML字符串
     */
    renderSuperviseBadge: function(supervise) {
        if (supervise === 'none') return '';
        
        const superviseMap = {
            general: { text: '一般督办', icon: 'eye' },
            important: { text: '重点督办', icon: 'exclamation' },
            leader: { text: '领导督办', icon: 'crown' }
        };
        
        const superviseInfo = superviseMap[supervise] || { text: supervise, icon: 'eye' };
        
        return `
            <span class="supervise-badge ${supervise}">
                <i class="fas fa-${superviseInfo.icon}"></i>
                ${superviseInfo.text}
            </span>
        `;
    },
    
    /**
     * 渲染市民信息
     * @param {Object} citizen 市民信息
     * @returns {string} HTML字符串
     */
    renderCitizenInfo: function(citizen) {
        let html = `
            <div class="citizen-info">
                <div class="citizen-name">${citizen.name}</div>
                <div class="citizen-phone">${citizen.phone}</div>
        `;
        
        if (citizen.isVip) {
            html += `
                <div class="citizen-extra">
                    <span class="vip-badge">
                        <i class="fas fa-star"></i>
                        ${citizen.vipType}
                    </span>
                </div>
            `;
        }
        
        if (citizen.historyCount > 0) {
            html += `
                <div class="citizen-extra">
                    <span class="tag">历史${citizen.historyCount}次</span>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    },
    
    /**
     * 渲染工单内容
     * @param {Object} content 工单内容
     * @returns {string} HTML字符串
     */
    renderTicketContent: function(content) {
        return `
            <div class="ticket-content">
                <div class="content-title" title="${content.title}">${content.title}</div>
                <div class="content-category">${content.category}</div>
                <div class="content-address hide-mobile" title="${content.address}">${content.address}</div>
            </div>
        `;
    },
    
    /**
     * 渲染时限信息
     * @param {Object} timeLimit 时限信息
     * @returns {string} HTML字符串
     */
    renderTimeLimitInfo: function(timeLimit) {
        return `
            <div class="time-limit-info">
                <div class="time-remaining">
                    <span class="time-limit-badge ${timeLimit.warningLevel}">
                        ${timeLimit.remaining}
                    </span>
                </div>
                <div class="time-total hide-sm">总计: ${timeLimit.total}</div>
                <div class="time-current hide-sm">当前: ${timeLimit.current}</div>
            </div>
        `;
    },
    
    /**
     * 渲染操作按钮
     * @param {Object} ticket 工单数据
     * @returns {string} HTML字符串
     */
    renderActionButtons: function(ticket) {
        const buttons = this.getActionButtons(ticket);
        
        let html = '<div class="action-buttons">';
        
        // 显示前3个按钮
        buttons.slice(0, 3).forEach(button => {
            html += `
                <button class="action-btn ${button.type || ''}" 
                        data-action="${button.action}" 
                        title="${button.title}">
                    <i class="fas fa-${button.icon}"></i>
                    <span class="hide-mobile">${button.text}</span>
                </button>
            `;
        });
        
        // 如果有更多按钮，显示"更多"菜单
        if (buttons.length > 3) {
            html += `
                <div class="more-actions">
                    <button class="action-btn" onclick="toggleMoreActions(this)">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div class="more-actions-menu">
                        ${buttons.slice(3).map(button => `
                            <button class="more-actions-item" data-action="${button.action}">
                                <i class="fas fa-${button.icon}"></i>
                                ${button.text}
                            </button>
                        `).join('')}
                    </div>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    },
    
    /**
     * 获取操作按钮配置
     * @param {Object} ticket 工单数据
     * @returns {Array} 按钮配置数组
     */
    getActionButtons: function(ticket) {
        const buttons = [];
        
        // 根据工单状态和用户权限返回不同的按钮
        switch (ticket.status) {
            case 'draft':
                buttons.push(
                    { action: 'edit', text: '编辑', icon: 'edit', type: 'primary' },
                    { action: 'assign', text: '派单', icon: 'share', type: 'success' },
                    { action: 'delete', text: '删除', icon: 'trash', type: 'danger' }
                );
                break;
            case 'pending':
                buttons.push(
                    { action: 'accept', text: '接单', icon: 'check', type: 'primary' },
                    { action: 'reject', text: '退回', icon: 'times', type: 'warning' },
                    { action: 'view', text: '查看', icon: 'eye' }
                );
                break;
            case 'processing':
                buttons.push(
                    { action: 'update', text: '更新', icon: 'edit', type: 'primary' },
                    { action: 'submit', text: '提交', icon: 'paper-plane', type: 'success' },
                    { action: 'suspend', text: '挂起', icon: 'pause', type: 'warning' }
                );
                break;
            default:
                buttons.push(
                    { action: 'view', text: '查看', icon: 'eye', type: 'primary' }
                );
        }
        
        // 通用操作
        buttons.push(
            { action: 'history', text: '历史', icon: 'history' },
            { action: 'print', text: '打印', icon: 'print' }
        );
        
        return buttons;
    },
    
    /**
     * 处理行操作
     * @param {string} action 操作类型
     * @param {string} ticketId 工单ID
     */
    handleRowAction: function(action, ticketId) {
        const ticket = this.currentData.find(t => t.id.toString() === ticketId);
        if (!ticket) return;
        
        switch (action) {
            case 'view':
                this.showTicketDetail(ticketId);
                break;
            case 'edit':
                this.editTicket(ticketId);
                break;
            case 'assign':
                this.assignTicket(ticketId);
                break;
            case 'accept':
                this.acceptTicket(ticketId);
                break;
            case 'reject':
                this.rejectTicket(ticketId);
                break;
            case 'submit':
                this.submitTicket(ticketId);
                break;
            case 'delete':
                this.deleteTicket(ticketId);
                break;
            default:
                Utils.showMessage(`执行操作: ${action}`, 'info');
        }
    },
    
    /**
     * 显示工单详情
     * @param {string} ticketId 工单ID
     */
    showTicketDetail: function(ticketId) {
        const sidebar = document.getElementById('ticketDetailSidebar');
        const overlay = document.getElementById('overlay');
        const content = document.getElementById('sidebarContent');
        
        if (sidebar && overlay && content) {
            // 加载详情数据
            if (window.mockAPI) {
                window.mockAPI.getTicketDetail(ticketId).then(ticket => {
                    content.innerHTML = this.renderTicketDetail(ticket);
                    sidebar.classList.add('show');
                    overlay.classList.add('show');
                });
            }
        }
    },
    
    /**
     * 渲染工单详情
     * @param {Object} ticket 工单数据
     * @returns {string} HTML字符串
     */
    renderTicketDetail: function(ticket) {
        return `
            <div class="ticket-detail">
                <div class="detail-section">
                    <h4>基本信息</h4>
                    <div class="detail-item">
                        <label>工单编号:</label>
                        <span>${ticket.ticketNo}</span>
                    </div>
                    <div class="detail-item">
                        <label>工单状态:</label>
                        <span>${this.renderStatusBadge(ticket.status)}</span>
                    </div>
                    <div class="detail-item">
                        <label>紧急程度:</label>
                        <span>${this.renderUrgencyBadge(ticket.urgency)}</span>
                    </div>
                    <div class="detail-item">
                        <label>处理模式:</label>
                        <span>${this.renderModeBadge(ticket.mode)}</span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>工单内容</h4>
                    <div class="detail-item">
                        <label>标题:</label>
                        <span>${ticket.content.title}</span>
                    </div>
                    <div class="detail-item">
                        <label>分类:</label>
                        <span>${ticket.content.category}</span>
                    </div>
                    <div class="detail-item">
                        <label>地址:</label>
                        <span>${ticket.content.address}</span>
                    </div>
                    <div class="detail-item">
                        <label>描述:</label>
                        <div>${ticket.content.description}</div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>市民信息</h4>
                    <div class="detail-item">
                        <label>姓名:</label>
                        <span>${ticket.citizen.name}</span>
                    </div>
                    <div class="detail-item">
                        <label>电话:</label>
                        <span>${ticket.citizen.phone}</span>
                    </div>
                    ${ticket.citizen.isVip ? `
                    <div class="detail-item">
                        <label>VIP类型:</label>
                        <span class="vip-badge">
                            <i class="fas fa-star"></i>
                            ${ticket.citizen.vipType}
                        </span>
                    </div>
                    ` : ''}
                </div>
                
                <div class="detail-section">
                    <h4>处理信息</h4>
                    <div class="detail-item">
                        <label>当前环节:</label>
                        <span>${ticket.currentStep}</span>
                    </div>
                    <div class="detail-item">
                        <label>流转路径:</label>
                        <span>${ticket.flowPath}</span>
                    </div>
                    <div class="detail-item">
                        <label>承办单位:</label>
                        <span>${ticket.assignedUnit}</span>
                    </div>
                    ${ticket.handler ? `
                    <div class="detail-item">
                        <label>处理人:</label>
                        <span>${ticket.handler}</span>
                    </div>
                    ` : ''}
                </div>
                
                <div class="detail-section">
                    <h4>时间信息</h4>
                    <div class="detail-item">
                        <label>创建时间:</label>
                        <span>${Utils.formatDateTime(ticket.createTime)}</span>
                    </div>
                    <div class="detail-item">
                        <label>更新时间:</label>
                        <span>${Utils.formatDateTime(ticket.updateTime)}</span>
                    </div>
                    <div class="detail-item">
                        <label>时限状态:</label>
                        <span>${this.renderTimeLimitInfo(ticket.timeLimit)}</span>
                    </div>
                </div>
            </div>
        `;
    },
    
    // 其他操作方法的占位符
    editTicket: function(ticketId) { Utils.showMessage('编辑工单功能', 'info'); },
    assignTicket: function(ticketId) { Utils.showMessage('派单功能', 'info'); },
    acceptTicket: function(ticketId) { Utils.showMessage('接单功能', 'info'); },
    rejectTicket: function(ticketId) { Utils.showMessage('退回功能', 'info'); },
    submitTicket: function(ticketId) { Utils.showMessage('提交功能', 'info'); },
    deleteTicket: function(ticketId) { 
        Utils.confirm('确定要删除这个工单吗？', () => {
            Utils.showMessage('删除成功', 'success');
        });
    },
    
    /**
     * 切换行选择
     * @param {string} ticketId 工单ID
     * @param {boolean} checked 是否选中
     */
    toggleRowSelection: function(ticketId, checked) {
        if (checked) {
            this.selectedTickets.add(ticketId);
        } else {
            this.selectedTickets.delete(ticketId);
        }
        
        this.updateRowSelection(ticketId, checked);
        this.updateSelectAllCheckbox();
        this.updateBatchOperations();
    },
    
    /**
     * 更新行选择状态
     * @param {string} ticketId 工单ID
     * @param {boolean} checked 是否选中
     */
    updateRowSelection: function(ticketId, checked) {
        const row = document.querySelector(`tr[data-ticket-id="${ticketId}"]`);
        if (row) {
            if (checked) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        }
    },
    
    /**
     * 切换全选
     * @param {boolean} checked 是否全选
     */
    toggleSelectAll: function(checked) {
        this.selectedTickets.clear();
        
        if (checked) {
            this.currentData.forEach(ticket => {
                this.selectedTickets.add(ticket.id.toString());
            });
        }
        
        // 更新所有行的选择状态
        document.querySelectorAll('.ticket-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });
        
        document.querySelectorAll('tbody tr').forEach(row => {
            if (checked) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });
        
        this.updateBatchOperations();
    },
    
    /**
     * 更新全选复选框状态
     */
    updateSelectAllCheckbox: function() {
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            const totalRows = this.currentData.length;
            const selectedRows = this.selectedTickets.size;
            
            if (selectedRows === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (selectedRows === totalRows) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }
    },
    
    /**
     * 更新批量操作显示
     */
    updateBatchOperations: function() {
        const batchOperations = document.getElementById('batchOperations');
        const selectedCount = document.getElementById('selectedCount');
        
        if (batchOperations && selectedCount) {
            const count = this.selectedTickets.size;
            selectedCount.textContent = count;
            
            if (count > 0) {
                batchOperations.style.display = 'flex';
            } else {
                batchOperations.style.display = 'none';
            }
        }
    },
    
    /**
     * 选择行（单选，用于预览）
     * @param {string} ticketId 工单ID
     */
    selectRow: function(ticketId) {
        // 移除其他行的高亮
        document.querySelectorAll('tbody tr').forEach(row => {
            row.classList.remove('highlighted');
        });
        
        // 高亮当前行
        const row = document.querySelector(`tr[data-ticket-id="${ticketId}"]`);
        if (row) {
            row.classList.add('highlighted');
        }
        
        // 可以在这里添加预览功能
        // this.showTicketPreview(ticketId);
    },
    
    /**
     * 显示右键菜单
     * @param {Event} e 事件对象
     * @param {string} ticketId 工单ID
     */
    showContextMenu: function(e, ticketId) {
        // 这里可以实现右键菜单功能
        console.log('显示右键菜单', ticketId);
    },
    
    /**
     * 显示空状态
     */
    showEmptyState: function() {
        const emptyState = document.getElementById('emptyState');
        const tableBody = document.getElementById('ticketTableBody');
        
        if (emptyState) emptyState.style.display = 'flex';
        if (tableBody) tableBody.innerHTML = '';
    },
    
    /**
     * 隐藏空状态
     */
    hideEmptyState: function() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'none';
    },
    
    /**
     * 渲染分页
     */
    renderPagination: function() {
        this.renderPageNumbers();
        this.updatePaginationButtons();
        this.updateJumpPageInput();
    },
    
    /**
     * 渲染页码
     */
    renderPageNumbers: function() {
        const pageNumbers = document.getElementById('pageNumbers');
        if (!pageNumbers) return;
        
        const { current, totalPages } = this.pagination;
        let html = '';
        
        // 计算显示的页码范围
        let start = Math.max(1, current - 2);
        let end = Math.min(totalPages, current + 2);
        
        // 调整范围以显示5个页码
        if (end - start < 4) {
            if (start === 1) {
                end = Math.min(totalPages, start + 4);
            } else {
                start = Math.max(1, end - 4);
            }
        }
        
        // 第一页
        if (start > 1) {
            html += `<button class="page-number" onclick="goToPage(1)">1</button>`;
            if (start > 2) {
                html += `<span class="page-number ellipsis">...</span>`;
            }
        }
        
        // 中间页码
        for (let i = start; i <= end; i++) {
            html += `
                <button class="page-number ${i === current ? 'active' : ''}" 
                        onclick="goToPage(${i})">${i}</button>
            `;
        }
        
        // 最后一页
        if (end < totalPages) {
            if (end < totalPages - 1) {
                html += `<span class="page-number ellipsis">...</span>`;
            }
            html += `<button class="page-number" onclick="goToPage(${totalPages})">${totalPages}</button>`;
        }
        
        pageNumbers.innerHTML = html;
    },
    
    /**
     * 更新分页按钮状态
     */
    updatePaginationButtons: function() {
        const { current, totalPages } = this.pagination;
        
        const firstPageBtn = document.getElementById('firstPageBtn');
        const prevPageBtn = document.getElementById('prevPageBtn');
        const nextPageBtn = document.getElementById('nextPageBtn');
        const lastPageBtn = document.getElementById('lastPageBtn');
        
        if (firstPageBtn) firstPageBtn.disabled = current <= 1;
        if (prevPageBtn) prevPageBtn.disabled = current <= 1;
        if (nextPageBtn) nextPageBtn.disabled = current >= totalPages;
        if (lastPageBtn) lastPageBtn.disabled = current >= totalPages;
    },
    
    /**
     * 更新跳转页面输入框
     */
    updateJumpPageInput: function() {
        const jumpPageInput = document.getElementById('jumpPageInput');
        if (jumpPageInput) {
            jumpPageInput.max = this.pagination.totalPages;
            jumpPageInput.placeholder = `1-${this.pagination.totalPages}`;
        }
    },
    
    /**
     * 更新总数显示
     */
    updateTotalCount: function() {
        const totalCount = document.getElementById('totalCount');
        if (totalCount) {
            totalCount.textContent = this.pagination.total;
        }
    },
    
    /**
     * 跳转到指定页面
     * @param {number} page 页码
     */
    goToPage: function(page) {
        if (page < 1 || page > this.pagination.totalPages) return;
        if (page === this.pagination.current) return;
        
        this.pagination.current = page;
        
        // 重新加载数据
        if (window.FilterManager) {
            const params = window.FilterManager.getCurrentParams();
            params.page = page;
            params.pageSize = this.pagination.pageSize;
            
            if (window.mockAPI) {
                window.FilterManager.showLoading();
                window.mockAPI.getTickets(params).then(result => {
                    window.FilterManager.hideLoading();
                    this.updateTable(result);
                });
            }
        }
    },
    
    /**
     * 跳转到上一页
     */
    goToPrevPage: function() {
        this.goToPage(this.pagination.current - 1);
    },
    
    /**
     * 跳转到下一页
     */
    goToNextPage: function() {
        this.goToPage(this.pagination.current + 1);
    },
    
    /**
     * 跳转到最后一页
     */
    goToLastPage: function() {
        this.goToPage(this.pagination.totalPages);
    },
    
    /**
     * 跳转页面
     */
    jumpToPage: function() {
        const jumpPageInput = document.getElementById('jumpPageInput');
        if (jumpPageInput) {
            const page = parseInt(jumpPageInput.value);
            if (page) {
                this.goToPage(page);
                jumpPageInput.value = '';
            }
        }
    },
    
    /**
     * 改变页面大小
     * @param {number} pageSize 页面大小
     */
    changePageSize: function(pageSize) {
        this.pagination.pageSize = pageSize;
        this.config.pageSize = pageSize;

        // 重新加载第一页数据
        this.goToPage(1);
    },

    /**
     * 获取选中的工单数据
     * @returns {Array} 选中的工单数组
     */
    getSelectedTickets: function() {
        return this.currentData.filter(ticket =>
            this.selectedTickets.has(ticket.id.toString())
        );
    },

    /**
     * 清空选择
     */
    clearSelection: function() {
        this.selectedTickets.clear();
        this.updateSelectAllCheckbox();
        this.updateBatchOperations();

        // 更新UI
        document.querySelectorAll('.ticket-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });

        document.querySelectorAll('tbody tr').forEach(row => {
            row.classList.remove('selected');
        });
    }
};

// 全局函数，供HTML调用
window.goToPage = function(page) { window.TableManager.goToPage(page); };
window.goToPrevPage = function() { window.TableManager.goToPrevPage(); };
window.goToNextPage = function() { window.TableManager.goToNextPage(); };
window.goToLastPage = function() { window.TableManager.goToLastPage(); };
window.jumpToPage = function() { window.TableManager.jumpToPage(); };
window.changePageSize = function() { 
    const select = document.getElementById('pageSize');
    if (select) {
        window.TableManager.changePageSize(parseInt(select.value));
    }
};

window.toggleMoreActions = function(button) {
    const menu = button.nextElementSibling;
    if (menu) {
        menu.classList.toggle('show');
    }
};

// 点击其他地方关闭更多操作菜单
document.addEventListener('click', function(e) {
    if (!e.target.closest('.more-actions')) {
        document.querySelectorAll('.more-actions-menu').forEach(menu => {
            menu.classList.remove('show');
        });
    }
});
