/* 模态框样式 */

/* 模态框遮罩 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--modal-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

/* 模态框内容 */
.modal-content {
    background-color: var(--modal-content-bg);
    border-radius: var(--modal-border-radius);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(-20px);
    transition: all var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1) translateY(0);
}

/* 模态框头部 */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--modal-header-padding);
    border-bottom: 1px solid var(--border-light);
    background-color: var(--table-header-bg);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin: 0;
}

.modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    background: none;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.modal-close:hover {
    color: var(--error-color);
    background-color: var(--error-light);
}

/* 模态框主体 */
.modal-body {
    padding: var(--modal-body-padding);
    overflow-y: auto;
    max-height: 70vh;
}

/* 模态框底部 */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--modal-footer-padding);
    border-top: 1px solid var(--border-light);
    background-color: var(--bg-gray);
}

/* 高级搜索模态框 */
.advanced-search-modal .modal-content {
    width: 800px;
}

.search-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.search-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.search-section-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin: 0;
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-light);
}

.search-row {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-end;
}

.search-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
}

.search-field.compact {
    flex: 0 0 150px;
}

.search-field.wide {
    flex: 2;
}

.search-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.search-input,
.search-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
    transition: all var(--transition-normal);
}

.search-input:focus,
.search-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

/* 搜索历史 */
.search-history {
    margin-top: var(--spacing-lg);
}

.search-history-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
}

.search-history-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    max-height: 150px;
    overflow-y: auto;
}

.search-history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.search-history-item:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.search-history-remove {
    font-size: var(--font-size-xs);
    color: var(--text-disabled);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    transition: color var(--transition-normal);
}

.search-history-remove:hover {
    color: var(--error-color);
}

/* 列设置模态框 */
.column-settings-modal .modal-content {
    width: 600px;
}

.column-settings {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.column-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    max-height: 400px;
    overflow-y: auto;
}

.column-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    cursor: move;
    transition: all var(--transition-normal);
}

.column-item:hover {
    background-color: var(--bg-white);
    box-shadow: var(--shadow-sm);
}

.column-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.column-drag-handle {
    font-size: var(--font-size-sm);
    color: var(--text-disabled);
    cursor: move;
}

.column-checkbox {
    margin: 0;
}

.column-name {
    flex: 1;
    font-size: var(--font-size-sm);
    color: var(--text-color);
}

.column-width {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.column-width-input {
    width: 60px;
    padding: 2px var(--spacing-xs);
    font-size: var(--font-size-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    text-align: center;
}

.column-width-unit {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 列设置预设 */
.column-presets {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.column-preset-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.column-preset-btn:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.column-preset-btn.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

/* 导出模态框 */
.export-modal .modal-content {
    width: 500px;
}

.export-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.export-format {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.export-format-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.export-format-options {
    display: flex;
    gap: var(--spacing-sm);
}

.export-format-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.export-format-option:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.export-format-option.selected {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.export-range {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.export-range-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.export-range-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.export-range-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-color);
}

.export-progress {
    display: none;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.export-progress.show {
    display: flex;
}

.export-progress-bar {
    width: 100%;
    height: var(--progress-height);
    background-color: var(--progress-bg);
    border-radius: var(--progress-border-radius);
    overflow: hidden;
}

.export-progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: var(--progress-border-radius);
    transition: width var(--transition-normal);
    width: 0%;
}

.export-progress-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-align: center;
}

/* 响应式模态框 */
@media (max-width: 768px) {
    .modal-content {
        max-width: 95vw;
        max-height: 95vh;
        margin: var(--spacing-sm);
    }
    
    .advanced-search-modal .modal-content,
    .column-settings-modal .modal-content,
    .export-modal .modal-content {
        width: auto;
    }
    
    .search-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-field.compact,
    .search-field.wide {
        flex: 1;
    }
    
    .modal-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .modal-body {
        padding: var(--spacing-md);
    }
    
    .modal-footer {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-direction: column;
        align-items: stretch;
    }
}
