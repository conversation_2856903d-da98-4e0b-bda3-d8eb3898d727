/* CSS 变量定义 */
:root {
    /* 颜色系统 */
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --primary-active: #096dd9;
    --primary-light: #e6f7ff;
    
    --success-color: #52c41a;
    --success-light: #f6ffed;
    
    --warning-color: #faad14;
    --warning-light: #fffbe6;
    
    --error-color: #ff4d4f;
    --error-light: #fff2f0;
    
    --info-color: #13c2c2;
    --info-light: #e6fffb;
    
    /* 中性色 */
    --text-color: #262626;
    --text-secondary: #595959;
    --text-disabled: #bfbfbf;
    --text-placeholder: #d9d9d9;
    
    --bg-white: #ffffff;
    --bg-gray: #fafafa;
    --bg-light: #f5f5f5;
    --bg-dark: #001529;
    
    --border-color: #d9d9d9;
    --border-light: #f0f0f0;
    --border-dark: #434343;
    
    /* 状态颜色 */
    --status-draft: #8c8c8c;
    --status-pending: #fa8c16;
    --status-processing: #1890ff;
    --status-review: #722ed1;
    --status-callback: #13c2c2;
    --status-closed: #52c41a;
    --status-cancelled: #ff4d4f;
    --status-suspended: #faad14;
    
    /* 紧急程度颜色 */
    --urgency-normal: #52c41a;
    --urgency-urgent: #faad14;
    --urgency-critical: #ff4d4f;
    
    /* 处理模式颜色 */
    --mode-instant: #faad14;
    --mode-normal: #1890ff;
    --mode-collaborative: #52c41a;
    --mode-supervise: #ff4d4f;
    
    /* 字体系统 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    --line-height-base: 1.5;
    --line-height-sm: 1.2;
    --line-height-lg: 1.8;
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* 圆角系统 */
    --border-radius-sm: 2px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-xl: 12px;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
    --shadow-md: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
    --shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-xl: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* 过渡动画 */
    --transition-fast: 0.1s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;
    
    /* Z-index 层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    
    /* 表格相关 */
    --table-header-bg: #fafafa;
    --table-row-hover: #f5f5f5;
    --table-border: #f0f0f0;
    --table-cell-padding: 12px 16px;
    
    /* 按钮尺寸 */
    --btn-height-sm: 24px;
    --btn-height-md: 32px;
    --btn-height-lg: 40px;
    --btn-padding-sm: 0 7px;
    --btn-padding-md: 4px 15px;
    --btn-padding-lg: 6px 15px;
    
    /* 输入框尺寸 */
    --input-height-sm: 24px;
    --input-height-md: 32px;
    --input-height-lg: 40px;
    --input-padding: 4px 11px;
    
    /* 模态框 */
    --modal-bg: rgba(0, 0, 0, 0.45);
    --modal-content-bg: #ffffff;
    --modal-border-radius: 8px;
    --modal-header-padding: 16px 24px;
    --modal-body-padding: 24px;
    --modal-footer-padding: 10px 16px;
    
    /* 响应式断点 */
    --breakpoint-xs: 480px;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1600px;
    
    /* 工单状态图标 */
    --icon-draft: '📝';
    --icon-pending: '📥';
    --icon-processing: '⚙️';
    --icon-review: '🔍';
    --icon-callback: '📞';
    --icon-closed: '✅';
    --icon-cancelled: '❌';
    --icon-suspended: '⏸️';
    
    /* 紧急程度图标 */
    --icon-normal: '';
    --icon-urgent: '⚠️';
    --icon-critical: '🔥';
    
    /* 处理模式图标 */
    --icon-instant: '⚡';
    --icon-normal-mode: '🔄';
    --icon-collaborative: '🤝';
    --icon-supervise: '🚨';
    
    /* 特殊标识图标 */
    --icon-vip: '⭐';
    --icon-repeat: '🔄';
    --icon-media: '📺';
    --icon-leader: '📝';
    --icon-merge: '🔗';
    --icon-split: '📋';
    
    /* 操作图标 */
    --icon-view: '👁️';
    --icon-edit: '✏️';
    --icon-dispatch: '📤';
    --icon-follow: '📝';
    --icon-close: '❌';
    --icon-more: '⋯';
    
    /* 工具图标 */
    --icon-search: '🔍';
    --icon-filter: '🔽';
    --icon-refresh: '🔄';
    --icon-export: '📊';
    --icon-settings: '⚙️';
    --icon-add: '➕';
    
    /* 导航图标 */
    --icon-prev: '◀';
    --icon-next: '▶';
    --icon-first: '⏮';
    --icon-last: '⏭';
    
    /* 排序图标 */
    --icon-sort: '↕️';
    --icon-sort-asc: '↑';
    --icon-sort-desc: '↓';
    
    /* 选择图标 */
    --icon-check: '✓';
    --icon-uncheck: '☐';
    --icon-radio: '○';
    --icon-radio-checked: '●';
    
    /* 状态指示器尺寸 */
    --indicator-size: 8px;
    --indicator-margin: 6px;
    
    /* 标签样式 */
    --tag-height: 22px;
    --tag-padding: 0 7px;
    --tag-border-radius: 4px;
    --tag-font-size: 12px;
    
    /* 徽章样式 */
    --badge-size: 16px;
    --badge-font-size: 10px;
    --badge-border-radius: 8px;
    
    /* 进度条 */
    --progress-height: 6px;
    --progress-bg: #f5f5f5;
    --progress-border-radius: 3px;
    
    /* 加载动画 */
    --spinner-size: 20px;
    --spinner-border: 2px;
    --spinner-color: var(--primary-color);
    
    /* 工作台特定颜色 */
    --dashboard-bg: #f0f2f5;
    --card-bg: #ffffff;
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --card-hover-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    
    /* 统计卡片颜色 */
    --stat-urgent-bg: #fff2f0;
    --stat-urgent-border: #ffccc7;
    --stat-warning-bg: #fffbe6;
    --stat-warning-border: #ffe58f;
    --stat-pending-bg: #e6f7ff;
    --stat-pending-border: #91d5ff;
    --stat-merge-bg: #f6ffed;
    --stat-merge-border: #b7eb8f;
    
    /* 表格行状态颜色 */
    --row-urgent: #fff2f0;
    --row-timeout: #fffbe6;
    --row-selected: #e6f7ff;
    --row-disabled: #f5f5f5;
    
    /* 批量操作栏 */
    --batch-bg: #e6f7ff;
    --batch-border: #91d5ff;
    --batch-text: #0050b3;
}
