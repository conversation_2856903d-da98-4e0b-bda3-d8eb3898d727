/**
 * 布局样式
 * 包含容器、网格、表单布局等
 */

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: var(--bg-white);
    min-height: 100vh;
}

/* 表单头部 */
.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) 0;
    border-bottom: 2px solid var(--border-light);
    margin-bottom: var(--spacing-xl);
}

.form-header h1 {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin: 0;
}

.form-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-indicator {
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--warning-color);
    color: white;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.auto-save-status {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 表单主体 */
.ticket-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* 表单区域 */
.form-section {
    background-color: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin: 0 0 var(--spacing-lg) 0;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 60px;
    height: 2px;
    background-color: var(--primary-color);
}

/* 子区域 */
.form-group-section {
    margin-bottom: var(--spacing-xl);
}

.form-group-section:last-child {
    margin-bottom: 0;
}

.subsection-title {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin: 0 0 var(--spacing-md) 0;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

/* 表单行 */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-row:last-child {
    margin-bottom: 0;
}

/* 单列布局 */
.form-row.single-column {
    grid-template-columns: 1fr;
}

/* 两列布局 */
.form-row.two-columns {
    grid-template-columns: 1fr 1fr;
}

/* 三列布局 */
.form-row.three-columns {
    grid-template-columns: 1fr 1fr 1fr;
}

/* 表单组 */
.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.form-group:last-child {
    margin-bottom: 0;
}

/* 表单标签 */
.form-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-label.required::after {
    content: '*';
    color: var(--error-color);
    font-weight: var(--font-weight-bold);
}

/* 输入组 */
.input-group {
    display: flex;
    align-items: stretch;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: border-color var(--transition-normal);
    background-color: var(--bg-white);
}

.input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.input-group .form-input {
    border: none;
    border-radius: 0;
    flex: 1;
    background-color: transparent;
}

.input-group .form-input:focus {
    box-shadow: none;
}

.input-group .btn {
    border: none;
    border-left: 1px solid var(--border-light);
    border-radius: 0;
    background-color: var(--bg-gray);
    transition: all var(--transition-normal);
}

.input-group .btn:hover {
    background-color: var(--primary-light);
    border-left-color: var(--primary-color);
}

/* 地址输入容器 */
.address-input-container {
    display: flex;
    align-items: stretch;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: border-color var(--transition-normal);
    background-color: var(--bg-white);
}

.address-input-container:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.address-input-container .form-input {
    flex: 1;
    border: none;
    border-radius: 0;
    background-color: transparent;
}

.address-input-container .form-input:focus {
    box-shadow: none;
}

.address-input-container .btn {
    border: none;
    border-left: 1px solid var(--border-light);
    border-radius: 0;
    transition: all var(--transition-normal);
}

/* 地址建议 */
.address-suggestions {
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    max-height: 200px;
    overflow-y: auto;
    display: none;
    position: relative;
    z-index: 10;
}

.address-suggestions.show {
    display: block;
}

.address-suggestion-item {
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    border-bottom: 1px solid var(--border-light);
    transition: background-color var(--transition-fast);
}

.address-suggestion-item:hover {
    background-color: var(--bg-gray);
}

.address-suggestion-item:last-child {
    border-bottom: none;
}

/* 地图容器 */
.map-container {
    margin-top: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    height: 300px;
    overflow: hidden;
}

.map-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: var(--bg-gray);
    color: var(--text-secondary);
    font-size: var(--font-size-md);
}

/* 标签输入容器 */
.tag-input-container {
    display: flex;
    align-items: stretch;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: border-color var(--transition-normal);
    background-color: var(--bg-white);
}

.tag-input-container:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.tag-input-container .form-input {
    flex: 1;
    border: none;
    border-radius: 0;
    background-color: transparent;
}

.tag-input-container .form-input:focus {
    box-shadow: none;
}

.tag-input-container .btn {
    border: none;
    border-left: 1px solid var(--border-light);
    border-radius: 0;
    transition: all var(--transition-normal);
}

/* 上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
    transform: scale(1.02);
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.upload-icon {
    font-size: 48px;
    opacity: 0.6;
}

.upload-text p {
    margin: 0;
    font-size: var(--font-size-md);
    color: var(--text-color);
}

.upload-tips {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 附件列表 */
.attachment-list {
    margin-top: var(--spacing-md);
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

/* 表单操作区域 */
.form-actions {
    background-color: var(--bg-gray);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
    position: sticky;
    bottom: var(--spacing-lg);
    z-index: 100;
}

.action-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-small .modal-content {
    width: 400px;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.modal-body {
    padding: var(--spacing-lg);
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* 部门选择组件 */
.department-selection {
    position: relative;
}

.selected-departments {
    min-height: 44px;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-white);
    cursor: pointer;
    transition: border-color var(--transition-normal);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    align-items: center;
}

.selected-departments:hover {
    border-color: var(--primary-hover);
}

.selected-departments.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.department-placeholder {
    color: var(--text-disabled);
    font-size: var(--font-size-sm);
}

.selected-department-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.remove-department {
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
    transition: opacity var(--transition-fast);
}

.remove-department:hover {
    opacity: 1;
}

.department-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    box-shadow: var(--shadow-md);
    z-index: 100;
    display: none;
}

.department-dropdown.show {
    display: block;
}

.department-dropdown #departmentSearch {
    border: none;
    border-bottom: 1px solid var(--border-light);
    border-radius: 0;
    margin: 0;
}

.department-list {
    max-height: 200px;
    overflow-y: auto;
}

.department-item {
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    border-bottom: 1px solid var(--border-light);
    transition: background-color var(--transition-fast);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.department-item:hover {
    background-color: var(--bg-gray);
}

.department-item:last-child {
    border-bottom: none;
}

.department-item.selected {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.department-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.department-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.department-code {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-left: var(--spacing-xs);
}

.department-type {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    background-color: var(--bg-gray);
    padding: 2px var(--spacing-xs);
    border-radius: var(--border-radius-sm);
}

.empty-co-departments {
    color: var(--text-disabled);
    font-size: var(--font-size-xs);
    font-style: italic;
    padding: var(--spacing-sm);
    text-align: center;
}

/* 条件显示 */
.dispatch-only {
    display: none;
}

.form-section.dispatch-mode .dispatch-only {
    display: block;
}

.multi-dept-only {
    display: none;
}

.form-section.multi-department .multi-dept-only {
    display: block;
}

.instant-finish-only {
    display: none;
}

.form-section.instant-finish .instant-finish-only {
    display: block;
}

/* 处理模式相关样式 */
#processModeInfo .form-group-section {
    margin-bottom: var(--spacing-xl);
}

#processModeInfo .subsection-title {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

/* 时限设置区域样式 */
#deadlineSection {
    border-top: 1px solid var(--border-light);
    padding-top: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.deadline-defaults {
    background-color: var(--bg-gray);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border-left: 3px solid var(--info-color);
}

/* 地图容器样式 */
.map-container {
    margin-top: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.map-placeholder {
    height: 300px;
    background-color: var(--bg-gray);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    position: relative;
}

.map-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.6;
}

.map-text {
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.map-text p {
    margin: 0;
    margin-bottom: var(--spacing-xs);
}

.map-tips {
    font-size: var(--font-size-sm);
    color: var(--text-disabled);
}

.map-coordinates {
    position: absolute;
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    background-color: rgba(255, 255, 255, 0.9);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    display: flex;
    gap: var(--spacing-md);
}

/* 历史地址样式 */
.history-addresses {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    max-height: 200px;
    overflow-y: auto;
}

.history-address-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    background-color: var(--bg-white);
}

.history-address-item:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.address-text {
    flex: 1;
    font-size: var(--font-size-sm);
    color: var(--text-color);
    margin-right: var(--spacing-md);
}

.address-frequency {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    background-color: var(--bg-gray);
    padding: 2px var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
}

.empty-history {
    text-align: center;
    color: var(--text-disabled);
    font-size: var(--font-size-sm);
    padding: var(--spacing-lg);
    font-style: italic;
}

/* 行政区划选择样式优化 */
.form-select:disabled {
    background-color: var(--bg-gray);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.form-select:disabled option {
    color: var(--text-disabled);
}

/* 地址相关表单组 */
.form-group-section h3.subsection-title {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-light);
    padding-bottom: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
}

/* 重复电话警告 */
.repeat-phone-warning {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: #fff7e6;
    border: 1px solid var(--warning-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    color: #d48806;
    margin-top: var(--spacing-xs);
}

.warning-icon {
    font-size: var(--font-size-sm);
}

/* 字符计数器 */
.char-counter {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    text-align: right;
    margin-top: var(--spacing-xs);
}

.char-counter.over-limit {
    color: var(--error-color);
}

/* 表单帮助文本 */
.form-help {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* 表单错误 */
.form-error {
    font-size: var(--font-size-xs);
    color: var(--error-color);
    margin-top: var(--spacing-xs);
    display: none;
}

.form-error.show {
    display: block;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-light);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 成功提示 */
.success-message {
    background-color: #f6ffed;
    border: 1px solid var(--success-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    color: #389e0d;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.success-message .icon {
    font-size: var(--font-size-lg);
}

/* 错误提示 */
.error-message {
    background-color: #fff2f0;
    border: 1px solid var(--error-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    color: #cf1322;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.error-message .icon {
    font-size: var(--font-size-lg);
}
