<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话框测试页面</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .test-btn.primary {
            background: #1890ff;
            color: white;
        }
        
        .test-btn.primary:hover {
            background: #40a9ff;
        }
        
        .test-btn.success {
            background: #52c41a;
            color: white;
        }
        
        .test-btn.success:hover {
            background: #73d13d;
        }
        
        .test-btn.warning {
            background: #faad14;
            color: white;
        }
        
        .test-btn.warning:hover {
            background: #ffc53d;
        }
        
        .test-btn.danger {
            background: #ff4d4f;
            color: white;
        }
        
        .test-btn.danger:hover {
            background: #ff7875;
        }
    </style>
</head>
<body>
    <h1>对话框功能测试</h1>
    
    <div class="test-container">
        <h2>批量操作对话框测试</h2>
        <p>点击下面的按钮测试不同的对话框：</p>
        
        <div class="test-buttons">
            <button class="test-btn primary" onclick="testAssignDialog()">
                <i class="fas fa-share"></i> 测试批量派单
            </button>
            <button class="test-btn success" onclick="testApproveDialog()">
                <i class="fas fa-check"></i> 测试批量审核
            </button>
            <button class="test-btn warning" onclick="testRejectDialog()">
                <i class="fas fa-times"></i> 测试批量退回
            </button>
            <button class="test-btn danger" onclick="testDeleteDialog()">
                <i class="fas fa-trash"></i> 测试批量删除
            </button>
        </div>
    </div>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <ul>
            <li>点击按钮应该会弹出对应的对话框</li>
            <li>对话框应该居中显示，有半透明背景遮罩</li>
            <li>点击遮罩层或关闭按钮应该能关闭对话框</li>
            <li>对话框内容应该正确显示</li>
        </ul>
    </div>

    <!-- 遮罩层 -->
    <div class="overlay" id="overlay" onclick="closeDialog()"></div>

    <!-- 批量派单对话框 -->
    <div class="dialog" id="assignDialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批量派单</h3>
                <button class="dialog-close" onclick="closeDialog()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="dialog-body">
                <div class="selected-tickets-list">
                    <h4>已选择 3 个工单</h4>
                    <div class="selected-ticket-item">
                        <div class="ticket-info">
                            <div class="ticket-no">GD202412250001</div>
                            <div class="ticket-title">测试工单标题1</div>
                        </div>
                        <span class="ticket-status status-badge pending">待接收</span>
                    </div>
                    <div class="selected-ticket-item">
                        <div class="ticket-info">
                            <div class="ticket-no">GD202412250002</div>
                            <div class="ticket-title">测试工单标题2</div>
                        </div>
                        <span class="ticket-status status-badge pending">待接收</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>承办单位</label>
                    <select id="assignUnit">
                        <option value="">请选择承办单位</option>
                        <option value="unit1">市政管理局</option>
                        <option value="unit2">环保局</option>
                        <option value="unit3">交通局</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>处理人员</label>
                    <select id="assignHandler">
                        <option value="">请选择处理人员</option>
                        <option value="handler1">张三</option>
                        <option value="handler2">李四</option>
                        <option value="handler3">王五</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>备注</label>
                    <textarea id="assignRemark" placeholder="请输入派单说明（可选）"></textarea>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn-confirm" onclick="confirmAssign()">确认派单</button>
            </div>
        </div>
    </div>

    <!-- 批量审核对话框 -->
    <div class="dialog" id="approveDialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批量审核</h3>
                <button class="dialog-close" onclick="closeDialog()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="dialog-body">
                <div class="selected-tickets-list">
                    <h4>已选择 2 个工单</h4>
                </div>
                
                <div class="form-group">
                    <label>审核意见</label>
                    <textarea id="approveRemark" placeholder="请输入审核意见（可选）"></textarea>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn-confirm" onclick="confirmApprove()">确认审核</button>
            </div>
        </div>
    </div>

    <!-- 批量退回对话框 -->
    <div class="dialog" id="rejectDialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批量退回</h3>
                <button class="dialog-close" onclick="closeDialog()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="dialog-body">
                <div class="selected-tickets-list">
                    <h4>已选择 1 个工单</h4>
                </div>
                
                <div class="form-group">
                    <label>退回原因</label>
                    <select id="rejectReason">
                        <option value="">请选择退回原因</option>
                        <option value="incomplete">信息不完整</option>
                        <option value="duplicate">重复工单</option>
                        <option value="invalid">无效工单</option>
                        <option value="other">其他原因</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>详细说明</label>
                    <textarea id="rejectRemark" placeholder="请详细说明退回原因"></textarea>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn-confirm" onclick="confirmReject()">确认退回</button>
            </div>
        </div>
    </div>

    <!-- 批量删除对话框 -->
    <div class="dialog" id="deleteDialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批量删除</h3>
                <button class="dialog-close" onclick="closeDialog()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="dialog-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h4>警告</h4>
                    <p>删除操作不可恢复，请确认是否继续？</p>
                </div>
                <div class="selected-tickets-list">
                    <h4>将要删除 2 个工单</h4>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn-cancel" onclick="closeDialog()">取消</button>
                <button class="btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>

    <script>
        // 测试函数
        function testAssignDialog() {
            showDialog('assignDialog');
        }
        
        function testApproveDialog() {
            showDialog('approveDialog');
        }
        
        function testRejectDialog() {
            showDialog('rejectDialog');
        }
        
        function testDeleteDialog() {
            showDialog('deleteDialog');
        }
        
        function showDialog(dialogId) {
            const dialog = document.getElementById(dialogId);
            const overlay = document.getElementById('overlay');
            
            if (dialog && overlay) {
                dialog.classList.add('active');
                overlay.classList.add('active');
            }
        }
        
        function closeDialog() {
            document.querySelectorAll('.dialog').forEach(dialog => {
                dialog.classList.remove('active');
            });
            
            const overlay = document.getElementById('overlay');
            if (overlay) {
                overlay.classList.remove('active');
            }
        }
        
        function confirmAssign() {
            alert('派单确认！');
            closeDialog();
        }
        
        function confirmApprove() {
            alert('审核确认！');
            closeDialog();
        }
        
        function confirmReject() {
            alert('退回确认！');
            closeDialog();
        }
        
        function confirmDelete() {
            alert('删除确认！');
            closeDialog();
        }
    </script>
</body>
</html>
