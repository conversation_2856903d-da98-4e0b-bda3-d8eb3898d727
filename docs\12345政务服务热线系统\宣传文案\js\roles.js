/**
 * 角色页面专用脚本
 * 提供角色卡片的交互功能和动画效果
 */

document.addEventListener('DOMContentLoaded', function() {
    initRolesPage();
});

/**
 * 初始化角色页面
 */
function initRolesPage() {
    // 初始化角色卡片动画
    initRoleCardAnimations();
    
    // 初始化统计数字动画
    initStatsAnimation();
    
    // 初始化角色筛选功能
    initRoleFiltering();
    
    // 角色详情展开功能已移除，内容默认完全显示
}

/**
 * 初始化角色卡片动画
 */
function initRoleCardAnimations() {
    const roleCards = document.querySelectorAll('.role-card');
    
    // 创建交叉观察器
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                }, index * 100);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    roleCards.forEach(card => {
        observer.observe(card);
    });
}

/**
 * 初始化统计数字动画
 */
function initStatsAnimation() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const finalValue = target.textContent;
                
                if (!isNaN(finalValue)) {
                    animateNumber(target, 0, parseInt(finalValue), 1500);
                }
                
                observer.unobserve(target);
            }
        });
    });
    
    statNumbers.forEach(stat => {
        observer.observe(stat);
    });
}

/**
 * 数字动画函数
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(start + (end - start) * easeOutCubic);
        
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(update);
        } else {
            element.textContent = end;
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * 初始化角色筛选功能
 */
function initRoleFiltering() {
    // 创建筛选按钮
    createFilterButtons();
    
    // 绑定筛选事件
    bindFilterEvents();
}

/**
 * 创建筛选按钮
 */
function createFilterButtons() {
    const rolesOverview = document.querySelector('.roles-overview .container');
    
    if (!rolesOverview) return;
    
    const filterContainer = document.createElement('div');
    filterContainer.className = 'role-filters';
    filterContainer.innerHTML = `
        <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">
                <i class="fas fa-users"></i>
                全部角色
            </button>
            <button class="filter-btn" data-filter="external">
                <i class="fas fa-user"></i>
                外部角色
            </button>
            <button class="filter-btn" data-filter="core">
                <i class="fas fa-users-cog"></i>
                核心业务角色
            </button>
            <button class="filter-btn" data-filter="support">
                <i class="fas fa-user-shield"></i>
                管理支持角色
            </button>
        </div>
    `;
    
    rolesOverview.appendChild(filterContainer);
    
    // 添加筛选按钮样式
    const style = document.createElement('style');
    style.textContent = `
        .role-filters {
            margin-top: 2rem;
            text-align: center;
        }
        
        .filter-buttons {
            display: inline-flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .filter-btn {
            padding: 0.75rem 1.5rem;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
        }
        
        .filter-btn:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-2px);
        }
        
        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }
        
        @media (max-width: 768px) {
            .filter-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .filter-btn {
                width: 200px;
                justify-content: center;
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * 绑定筛选事件
 */
function bindFilterEvents() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // 更新按钮状态
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // 执行筛选
            filterRoles(filter);
        });
    });
}

/**
 * 筛选角色
 */
function filterRoles(filter) {
    const roleCategories = document.querySelectorAll('.role-category');
    
    roleCategories.forEach(category => {
        const categoryTitle = category.querySelector('.category-title').textContent;
        let shouldShow = false;
        
        if (filter === 'all') {
            shouldShow = true;
        } else if (filter === 'external' && categoryTitle.includes('外部角色')) {
            shouldShow = true;
        } else if (filter === 'core' && categoryTitle.includes('核心业务角色')) {
            shouldShow = true;
        } else if (filter === 'support' && categoryTitle.includes('管理与支持角色')) {
            shouldShow = true;
        }
        
        if (shouldShow) {
            category.style.display = 'block';
            category.style.animation = 'fadeInUp 0.6s ease-out';
        } else {
            category.style.display = 'none';
        }
    });
}

// 展开功能已移除，角色内容默认完全显示

/**
 * 角色搜索功能
 */
function initRoleSearch() {
    // 创建搜索框
    const searchContainer = document.createElement('div');
    searchContainer.className = 'role-search';
    searchContainer.innerHTML = `
        <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="搜索角色..." id="roleSearchInput">
            <button type="button" id="clearSearch" style="display: none;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    const rolesOverview = document.querySelector('.roles-overview .container');
    if (rolesOverview) {
        rolesOverview.insertBefore(searchContainer, rolesOverview.firstChild);
    }
    
    // 绑定搜索事件
    const searchInput = document.getElementById('roleSearchInput');
    const clearButton = document.getElementById('clearSearch');
    
    searchInput.addEventListener('input', debounce(handleSearch, 300));
    clearButton.addEventListener('click', clearSearch);
    
    // 添加搜索框样式
    const style = document.createElement('style');
    style.textContent = `
        .role-search {
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .search-box {
            position: relative;
            display: inline-block;
            max-width: 400px;
            width: 100%;
        }
        
        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s ease;
        }
        
        .search-box input:focus {
            border-color: #667eea;
        }
        
        .search-box button {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            border: none;
            background: #f8f9fa;
            border-radius: 50%;
            cursor: pointer;
            color: #666;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 处理搜索
 */
function handleSearch(event) {
    const searchTerm = event.target.value.toLowerCase().trim();
    const clearButton = document.getElementById('clearSearch');
    
    if (searchTerm) {
        clearButton.style.display = 'block';
        filterRolesBySearch(searchTerm);
    } else {
        clearButton.style.display = 'none';
        showAllRoles();
    }
}

/**
 * 根据搜索词筛选角色
 */
function filterRolesBySearch(searchTerm) {
    const roleCards = document.querySelectorAll('.role-card');
    
    roleCards.forEach(card => {
        const roleName = card.querySelector('.role-name').textContent.toLowerCase();
        const roleSubtitle = card.querySelector('.role-subtitle').textContent.toLowerCase();
        const roleContent = card.querySelector('.role-content').textContent.toLowerCase();
        
        const isMatch = roleName.includes(searchTerm) || 
                       roleSubtitle.includes(searchTerm) || 
                       roleContent.includes(searchTerm);
        
        if (isMatch) {
            card.style.display = 'block';
            highlightSearchTerm(card, searchTerm);
        } else {
            card.style.display = 'none';
        }
    });
}

/**
 * 高亮搜索词
 */
function highlightSearchTerm(card, searchTerm) {
    const textElements = card.querySelectorAll('.role-name, .role-subtitle, .role-content p, .role-content li');
    
    textElements.forEach(element => {
        const originalText = element.textContent;
        const highlightedText = originalText.replace(
            new RegExp(searchTerm, 'gi'),
            `<mark>$&</mark>`
        );
        
        if (highlightedText !== originalText) {
            element.innerHTML = highlightedText;
        }
    });
}

/**
 * 清除搜索
 */
function clearSearch() {
    const searchInput = document.getElementById('roleSearchInput');
    const clearButton = document.getElementById('clearSearch');
    
    searchInput.value = '';
    clearButton.style.display = 'none';
    
    showAllRoles();
    clearHighlights();
}

/**
 * 显示所有角色
 */
function showAllRoles() {
    const roleCards = document.querySelectorAll('.role-card');
    roleCards.forEach(card => {
        card.style.display = 'block';
    });
}

/**
 * 清除高亮
 */
function clearHighlights() {
    const highlights = document.querySelectorAll('mark');
    highlights.forEach(mark => {
        const parent = mark.parentNode;
        parent.replaceChild(document.createTextNode(mark.textContent), mark);
        parent.normalize();
    });
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 初始化搜索功能
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initRoleSearch, 500);
});
