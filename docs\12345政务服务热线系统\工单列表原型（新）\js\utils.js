/**
 * 工具函数库
 * 包含通用的工具函数和辅助方法
 */

/**
 * 工具函数命名空间
 */
window.Utils = {
    /**
     * 格式化日期时间
     * @param {string|Date} date 日期
     * @param {string} format 格式 'YYYY-MM-DD HH:mm:ss'
     * @returns {string} 格式化后的日期字符串
     */
    formatDateTime: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 格式化相对时间
     * @param {string|Date} date 日期
     * @returns {string} 相对时间字符串
     */
    formatRelativeTime: function(date) {
        if (!date) return '';
        
        const now = new Date();
        const target = new Date(date);
        const diff = now - target;
        
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return this.formatDateTime(date, 'MM-DD HH:mm');
    },
    
    /**
     * 计算剩余时间
     * @param {string} deadline 截止时间
     * @returns {Object} 剩余时间信息
     */
    calculateRemainingTime: function(deadline) {
        if (!deadline) return { text: '', level: 'normal', isOvertime: false };
        
        const now = new Date();
        const target = new Date(deadline);
        const diff = target - now;
        
        if (diff <= 0) {
            const overDays = Math.floor(Math.abs(diff) / (1000 * 60 * 60 * 24));
            return {
                text: `超时${overDays}天`,
                level: 'danger',
                isOvertime: true
            };
        }
        
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        
        let text = '';
        let level = 'normal';
        
        if (days > 0) {
            text = `${days}天${hours}小时`;
            level = days > 2 ? 'normal' : days > 1 ? 'warning' : 'danger';
        } else if (hours > 0) {
            text = `${hours}小时${minutes}分钟`;
            level = hours > 4 ? 'warning' : 'danger';
        } else {
            text = `${minutes}分钟`;
            level = 'danger';
        }
        
        return { text, level, isOvertime: false };
    },
    
    /**
     * 脱敏处理
     * @param {string} str 原始字符串
     * @param {string} type 脱敏类型 'name', 'phone', 'idcard'
     * @returns {string} 脱敏后的字符串
     */
    maskSensitiveInfo: function(str, type) {
        if (!str) return '';
        
        switch (type) {
            case 'name':
                if (str.length <= 2) return str;
                return str.charAt(0) + '*'.repeat(str.length - 2) + str.charAt(str.length - 1);
            
            case 'phone':
                if (str.length !== 11) return str;
                return str.substring(0, 3) + '****' + str.substring(7);
            
            case 'idcard':
                if (str.length < 8) return str;
                return str.substring(0, 4) + '****' + str.substring(str.length - 4);
            
            default:
                return str;
        }
    },
    
    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 时间限制
     * @returns {Function} 节流后的函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * 深拷贝对象
     * @param {*} obj 要拷贝的对象
     * @returns {*} 拷贝后的对象
     */
    deepClone: function(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },
    
    /**
     * 获取嵌套对象属性值
     * @param {Object} obj 对象
     * @param {string} path 属性路径，如 'user.profile.name'
     * @param {*} defaultValue 默认值
     * @returns {*} 属性值
     */
    getNestedValue: function(obj, path, defaultValue = undefined) {
        if (!obj || !path) return defaultValue;
        
        const keys = path.split('.');
        let result = obj;
        
        for (const key of keys) {
            if (result === null || result === undefined || !result.hasOwnProperty(key)) {
                return defaultValue;
            }
            result = result[key];
        }
        
        return result;
    },
    
    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId: function() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    /**
     * 显示消息提示
     * @param {string} message 消息内容
     * @param {string} type 消息类型 'success', 'warning', 'error', 'info'
     * @param {number} duration 显示时长（毫秒）
     */
    showMessage: function(message, type = 'info', duration = 3000) {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.innerHTML = `
            <i class="fas fa-${this.getMessageIcon(type)}"></i>
            <span>${message}</span>
            <button class="message-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // 添加样式
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: this.getMessageColor(type),
            color: 'white',
            padding: '12px 16px',
            borderRadius: '4px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: '9999',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            maxWidth: '400px',
            animation: 'slideInRight 0.3s ease-out'
        });
        
        // 添加到页面
        document.body.appendChild(messageEl);
        
        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                if (messageEl.parentElement) {
                    messageEl.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => messageEl.remove(), 300);
                }
            }, duration);
        }
    },
    
    /**
     * 获取消息图标
     * @param {string} type 消息类型
     * @returns {string} 图标类名
     */
    getMessageIcon: function(type) {
        const icons = {
            success: 'check-circle',
            warning: 'exclamation-triangle',
            error: 'times-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    /**
     * 获取消息颜色
     * @param {string} type 消息类型
     * @returns {string} 颜色值
     */
    getMessageColor: function(type) {
        const colors = {
            success: '#52c41a',
            warning: '#faad14',
            error: '#ff4d4f',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    },
    
    /**
     * 确认对话框
     * @param {string} message 确认消息
     * @param {Function} onConfirm 确认回调
     * @param {Function} onCancel 取消回调
     */
    confirm: function(message, onConfirm, onCancel) {
        const confirmed = window.confirm(message);
        if (confirmed && onConfirm) {
            onConfirm();
        } else if (!confirmed && onCancel) {
            onCancel();
        }
    },
    
    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 导出数据为CSV
     * @param {Array} data 数据数组
     * @param {string} filename 文件名
     * @param {Array} headers 表头
     */
    exportToCSV: function(data, filename, headers) {
        let csvContent = '';
        
        // 添加表头
        if (headers && headers.length > 0) {
            csvContent += headers.join(',') + '\n';
        }
        
        // 添加数据行
        data.forEach(row => {
            const values = headers ? headers.map(header => row[header] || '') : Object.values(row);
            csvContent += values.map(value => `"${String(value).replace(/"/g, '""')}"`).join(',') + '\n';
        });
        
        // 创建下载链接
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    },
    
    /**
     * 获取URL参数
     * @param {string} name 参数名
     * @returns {string|null} 参数值
     */
    getUrlParameter: function(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },
    
    /**
     * 设置URL参数
     * @param {string} name 参数名
     * @param {string} value 参数值
     */
    setUrlParameter: function(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.replaceState({}, '', url);
    }
};

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .message-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 0;
        margin-left: auto;
    }
`;
document.head.appendChild(style);
