/**
 * 12345政务服务热线系统核心角色可视化主程序
 */

// 全局变量
let currentCategory = 'all';
let isInitialized = false;

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，开始初始化...');
    
    // 等待Mermaid加载完成
    if (typeof mermaid !== 'undefined') {
        initializePage();
    } else {
        // 如果Mermaid还没加载完成，等待一下
        setTimeout(() => {
            if (typeof mermaid !== 'undefined') {
                initializePage();
            } else {
                console.error('Mermaid库加载失败');
                showError('图表库加载失败，请刷新页面重试');
            }
        }, 1000);
    }
});

/**
 * 初始化页面
 */
function initializePage() {
    try {
        console.log('开始初始化页面...');
        
        // 渲染角色关系图
        renderRoleDiagram('hierarchy');
        
        // 显示所有角色卡片
        showCategory('all');
        
        // 更新统计信息
        updateStatistics();
        
        // 添加图表类型切换按钮
        addDiagramControls();
        
        isInitialized = true;
        console.log('页面初始化完成');
        
    } catch (error) {
        console.error('页面初始化失败:', error);
        showError('页面初始化失败，请刷新页面重试');
    }
}

/**
 * 显示指定类别的角色
 */
function showCategory(category, buttonElement) {
    currentCategory = category;
    
    // 更新按钮状态
    updateCategoryButtons(buttonElement);
    
    // 获取角色数据
    const roles = getRolesByCategory(category);
    
    // 渲染角色卡片
    renderRoleCards(roles);
    
    console.log(`显示${category}类别的角色，共${roles.length}个`);
}

/**
 * 更新分类按钮状态
 */
function updateCategoryButtons(activeButton) {
    // 移除所有按钮的active类
    document.querySelectorAll('.category-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 为当前按钮添加active类
    if (activeButton) {
        activeButton.classList.add('active');
    }
}

/**
 * 渲染角色卡片
 */
function renderRoleCards(roles) {
    const container = document.getElementById('roles-grid');
    if (!container) return;
    
    // 清空容器
    container.innerHTML = '';
    
    // 渲染每个角色卡片
    roles.forEach((role, index) => {
        const card = createRoleCard(role);
        container.appendChild(card);
        
        // 添加延迟动画效果
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
}

/**
 * 创建角色卡片
 */
function createRoleCard(role) {
    const card = document.createElement('div');
    card.className = `role-card ${role.category}`;
    card.setAttribute('data-role', role.id);
    
    // 处理职责列表
    let responsibilitiesHtml = '';
    if (Array.isArray(role.responsibilities)) {
        if (typeof role.responsibilities[0] === 'string') {
            // 简单字符串数组
            responsibilitiesHtml = role.responsibilities.map(resp => `<li>${resp}</li>`).join('');
        } else {
            // 对象数组（包含title和description）
            responsibilitiesHtml = role.responsibilities.map(resp => {
                if (typeof resp === 'object' && resp.title) {
                    return `<li><strong>${resp.title}：</strong>${resp.description}</li>`;
                } else {
                    return `<li>${resp}</li>`;
                }
            }).join('');
        }
    }
    
    // 包含信息
    const includesHtml = role.includes ? 
        `<div class="role-includes"><strong>包含：</strong>${role.includes}</div>` : '';
    
    card.innerHTML = `
        <div class="role-header">
            <div class="role-icon">${role.icon}</div>
            <h3>${role.name}</h3>
            <span class="role-type">${getCategoryName(role.category)}</span>
        </div>
        <div class="role-content">
            <div class="role-position">${role.position}</div>
            <div class="role-responsibilities">
                <h4>核心职责：</h4>
                <ul>${responsibilitiesHtml}</ul>
            </div>
            ${includesHtml}
        </div>
    `;
    
    return card;
}

/**
 * 获取分类中文名称
 */
function getCategoryName(category) {
    const categoryNames = {
        'external': '外部角色',
        'business': '核心业务角色',
        'support': '管理支持角色'
    };
    return categoryNames[category] || '未知类别';
}

/**
 * 更新统计信息
 */
function updateStatistics() {
    const stats = getRoleStatistics();
    
    // 更新统计数字
    const statItems = document.querySelectorAll('.stat-item');
    if (statItems.length >= 4) {
        statItems[0].querySelector('.stat-number').textContent = stats.external;
        statItems[1].querySelector('.stat-number').textContent = stats.business;
        statItems[2].querySelector('.stat-number').textContent = stats.support;
        statItems[3].querySelector('.stat-number').textContent = stats.total;
    }
}

/**
 * 添加图表控制按钮
 */
function addDiagramControls() {
    const diagramContainer = document.querySelector('.role-diagram-container');
    if (!diagramContainer) return;
    
    const controlsHtml = `
        <div class="diagram-controls" style="text-align: center; margin-bottom: 15px;">
            <button class="diagram-type-btn active" onclick="switchDiagramView('hierarchy', this)">层级图</button>
            <button class="diagram-type-btn" onclick="switchDiagramView('detailed', this)">详细图</button>
            <button class="diagram-type-btn" onclick="switchDiagramView('simplified', this)">简化图</button>
        </div>
    `;
    
    // 在h3标题后插入控制按钮
    const h3 = diagramContainer.querySelector('h3');
    if (h3) {
        h3.insertAdjacentHTML('afterend', controlsHtml);
    }
    
    // 添加按钮样式
    const style = document.createElement('style');
    style.textContent = `
        .diagram-type-btn {
            margin: 0 5px;
            padding: 8px 16px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .diagram-type-btn:hover {
            background: #3498db;
            color: white;
        }
        .diagram-type-btn.active {
            background: #3498db;
            color: white;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 切换图表视图
 */
function switchDiagramView(type, buttonElement) {
    // 更新按钮状态
    document.querySelectorAll('.diagram-type-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    if (buttonElement) {
        buttonElement.classList.add('active');
    }
    
    // 切换图表
    renderRoleDiagram(type);
}

/**
 * 显示错误信息
 */
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #e74c3c;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 3000);
}

/**
 * 搜索角色
 */
function searchRoles(keyword) {
    if (!keyword.trim()) {
        showCategory(currentCategory);
        return;
    }

    const allRoles = getAllRoles();
    const filteredRoles = allRoles.filter(role => {
        const searchText = keyword.toLowerCase();

        // 搜索角色名称
        if (role.name.toLowerCase().includes(searchText)) return true;

        // 搜索职位描述
        if (role.position.toLowerCase().includes(searchText)) return true;

        // 搜索包含信息
        if (role.includes && role.includes.toLowerCase().includes(searchText)) return true;

        // 搜索职责内容
        if (Array.isArray(role.responsibilities)) {
            for (let resp of role.responsibilities) {
                if (typeof resp === 'string' && resp.toLowerCase().includes(searchText)) {
                    return true;
                } else if (typeof resp === 'object') {
                    if (resp.title && resp.title.toLowerCase().includes(searchText)) return true;
                    if (resp.description && resp.description.toLowerCase().includes(searchText)) return true;
                }
            }
        }

        return false;
    });

    renderRoleCards(filteredRoles);

    // 更新搜索结果提示
    updateSearchResultsInfo(filteredRoles.length, keyword);
}

/**
 * 处理搜索输入
 */
function handleSearch(event) {
    if (event.key === 'Enter') {
        performSearch();
    } else {
        // 实时搜索（防抖）
        clearTimeout(window.searchTimeout);
        window.searchTimeout = setTimeout(() => {
            performSearch();
        }, 300);
    }
}

/**
 * 执行搜索
 */
function performSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        const keyword = searchInput.value.trim();
        searchRoles(keyword);
    }
}

/**
 * 更新搜索结果信息
 */
function updateSearchResultsInfo(count, keyword) {
    // 移除之前的搜索结果提示
    const existingInfo = document.querySelector('.search-results-info');
    if (existingInfo) {
        existingInfo.remove();
    }

    if (keyword) {
        const infoDiv = document.createElement('div');
        infoDiv.className = 'search-results-info';
        infoDiv.style.cssText = `
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background: #e8f4fd;
            border-radius: 8px;
            color: #2c3e50;
        `;
        infoDiv.innerHTML = `
            搜索 "<strong>${keyword}</strong>" 找到 <strong>${count}</strong> 个相关角色
            <button onclick="clearSearch()" style="margin-left: 10px; padding: 4px 8px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">清除搜索</button>
        `;

        const rolesGrid = document.getElementById('roles-grid');
        if (rolesGrid) {
            rolesGrid.parentNode.insertBefore(infoDiv, rolesGrid);
        }
    }
}

/**
 * 清除搜索
 */
function clearSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.value = '';
    }

    // 移除搜索结果提示
    const existingInfo = document.querySelector('.search-results-info');
    if (existingInfo) {
        existingInfo.remove();
    }

    // 显示当前分类的所有角色
    showCategory(currentCategory);
}

// 导出全局函数
window.showCategory = showCategory;
window.switchDiagramView = switchDiagramView;
window.searchRoles = searchRoles;
window.handleSearch = handleSearch;
window.performSearch = performSearch;
window.clearSearch = clearSearch;
