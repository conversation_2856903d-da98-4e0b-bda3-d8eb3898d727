
---

### **“情感分析设置”页面内容详解**

#### **一、 页面核心目标**

1.  **启用与配置模型**: 选择并启用情感分析模型，设定其分析的敏感度和语言。
2.  **定义情感阈值**: 将模型输出的连续性情感分数（如-1到1），映射为业务上可理解的离散标签（如：负面、中性、正面）。
3.  **管理自定义词典**: 允许管理员根据自身业务特点，干预和修正模型对特定词汇的情感判断。
4.  **设定自动化触发器**: 将识别出的情感状态与后续的自动化规则进行联动，实现主动的服务干预。

---

### **二、 页面内容与布局**

这个页面的配置相对“意图识别”要轻量一些，通常采用**单页卡片式**的布局，将各项配置清晰地组织起来。

#### **模块一：主开关与模型配置 (Master Switch & Model Configuration)**

*   **`[ 启用工单情感分析 ]`**: 一个总开关，用于开启或关闭整个情感分析功能。
*   **分析目标文本 (Target Text for Analysis)**:
    *   一个多选框，让管理员选择对哪些文本进行情感分析。
    *   `[✓] 工单描述 (首次创建时)`
    *   `[✓] 客户的每一次回复/补记`
    *   `[ ] 处理人员的回复` (可选，用于分析员工的服务用语)
*   **分析语言 (Analysis Language)**:
    *   一个下拉菜单，选择主要分析的语言（如：中文、英文、多语言混合）。这会影响调用的后端模型。
*   **情感分析模型选择 (可选，高级功能)**:
    *   如果系统集成了多个情感分析引擎，这里可以提供选择，如 `系统内置模型`, `Google Natural Language API`, `Azure Text Analytics` 等。

#### **模块二：情感分类与阈值定义 (Sentiment Classification & Thresholds)**

这是将技术分数转化为业务标签的核心区域。

*   **情感分数范围**: 页面会显示模型输出的分数范围，通常是 `-1.0` (极度负面) 到 `+1.0` (极度正面)。
*   **阈值设置滑块/输入框**:
    *   **负面 (Negative)**: 当分数低于 `[ -0.25 ]` 时，被归类为负面。
    *   **中性 (Neutral)**: 当分数在 `[ -0.25 ]` 和 `[ 0.25 ]` 之间时，被归类为中性。
    *   **正面 (Positive)**: 当分数高于 `[ 0.25 ]` 时，被归类为正面。
    *   管理员可以通过拖动滑块或直接输入数字来调整这些阈值，以适应自己业务的敏感度要求。

#### **模块三：自定义情感词典 (Custom Sentiment Dictionary)**

这是让情感分析**“更懂你的业务”**的关键。模型可能对通用语言判断很准，但对行业术语或公司内部用语可能会误判。

*   **页面顶部**: `[ + 添加自定义词 ]` 按钮。
*   **词典列表**:
    *   **表头**: `词汇/短语`, `情感倾向`, `权重(可选)`, `操作`。
    *   **行内操作**: `[ 编辑 ]`, `[ 删除 ]`。
*   **新建/编辑词汇弹窗**:
    *   `词汇/短语`: [必填] 输入一个特定的词，如“坑爹”、“死机”、“完美”、“给力”。
    *   `情感倾向`: [必填] 一个下拉菜单，选择 `[ 正面 ]`, `[ 中性 ]`, `[ 负面 ]`。
    *   `权重/分数 (可选)`: 输入一个-1到1之间的数字，强制覆盖模型对这个词的判断。例如，虽然“死机”本身是负面的，但如果客户说“还好没有死机”，模型可能会误判。在这里，你可以将“死机”强制设定为一个非常低的负分，提升其在句子中的影响力。
*   **批量导入/导出**: 支持通过CSV文件批量管理自定义词典。

**示例**:
*   一个游戏公司，玩家说“这个副本好‘肝’啊”。通用模型可能不理解“肝”的负面疲惫含义。管理员就可以在词典里添加 `[肝]`，情感倾向设为 `[负面]`。
*   一个金融公司，“平仓”这个词本身是中性的，但管理员可以根据业务需要调整其权重。

#### **模块四：自动化联动配置 (Automation Linking)**

这部分将情感洞察转化为实际行动。

*   **快速规则配置**:
    *   一个简化的规则列表，专注于情感触发。
    *   **规则1**: `当新工单/回复的情感被识别为【负面】时:`
        *   `自动添加标签`: `[ 客户情绪预警 ]` (下拉多选)
        *   `自动提升优先级至`: `[ 高 ]` (下拉选择)
        *   `立即通知`: `[ 客户成功经理团队 ]` (人员/团队选择器)
    *   **规则2**: `当新工单/回复的情感被识别为【正面】时:`
        *   `自动添加标签`: `[ 潜在好评客户 ]`
*   **高级规则链接**:
    *   一个链接：“**前往[自动化规则]页面创建更复杂的规则**”。点击后，会跳转到完整的自动化规则构建器，在那里，`情感得分` 或 `情感分类` 都可以作为一个可用的**条件字段**。

#### **模块五：快速测试区 (Quick Test)**

*   一个文本输入框：“**在此测试情感分析效果...**”
*   管理员输入一段文本，如“你们这个更新真是太棒了，不过好像有个小bug总是闪退，有点烦人。”
*   点击“分析”后，系统返回结果：
    *   **总体情感**: `中性` (或略偏负面)
    *   **情感得分**: `-0.15`
    *   **关键词分析**: `棒` (正面), `bug` (负面), `闪退` (负面), `烦人` (负面)

---