/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 30px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    text-align: center;
    padding: 40px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    margin-bottom: 30px;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 300;
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 内容区域 */
.content {
    padding: 0 20px 40px;
}

/* 搜索功能 */
.search-container {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.search-box {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #3498db;
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
    transition: all 0.3s ease;
    max-width: 500px;
    width: 100%;
}

.search-box:focus-within {
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
    transform: translateY(-2px);
}

#search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 12px 20px;
    font-size: 16px;
    background: transparent;
    color: #2c3e50;
}

#search-input::placeholder {
    color: #95a5a6;
}

.search-btn {
    background: #3498db;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 16px;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    background: #2980b9;
    transform: scale(1.1);
}

/* 角色分类导航 */
.role-categories {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 12px 24px;
    border: 2px solid #3498db;
    background: white;
    color: #3498db;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    outline: none;
}

.category-btn:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.category-btn.active {
    background: #3498db;
    color: white;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* 角色关系图容器 */
.role-diagram-container {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.role-diagram-container h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.5em;
}

.diagram-wrapper {
    width: 100%;
    min-height: 400px;
    overflow: auto;
    border-radius: 10px;
    background: white;
    padding: 20px;
}

/* 角色卡片网格 */
.roles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

/* 角色卡片样式 */
.role-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 5px solid #3498db;
}

.role-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.role-card.external {
    border-left-color: #e74c3c;
}

.role-card.business {
    border-left-color: #3498db;
}

.role-card.support {
    border-left-color: #f39c12;
}

/* 角色卡片头部 */
.role-header {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.role-icon {
    font-size: 2.5em;
    text-align: center;
    margin-bottom: 10px;
}

.role-header h3 {
    font-size: 1.3em;
    color: #2c3e50;
    margin-bottom: 8px;
    text-align: center;
}

.role-type {
    display: block;
    text-align: center;
    font-size: 0.9em;
    color: #6c757d;
    background: #e9ecef;
    padding: 4px 12px;
    border-radius: 12px;
    display: inline-block;
    margin: 0 auto;
}

.role-card.external .role-type {
    background: #ffeaea;
    color: #e74c3c;
}

.role-card.business .role-type {
    background: #e3f2fd;
    color: #3498db;
}

.role-card.support .role-type {
    background: #fff3e0;
    color: #f39c12;
}

/* 角色卡片内容 */
.role-content {
    padding: 20px;
}

.role-position {
    font-size: 1.1em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 8px;
}

.role-responsibilities h4 {
    color: #34495e;
    margin-bottom: 10px;
    font-size: 1em;
}

.role-responsibilities ul {
    list-style: none;
    padding-left: 0;
}

.role-responsibilities li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    line-height: 1.5;
}

.role-responsibilities li:before {
    content: "▶";
    position: absolute;
    left: 0;
    color: #3498db;
    font-size: 0.8em;
}

.role-includes {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 0.9em;
    color: #6c757d;
}

/* 角色统计 */
.role-statistics {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 30px;
    color: white;
    margin-bottom: 40px;
}

.role-statistics h3 {
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.5em;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1em;
    opacity: 0.9;
}

/* 页脚样式 */
.footer {
    text-align: center;
    padding: 20px;
    background: #34495e;
    color: white;
    font-size: 14px;
}

/* 隐藏/显示动画 */
.role-card.hidden {
    display: none;
}

.role-card.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mermaid图表样式优化 */
#role-diagram .node rect {
    fill: #f9f9f9;
    stroke: #333;
    stroke-width: 2px;
}

#role-diagram .node text {
    font-family: 'Microsoft YaHei', sans-serif;
    font-size: 14px;
}

#role-diagram .edgePath path {
    stroke: #666;
    stroke-width: 2px;
}

#role-diagram .edgeLabel {
    background-color: white;
    border-radius: 4px;
    padding: 2px 4px;
    font-size: 10px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
