/**
 * 功能操作页面专用脚本
 * 提供操作项的交互功能和筛选
 */

document.addEventListener('DOMContentLoaded', function() {
    initOperationsPage();
});

/**
 * 初始化功能操作页面
 */
function initOperationsPage() {
    // 初始化操作项动画
    initOperationAnimations();
    
    // 初始化操作筛选
    initOperationFiltering();
    
    // 初始化操作搜索
    initOperationSearch();
    
    // 初始化操作详情
    initOperationDetails();
    
    // 初始化统计动画
    initStatsAnimation();
}

/**
 * 初始化操作项动画
 */
function initOperationAnimations() {
    const operationItems = document.querySelectorAll('.operation-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                }, index * 50);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    operationItems.forEach(item => {
        observer.observe(item);
    });
}

/**
 * 初始化操作筛选
 */
function initOperationFiltering() {
    createFilterInterface();
    bindFilterEvents();
}

/**
 * 创建筛选界面
 */
function createFilterInterface() {
    const operationsOverview = document.querySelector('.operations-overview .container');
    
    if (!operationsOverview) return;
    
    const filterContainer = document.createElement('div');
    filterContainer.className = 'operation-filters';
    filterContainer.innerHTML = `
        <div class="filter-section">
            <h3>操作筛选</h3>
            <div class="filter-groups">
                <div class="filter-group">
                    <h4>按阶段筛选</h4>
                    <div class="filter-buttons stage-filters">
                        <button class="filter-btn active" data-filter="all">全部阶段</button>
                        <button class="filter-btn" data-filter="create">创建与分派</button>
                        <button class="filter-btn" data-filter="process">处理与协作</button>
                        <button class="filter-btn" data-filter="audit">逐级审核</button>
                        <button class="filter-btn" data-filter="revisit">回访关闭</button>
                        <button class="filter-btn" data-filter="global">全局操作</button>
                    </div>
                </div>
                
                <div class="filter-group">
                    <h4>按角色筛选</h4>
                    <div class="filter-buttons role-filters">
                        <button class="filter-btn active" data-filter="all">全部角色</button>
                        <button class="filter-btn" data-filter="citizen">市民</button>
                        <button class="filter-btn" data-filter="operator">话务员</button>
                        <button class="filter-btn" data-filter="processor">处理人</button>
                        <button class="filter-btn" data-filter="manager">管理者</button>
                        <button class="filter-btn" data-filter="revisitor">回访员</button>
                    </div>
                </div>
                
                <div class="filter-group">
                    <h4>按重要性筛选</h4>
                    <div class="filter-buttons importance-filters">
                        <button class="filter-btn active" data-filter="all">全部操作</button>
                        <button class="filter-btn" data-filter="primary">核心操作</button>
                        <button class="filter-btn" data-filter="highlight">重点操作</button>
                        <button class="filter-btn" data-filter="normal">常规操作</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    operationsOverview.appendChild(filterContainer);
    addFilterStyles();
}

/**
 * 添加筛选样式
 */
function addFilterStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .operation-filters {
            margin-top: 2rem;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .filter-section h3 {
            margin-bottom: 1.5rem;
            color: #333;
            text-align: center;
        }
        
        .filter-groups {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .filter-group h4 {
            margin-bottom: 1rem;
            color: #555;
            font-size: 1rem;
        }
        
        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            color: #666;
        }
        
        .filter-btn:hover {
            border-color: #667eea;
            color: #667eea;
        }
        
        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }
        
        @media (max-width: 768px) {
            .filter-groups {
                gap: 1rem;
            }
            
            .filter-buttons {
                justify-content: center;
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * 绑定筛选事件
 */
function bindFilterEvents() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterGroup = this.closest('.filter-buttons');
            const filterType = filterGroup.classList.contains('stage-filters') ? 'stage' :
                              filterGroup.classList.contains('role-filters') ? 'role' : 'importance';
            const filterValue = this.getAttribute('data-filter');
            
            // 更新按钮状态
            filterGroup.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // 执行筛选
            filterOperations(filterType, filterValue);
        });
    });
}

/**
 * 筛选操作
 */
function filterOperations(filterType, filterValue) {
    const stageIcons = {
        'create': 'create',
        'process': 'process', 
        'audit': 'audit',
        'revisit': 'revisit',
        'global': 'global'
    };
    
    const stageSections = document.querySelectorAll('.stage-section');
    
    stageSections.forEach(section => {
        let shouldShow = false;
        
        if (filterType === 'stage') {
            if (filterValue === 'all') {
                shouldShow = true;
            } else {
                const stageIcon = section.querySelector('.stage-icon');
                if (stageIcon && stageIcon.classList.contains(filterValue)) {
                    shouldShow = true;
                }
            }
        } else if (filterType === 'role') {
            if (filterValue === 'all') {
                shouldShow = true;
            } else {
                const roleGroups = section.querySelectorAll('.role-group');
                roleGroups.forEach(group => {
                    const roleTitle = group.querySelector('.role-title').textContent.toLowerCase();
                    if (roleTitle.includes(getRoleKeyword(filterValue))) {
                        shouldShow = true;
                    }
                });
            }
        } else if (filterType === 'importance') {
            if (filterValue === 'all') {
                shouldShow = true;
            } else {
                const operationItems = section.querySelectorAll('.operation-item');
                operationItems.forEach(item => {
                    if (item.classList.contains(filterValue)) {
                        shouldShow = true;
                    }
                });
            }
        }
        
        if (shouldShow) {
            section.style.display = 'block';
            section.style.animation = 'fadeInUp 0.6s ease-out';
        } else {
            section.style.display = 'none';
        }
    });
    
    // 更新统计
    updateFilterStats();
}

/**
 * 获取角色关键词
 */
function getRoleKeyword(roleFilter) {
    const keywords = {
        'citizen': '市民',
        'operator': '话务员',
        'processor': '处理人',
        'manager': '管理者',
        'revisitor': '回访'
    };
    return keywords[roleFilter] || '';
}

/**
 * 更新筛选统计
 */
function updateFilterStats() {
    const visibleSections = document.querySelectorAll('.stage-section[style*="block"], .stage-section:not([style*="none"])');
    const totalOperations = Array.from(visibleSections).reduce((total, section) => {
        return total + section.querySelectorAll('.operation-item').length;
    }, 0);
    
    // 显示筛选结果
    showFilterResults(visibleSections.length, totalOperations);
}

/**
 * 显示筛选结果
 */
function showFilterResults(stageCount, operationCount) {
    let resultElement = document.querySelector('.filter-results');
    
    if (!resultElement) {
        resultElement = document.createElement('div');
        resultElement.className = 'filter-results';
        
        const filterSection = document.querySelector('.filter-section');
        filterSection.appendChild(resultElement);
    }
    
    resultElement.innerHTML = `
        <div class="result-stats">
            <span class="result-item">
                <i class="fas fa-layer-group"></i>
                ${stageCount} 个阶段
            </span>
            <span class="result-item">
                <i class="fas fa-cogs"></i>
                ${operationCount} 项操作
            </span>
        </div>
    `;
    
    // 添加结果样式
    if (!document.querySelector('#filter-results-styles')) {
        const style = document.createElement('style');
        style.id = 'filter-results-styles';
        style.textContent = `
            .filter-results {
                margin-top: 1rem;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: 10px;
                text-align: center;
            }
            
            .result-stats {
                display: flex;
                justify-content: center;
                gap: 2rem;
                flex-wrap: wrap;
            }
            
            .result-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: #667eea;
                font-weight: 500;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 初始化操作搜索
 */
function initOperationSearch() {
    createSearchInterface();
    bindSearchEvents();
}

/**
 * 创建搜索界面
 */
function createSearchInterface() {
    const operationsStages = document.querySelector('.operations-stages');
    
    if (!operationsStages) return;
    
    const searchContainer = document.createElement('div');
    searchContainer.className = 'operation-search';
    searchContainer.innerHTML = `
        <div class="search-container">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="搜索操作功能..." id="operationSearchInput">
                <button type="button" id="clearOperationSearch" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-suggestions" id="searchSuggestions" style="display: none;"></div>
        </div>
    `;
    
    operationsStages.insertBefore(searchContainer, operationsStages.firstChild);
    addSearchStyles();
}

/**
 * 添加搜索样式
 */
function addSearchStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .operation-search {
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .search-container {
            position: relative;
            display: inline-block;
            max-width: 500px;
            width: 100%;
        }
        
        .search-box {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .search-box i {
            position: absolute;
            left: 1rem;
            color: #666;
            z-index: 1;
        }
        
        .search-box input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s ease;
        }
        
        .search-box input:focus {
            border-color: #667eea;
        }
        
        .search-box button {
            position: absolute;
            right: 0.5rem;
            width: 30px;
            height: 30px;
            border: none;
            background: #f8f9fa;
            border-radius: 50%;
            cursor: pointer;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .suggestion-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s ease;
        }
        
        .suggestion-item:hover {
            background-color: #f8f9fa;
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 绑定搜索事件
 */
function bindSearchEvents() {
    const searchInput = document.getElementById('operationSearchInput');
    const clearButton = document.getElementById('clearOperationSearch');
    const suggestions = document.getElementById('searchSuggestions');
    
    searchInput.addEventListener('input', debounce(handleOperationSearch, 300));
    searchInput.addEventListener('focus', showSearchSuggestions);
    clearButton.addEventListener('click', clearOperationSearch);
    
    // 点击外部关闭建议
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-container')) {
            suggestions.style.display = 'none';
        }
    });
}

/**
 * 处理操作搜索
 */
function handleOperationSearch(event) {
    const searchTerm = event.target.value.toLowerCase().trim();
    const clearButton = document.getElementById('clearOperationSearch');
    const suggestions = document.getElementById('searchSuggestions');
    
    if (searchTerm) {
        clearButton.style.display = 'flex';
        filterOperationsBySearch(searchTerm);
        updateSearchSuggestions(searchTerm);
    } else {
        clearButton.style.display = 'none';
        showAllOperations();
        suggestions.style.display = 'none';
    }
}

/**
 * 根据搜索词筛选操作
 */
function filterOperationsBySearch(searchTerm) {
    const operationItems = document.querySelectorAll('.operation-item');
    let matchCount = 0;
    
    operationItems.forEach(item => {
        const operationName = item.querySelector('h5').textContent.toLowerCase();
        const operationDesc = item.querySelector('p').textContent.toLowerCase();
        
        const isMatch = operationName.includes(searchTerm) || operationDesc.includes(searchTerm);
        
        if (isMatch) {
            item.style.display = 'flex';
            highlightSearchTerm(item, searchTerm);
            matchCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    // 隐藏没有匹配项的阶段
    const stageSections = document.querySelectorAll('.stage-section');
    stageSections.forEach(section => {
        const visibleItems = section.querySelectorAll('.operation-item[style*="flex"]');
        if (visibleItems.length === 0) {
            section.style.display = 'none';
        } else {
            section.style.display = 'block';
        }
    });
    
    showSearchResults(matchCount);
}

/**
 * 高亮搜索词
 */
function highlightSearchTerm(item, searchTerm) {
    const textElements = item.querySelectorAll('h5, p');
    
    textElements.forEach(element => {
        const originalText = element.textContent;
        const highlightedText = originalText.replace(
            new RegExp(searchTerm, 'gi'),
            `<mark>$&</mark>`
        );
        
        if (highlightedText !== originalText) {
            element.innerHTML = highlightedText;
        }
    });
}

/**
 * 更新搜索建议
 */
function updateSearchSuggestions(searchTerm) {
    const suggestions = document.getElementById('searchSuggestions');
    const operationItems = document.querySelectorAll('.operation-item');
    const matches = [];
    
    operationItems.forEach(item => {
        const operationName = item.querySelector('h5').textContent;
        if (operationName.toLowerCase().includes(searchTerm) && matches.length < 5) {
            matches.push(operationName);
        }
    });
    
    if (matches.length > 0) {
        suggestions.innerHTML = matches.map(match => 
            `<div class="suggestion-item" onclick="selectSuggestion('${match}')">${match}</div>`
        ).join('');
        suggestions.style.display = 'block';
    } else {
        suggestions.style.display = 'none';
    }
}

/**
 * 选择建议
 */
function selectSuggestion(suggestion) {
    const searchInput = document.getElementById('operationSearchInput');
    searchInput.value = suggestion;
    handleOperationSearch({ target: searchInput });
    document.getElementById('searchSuggestions').style.display = 'none';
}

/**
 * 显示搜索结果
 */
function showSearchResults(matchCount) {
    let resultElement = document.querySelector('.search-results');
    
    if (!resultElement) {
        resultElement = document.createElement('div');
        resultElement.className = 'search-results';
        
        const searchContainer = document.querySelector('.search-container');
        searchContainer.appendChild(resultElement);
    }
    
    resultElement.innerHTML = `
        <div class="search-stats">
            找到 <strong>${matchCount}</strong> 项匹配的操作
        </div>
    `;
    
    // 添加搜索结果样式
    if (!document.querySelector('#search-results-styles')) {
        const style = document.createElement('style');
        style.id = 'search-results-styles';
        style.textContent = `
            .search-results {
                margin-top: 0.5rem;
                text-align: center;
            }
            
            .search-stats {
                color: #667eea;
                font-size: 0.9rem;
            }
            
            .search-stats strong {
                color: #333;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 显示搜索建议
 */
function showSearchSuggestions() {
    const searchInput = document.getElementById('operationSearchInput');
    if (searchInput.value.trim()) {
        updateSearchSuggestions(searchInput.value.toLowerCase().trim());
    }
}

/**
 * 清除操作搜索
 */
function clearOperationSearch() {
    const searchInput = document.getElementById('operationSearchInput');
    const clearButton = document.getElementById('clearOperationSearch');
    const suggestions = document.getElementById('searchSuggestions');
    
    searchInput.value = '';
    clearButton.style.display = 'none';
    suggestions.style.display = 'none';
    
    showAllOperations();
    clearOperationHighlights();
    
    const searchResults = document.querySelector('.search-results');
    if (searchResults) {
        searchResults.remove();
    }
}

/**
 * 显示所有操作
 */
function showAllOperations() {
    const operationItems = document.querySelectorAll('.operation-item');
    const stageSections = document.querySelectorAll('.stage-section');
    
    operationItems.forEach(item => {
        item.style.display = 'flex';
    });
    
    stageSections.forEach(section => {
        section.style.display = 'block';
    });
}

/**
 * 清除操作高亮
 */
function clearOperationHighlights() {
    const highlights = document.querySelectorAll('mark');
    highlights.forEach(mark => {
        const parent = mark.parentNode;
        parent.replaceChild(document.createTextNode(mark.textContent), mark);
        parent.normalize();
    });
}

/**
 * 初始化操作详情
 */
function initOperationDetails() {
    const operationItems = document.querySelectorAll('.operation-item');
    
    operationItems.forEach(item => {
        item.addEventListener('click', function() {
            showOperationDetails(this);
        });
    });
}

/**
 * 显示操作详情
 */
function showOperationDetails(operationItem) {
    const operationName = operationItem.querySelector('h5').textContent;
    const operationDesc = operationItem.querySelector('p').textContent;
    const isHighlight = operationItem.classList.contains('highlight');
    const isPrimary = operationItem.classList.contains('primary');
    
    // 创建详情模态框
    const modal = document.createElement('div');
    modal.className = 'operation-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${operationName}</h3>
                <div class="operation-badges">
                    ${isHighlight ? '<span class="badge highlight">重点操作</span>' : ''}
                    ${isPrimary ? '<span class="badge primary">核心操作</span>' : ''}
                </div>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="operation-description">
                    <h4>操作描述</h4>
                    <p>${operationDesc}</p>
                </div>
                <div class="operation-context">
                    <h4>使用场景</h4>
                    <p>${generateOperationContext(operationName)}</p>
                </div>
                <div class="operation-steps">
                    <h4>操作步骤</h4>
                    <ol>
                        ${generateOperationSteps(operationName)}
                    </ol>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 绑定关闭事件
    const closeBtn = modal.querySelector('.close-btn');
    closeBtn.addEventListener('click', () => modal.remove());
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });
    
    addOperationModalStyles();
}

/**
 * 生成操作上下文
 */
function generateOperationContext(operationName) {
    const contexts = {
        '新建工单': '当市民通过12345热线提出诉求时，话务员需要在系统中创建新的工单记录',
        '即时办结': '对于政策咨询、信息查询等无需流转的简单诉求，可直接在当前环节办结',
        '指派工单': '当工单需要下派给其他部门或人员处理时使用',
        '工单补记': '在处理工单过程中记录处理步骤、现场情况等重要信息',
        '审核通过': '管理者对下级提交的办结结果进行审核确认',
        '执行回访': '在工单处理完成后，回访员主动联系市民了解满意度'
    };
    
    return contexts[operationName] || '该操作用于工单处理流程中的特定环节，确保流程顺畅进行。';
}

/**
 * 生成操作步骤
 */
function generateOperationSteps(operationName) {
    const steps = {
        '新建工单': [
            '接听市民来电',
            '了解诉求详情',
            '选择工单类型',
            '填写基本信息',
            '录入诉求内容',
            '保存工单'
        ],
        '即时办结': [
            '判断是否可即时处理',
            '查询相关政策或信息',
            '向市民说明情况',
            '确认市民理解',
            '填写办结说明',
            '提交即时办结'
        ],
        '指派工单': [
            '分析工单内容',
            '确定承办单位',
            '选择具体处理人',
            '添加派单说明',
            '设置处理时限',
            '发送工单'
        ]
    };
    
    const defaultSteps = [
        '登录系统',
        '找到相关工单',
        '执行具体操作',
        '填写必要信息',
        '确认并提交'
    ];
    
    const operationSteps = steps[operationName] || defaultSteps;
    return operationSteps.map(step => `<li>${step}</li>`).join('');
}

/**
 * 添加操作模态框样式
 */
function addOperationModalStyles() {
    if (document.querySelector('#operation-modal-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'operation-modal-styles';
    style.textContent = `
        .operation-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }
        
        .operation-modal .modal-content {
            background: white;
            border-radius: 15px;
            max-width: 700px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideInUp 0.3s ease;
        }
        
        .operation-modal .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .operation-badges {
            display: flex;
            gap: 0.5rem;
        }
        
        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge.highlight {
            background: #ffc107;
            color: #856404;
        }
        
        .badge.primary {
            background: #667eea;
            color: white;
        }
        
        .operation-modal .modal-body {
            padding: 1.5rem;
        }
        
        .operation-modal .modal-body h4 {
            margin: 1.5rem 0 1rem;
            color: #333;
            font-size: 1.1rem;
        }
        
        .operation-modal .modal-body h4:first-child {
            margin-top: 0;
        }
        
        .operation-modal .modal-body p {
            color: #555;
            line-height: 1.6;
        }
        
        .operation-modal .modal-body ol {
            color: #555;
            line-height: 1.6;
            padding-left: 1.5rem;
        }
        
        .operation-modal .modal-body li {
            margin-bottom: 0.5rem;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 初始化统计动画
 */
function initStatsAnimation() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const finalValue = target.textContent;
                
                if (!isNaN(finalValue)) {
                    animateNumber(target, 0, parseInt(finalValue), 1500);
                }
                
                observer.unobserve(target);
            }
        });
    });
    
    statNumbers.forEach(stat => {
        observer.observe(stat);
    });
}

/**
 * 数字动画函数
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(start + (end - start) * easeOutCubic);
        
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(update);
        } else {
            element.textContent = end;
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
