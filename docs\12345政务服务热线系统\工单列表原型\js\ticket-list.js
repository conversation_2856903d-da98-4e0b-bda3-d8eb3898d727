/**
 * 工单列表主控制器
 */

class TicketListController {
    constructor() {
        this.currentUser = null;
        this.filters = {};
        this.pagination = {
            page: 1,
            pageSize: CONFIG.pagination.defaultPageSize,
            total: 0
        };
        this.sortField = CONFIG.table.defaultSortField;
        this.sortOrder = CONFIG.table.defaultSortOrder;
        
        this.table = null;
        this.autoRefreshTimer = null;
        
        this.init();
    }
    
    async init() {
        try {
            // 加载当前用户信息
            await this.loadCurrentUser();
            
            // 初始化组件
            this.initComponents();
            
            // 绑定事件
            this.bindEvents();
            
            // 加载数据
            await this.loadData();
            
            // 启动自动刷新
            this.startAutoRefresh();
            
            console.log('工单列表初始化完成');
        } catch (error) {
            console.error('工单列表初始化失败:', error);
            Message.error('系统初始化失败，请刷新页面重试');
        }
    }
    
    /**
     * 加载当前用户信息
     */
    async loadCurrentUser() {
        try {
            const response = await API.getCurrentUser();
            if (response.success) {
                this.currentUser = response.data;
                this.updateUserInfo();
                this.updateRoleSpecificUI();
            }
        } catch (error) {
            console.error('加载用户信息失败:', error);
        }
    }
    
    /**
     * 更新用户信息显示
     */
    updateUserInfo() {
        const userRoleEl = DOM.$('#userRole');
        if (userRoleEl && this.currentUser) {
            const roleTexts = {
                municipal_operator: '市级话务员',
                district_operator: '区级话务员',
                street_operator: '街镇话务员',
                department_staff: '部门工作人员',
                executor: '执行人员',
                collaborator: '协办人',
                callback_staff: '回访员',
                manager: '管理者',
                leader: '领导',
                citizen: '市民'
            };
            userRoleEl.textContent = roleTexts[this.currentUser.role] || this.currentUser.role;
        }
    }
    
    /**
     * 更新角色特定UI
     */
    updateRoleSpecificUI() {
        if (!this.currentUser) return;

        // 角色专用筛选已隐藏，相关功能移至高级搜索
        /*
        const roleSpecificFilters = DOM.$('#roleSpecificFilters');
        if (roleSpecificFilters) {
            // 根据角色显示特定筛选器
            const roleClass = `role-${this.currentUser.role.replace('_', '-')}`;
            roleSpecificFilters.className = `role-specific-filters ${roleClass}`;

            // 显示角色特定的筛选选项
            if (this.currentUser.role === 'municipal_operator') {
                DOM.show(roleSpecificFilters);
            } else {
                DOM.hide(roleSpecificFilters);
            }
        }
        */

        // 根据权限显示/隐藏按钮
        this.updatePermissionBasedUI();
    }
    
    /**
     * 更新基于权限的UI
     */
    updatePermissionBasedUI() {
        const permissions = this.currentUser.permissions || [];
        
        // 合并按钮
        const mergeBtn = DOM.$('#batchMergeBtn');
        if (mergeBtn) {
            if (permissions.includes('merge')) {
                DOM.show(mergeBtn, 'inline-flex');
            } else {
                DOM.hide(mergeBtn);
            }
        }
        
        // 新建工单按钮
        const newBtn = DOM.$('#newTicketBtn');
        if (newBtn) {
            if (permissions.includes('create')) {
                DOM.show(newBtn, 'inline-flex');
            } else {
                DOM.hide(newBtn);
            }
        }
    }
    
    /**
     * 初始化组件
     */
    initComponents() {
        // 初始化表格
        const tableContainer = DOM.$('.table-section');
        if (tableContainer) {
            this.table = new TicketTable(tableContainer, {
                columns: this.getUserColumns()
            });
        }
        
        // 初始化筛选器
        this.initFilters();
        
        // 初始化分页
        this.initPagination();
    }
    
    /**
     * 获取用户角色对应的列配置
     */
    getUserColumns() {
        if (this.currentUser && CONFIG.columns[this.currentUser.role]) {
            return CONFIG.columns[this.currentUser.role];
        }
        return CONFIG.columns.default;
    }
    
    /**
     * 初始化筛选器
     */
    initFilters() {
        // 加载保存的筛选条件
        const savedFilters = UTILS.storage.get(CONFIG.storageKeys.userSettings);
        if (savedFilters && savedFilters.filters) {
            this.filters = savedFilters.filters;
            this.applyFiltersToUI();
        }
    }
    
    /**
     * 应用筛选条件到UI
     */
    applyFiltersToUI() {
        // 状态筛选
        if (this.filters.status) {
            const statusBtn = DOM.$(`#statusFilters [data-value="${this.filters.status}"]`);
            if (statusBtn) {
                DOM.$$('#statusFilters .filter-btn').forEach(btn => DOM.removeClass(btn, 'active'));
                DOM.addClass(statusBtn, 'active');
            }
        }
        
        // 紧急程度筛选
        if (this.filters.urgency) {
            const urgencyBtn = DOM.$(`#urgencyFilters [data-value="${this.filters.urgency}"]`);
            if (urgencyBtn) {
                DOM.$$('#urgencyFilters .filter-btn').forEach(btn => DOM.removeClass(btn, 'active'));
                DOM.addClass(urgencyBtn, 'active');
            }
        }
        
        // 处理模式筛选
        if (this.filters.mode) {
            const modeBtn = DOM.$(`#modeFilters [data-value="${this.filters.mode}"]`);
            if (modeBtn) {
                DOM.$$('#modeFilters .filter-btn').forEach(btn => DOM.removeClass(btn, 'active'));
                DOM.addClass(modeBtn, 'active');
            }
        }
        
        // 搜索框
        if (this.filters.search) {
            const searchInput = DOM.$('#searchInput');
            if (searchInput) {
                searchInput.value = this.filters.search;
            }
        }
    }
    
    /**
     * 初始化分页
     */
    initPagination() {
        const pageSizeSelect = DOM.$('#pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.value = this.pagination.pageSize;
        }
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 筛选按钮事件
        this.bindFilterEvents();
        
        // 搜索事件
        this.bindSearchEvents();
        
        // 工具栏事件
        this.bindToolbarEvents();
        
        // 表格事件
        this.bindTableEvents();
        
        // 分页事件
        this.bindPaginationEvents();
        
        // 键盘快捷键
        this.bindKeyboardEvents();
    }
    
    /**
     * 绑定筛选事件
     */
    bindFilterEvents() {
        // 状态筛选
        EventHandler.delegate(document, '#statusFilters .filter-btn', 'click', (e) => {
            this.handleFilterClick(e.currentTarget, 'status');
        });
        
        // 紧急程度筛选
        EventHandler.delegate(document, '#urgencyFilters .filter-btn', 'click', (e) => {
            this.handleFilterClick(e.currentTarget, 'urgency');
        });
        
        // 处理模式筛选
        EventHandler.delegate(document, '#modeFilters .filter-btn', 'click', (e) => {
            this.handleFilterClick(e.currentTarget, 'mode');
        });
        
        // 角色特定筛选 - 已移除，功能整合到高级搜索
        /*
        const strategyFilter = DOM.$('#strategyFilter');
        if (strategyFilter) {
            EventHandler.on(strategyFilter, 'change', (e) => {
                this.filters.strategy = e.target.value;
                this.loadData();
            });
        }

        const mergeFilter = DOM.$('#mergeFilter');
        if (mergeFilter) {
            EventHandler.on(mergeFilter, 'change', (e) => {
                this.filters.merge = e.target.value;
                this.loadData();
            });
        }
        */
    }
    
    /**
     * 处理筛选按钮点击
     */
    handleFilterClick(button, filterType) {
        const value = button.dataset.value;
        const container = button.parentElement;
        
        // 更新UI状态
        DOM.$$('.filter-btn', container).forEach(btn => DOM.removeClass(btn, 'active'));
        DOM.addClass(button, 'active');
        
        // 更新筛选条件
        if (value === '') {
            delete this.filters[filterType];
        } else {
            this.filters[filterType] = value;
        }
        
        // 重新加载数据
        this.pagination.page = 1;
        this.loadData();
        this.saveUserSettings();
    }
    
    /**
     * 绑定搜索事件
     */
    bindSearchEvents() {
        const searchInput = DOM.$('#searchInput');
        const searchBtn = DOM.$('#searchBtn');
        
        if (searchInput) {
            // 防抖搜索
            const debouncedSearch = UTILS.debounce(() => {
                this.filters.search = searchInput.value.trim();
                this.pagination.page = 1;
                this.loadData();
                this.saveUserSettings();
            }, CONFIG.filter.debounceDelay);
            
            EventHandler.on(searchInput, 'input', debouncedSearch);
            EventHandler.on(searchInput, 'keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    debouncedSearch();
                }
            });
        }
        
        if (searchBtn) {
            EventHandler.on(searchBtn, 'click', () => {
                this.filters.search = searchInput.value.trim();
                this.pagination.page = 1;
                this.loadData();
                this.saveUserSettings();
            });
        }
        
        // 高级搜索
        const advancedSearchBtn = DOM.$('#advancedSearchBtn');
        if (advancedSearchBtn) {
            EventHandler.on(advancedSearchBtn, 'click', () => {
                this.showAdvancedSearch();
            });
        }
    }
    
    /**
     * 绑定工具栏事件
     */
    bindToolbarEvents() {
        // 新建工单
        const newTicketBtn = DOM.$('#newTicketBtn');
        if (newTicketBtn) {
            EventHandler.on(newTicketBtn, 'click', () => {
                this.createNewTicket();
            });
        }
        
        // 刷新
        const refreshBtn = DOM.$('#refreshBtn');
        if (refreshBtn) {
            EventHandler.on(refreshBtn, 'click', () => {
                this.refresh();
            });
        }
        
        // 批量合并
        const batchMergeBtn = DOM.$('#batchMergeBtn');
        if (batchMergeBtn) {
            EventHandler.on(batchMergeBtn, 'click', () => {
                this.batchMergeTickets();
            });
        }
        
        // 批量派单
        const batchDispatchBtn = DOM.$('#batchDispatchBtn');
        if (batchDispatchBtn) {
            EventHandler.on(batchDispatchBtn, 'click', () => {
                this.batchDispatchTickets();
            });
        }
        
        // 列设置
        const columnSettingsBtn = DOM.$('#columnSettingsBtn');
        if (columnSettingsBtn) {
            EventHandler.on(columnSettingsBtn, 'click', () => {
                this.showColumnSettings();
            });
        }
        
        // 导出
        const exportBtn = DOM.$('#exportBtn');
        if (exportBtn) {
            EventHandler.on(exportBtn, 'click', () => {
                this.exportData();
            });
        }
    }
    
    /**
     * 绑定表格事件
     */
    bindTableEvents() {
        if (!this.table) return;
        
        // 选择变化
        EventHandler.on(this.table.container, 'selection-change', (e) => {
            this.handleSelectionChange(e.detail);
        });
        
        // 排序变化
        EventHandler.on(this.table.container, 'sort-change', (e) => {
            this.sortField = e.detail.field;
            this.sortOrder = e.detail.order;
            this.loadData();
            this.saveUserSettings();
        });
        
        // 操作事件
        EventHandler.on(this.table.container, 'action', (e) => {
            this.handleTableAction(e.detail);
        });
    }
    
    /**
     * 绑定分页事件
     */
    bindPaginationEvents() {
        // 页面大小变化
        const pageSizeSelect = DOM.$('#pageSizeSelect');
        if (pageSizeSelect) {
            EventHandler.on(pageSizeSelect, 'change', (e) => {
                this.pagination.pageSize = parseInt(e.target.value);
                this.pagination.page = 1;
                this.loadData();
                this.saveUserSettings();
            });
        }
    }
    
    /**
     * 绑定键盘事件
     */
    bindKeyboardEvents() {
        EventHandler.on(document, 'keydown', (e) => {
            // Ctrl+R 刷新
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.refresh();
            }
            
            // F5 刷新
            if (e.key === 'F5') {
                e.preventDefault();
                this.refresh();
            }
            
            // Ctrl+A 全选
            if (e.ctrlKey && e.key === 'a' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                if (this.table) {
                    const selectAllCheckbox = DOM.$('#selectAll');
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = true;
                        this.table.toggleSelectAll(true);
                    }
                }
            }
        });
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 显示加载状态
            this.showLoading();
            
            // 构建查询参数
            const params = {
                ...this.filters,
                page: this.pagination.page,
                pageSize: this.pagination.pageSize,
                sortField: this.sortField,
                sortOrder: this.sortOrder
            };
            
            // 请求数据
            const response = await API.getTicketList(params);
            
            if (response.success) {
                // 更新分页信息
                this.pagination = response.pagination;
                
                // 渲染表格
                if (this.table) {
                    this.table.render(response.data);
                }
                
                // 更新分页UI
                this.updatePaginationUI();
                
                // 加载统计数据
                await this.loadStats();
            } else {
                throw new Error(response.message || '加载数据失败');
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            Message.error('加载数据失败，请稍后重试');
            
            // 显示空状态
            if (this.table) {
                this.table.render([]);
            }
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 加载统计数据
     */
    async loadStats() {
        try {
            const response = await API.getStats();
            if (response.success) {
                this.updateStatsUI(response.data);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }
    
    /**
     * 更新统计UI
     */
    updateStatsUI(stats) {
        const elements = {
            urgentCount: DOM.$('#urgentCount'),
            timeoutCount: DOM.$('#timeoutCount'),
            pendingCount: DOM.$('#pendingCount'),
            mergeCount: DOM.$('#mergeCount')
        };
        
        if (elements.urgentCount) elements.urgentCount.textContent = stats.urgent || 0;
        if (elements.timeoutCount) elements.timeoutCount.textContent = stats.timeout || 0;
        if (elements.pendingCount) elements.pendingCount.textContent = stats.pending || 0;
        if (elements.mergeCount) elements.mergeCount.textContent = stats.merge || 0;
    }
    
    /**
     * 更新分页UI
     */
    updatePaginationUI() {
        const totalCountEl = DOM.$('#totalCount');
        if (totalCountEl) {
            totalCountEl.textContent = this.pagination.total;
        }
        
        // 生成分页按钮
        this.renderPaginationControls();
    }
    
    /**
     * 渲染分页控件
     */
    renderPaginationControls() {
        const container = DOM.$('#paginationControls');
        if (!container) return;
        
        const { page, totalPages } = this.pagination;
        const buttons = [];
        
        // 上一页
        buttons.push(`
            <button class="btn btn-outline btn-small" ${page <= 1 ? 'disabled' : ''} data-page="${page - 1}">
                ${CONFIG.icons.navigation?.prev || '◀'}
            </button>
        `);
        
        // 页码按钮
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);
        
        if (startPage > 1) {
            buttons.push(`<button class="btn btn-outline btn-small" data-page="1">1</button>`);
            if (startPage > 2) {
                buttons.push(`<span class="pagination-ellipsis">...</span>`);
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            buttons.push(`
                <button class="btn ${i === page ? 'btn-primary' : 'btn-outline'} btn-small" data-page="${i}">
                    ${i}
                </button>
            `);
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                buttons.push(`<span class="pagination-ellipsis">...</span>`);
            }
            buttons.push(`<button class="btn btn-outline btn-small" data-page="${totalPages}">${totalPages}</button>`);
        }
        
        // 下一页
        buttons.push(`
            <button class="btn btn-outline btn-small" ${page >= totalPages ? 'disabled' : ''} data-page="${page + 1}">
                ${CONFIG.icons.navigation?.next || '▶'}
            </button>
        `);
        
        container.innerHTML = buttons.join('');
        
        // 绑定分页点击事件
        EventHandler.delegate(container, '[data-page]', 'click', (e) => {
            const newPage = parseInt(e.currentTarget.dataset.page);
            if (newPage !== this.pagination.page && newPage >= 1 && newPage <= totalPages) {
                this.pagination.page = newPage;
                this.loadData();
            }
        });
    }
    
    /**
     * 处理选择变化
     */
    handleSelectionChange(detail) {
        const { selectedCount } = detail;
        
        // 更新批量操作UI
        const batchInfo = DOM.$('#batchInfo');
        const selectedCountEl = DOM.$('#selectedCount');
        const batchMergeBtn = DOM.$('#batchMergeBtn');
        const batchDispatchBtn = DOM.$('#batchDispatchBtn');
        
        if (selectedCount > 0) {
            DOM.show(batchInfo);
            if (selectedCountEl) selectedCountEl.textContent = selectedCount;
            
            // 启用批量操作按钮
            if (batchMergeBtn) batchMergeBtn.disabled = false;
            if (batchDispatchBtn) batchDispatchBtn.disabled = false;
        } else {
            DOM.hide(batchInfo);
            
            // 禁用批量操作按钮
            if (batchMergeBtn) batchMergeBtn.disabled = true;
            if (batchDispatchBtn) batchDispatchBtn.disabled = true;
        }
    }
    
    /**
     * 处理表格操作
     */
    handleTableAction(detail) {
        const { action, ticketId } = detail;
        
        switch (action) {
            case 'view':
                this.viewTicketDetail(ticketId);
                break;
            case 'edit':
                this.editTicket(ticketId);
                break;
            case 'dispatch':
                this.dispatchTicket(ticketId);
                break;
            case 'follow':
                this.followTicket(ticketId);
                break;
            case 'close':
                this.closeTicket(ticketId);
                break;
            default:
                console.warn('未知操作:', action);
        }
    }
    
    /**
     * 显示加载状态
     */
    showLoading() {
        const loadingState = DOM.$('#loadingState');
        const emptyState = DOM.$('#emptyState');
        
        if (loadingState) DOM.show(loadingState);
        if (emptyState) DOM.hide(emptyState);
    }
    
    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingState = DOM.$('#loadingState');
        if (loadingState) DOM.hide(loadingState);
    }
    
    /**
     * 刷新数据
     */
    async refresh() {
        await this.loadData();
        Message.success('数据已刷新');
    }
    
    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
        }
        
        this.autoRefreshTimer = setInterval(() => {
            this.loadData();
        }, CONFIG.table.autoRefreshInterval);
    }
    
    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
            this.autoRefreshTimer = null;
        }
    }
    
    /**
     * 保存用户设置
     */
    saveUserSettings() {
        const settings = {
            filters: this.filters,
            pagination: {
                pageSize: this.pagination.pageSize
            },
            sort: {
                field: this.sortField,
                order: this.sortOrder
            }
        };
        
        UTILS.storage.set(CONFIG.storageKeys.userSettings, settings);
    }
    
    /**
     * 销毁组件
     */
    destroy() {
        this.stopAutoRefresh();
        
        // 清理事件监听器
        // 这里应该移除所有事件监听器，但由于使用了事件委托，大部分会自动清理
        
        console.log('工单列表组件已销毁');
    }
    
    // 占位方法，实际实现需要根据具体需求
    viewTicketDetail(ticketId) { console.log('查看工单详情:', ticketId); }
    editTicket(ticketId) { console.log('编辑工单:', ticketId); }
    dispatchTicket(ticketId) { console.log('派发工单:', ticketId); }
    followTicket(ticketId) { console.log('跟进工单:', ticketId); }
    closeTicket(ticketId) { console.log('关闭工单:', ticketId); }
    createNewTicket() { console.log('创建新工单'); }
    batchMergeTickets() { console.log('批量合并工单'); }
    batchDispatchTickets() { console.log('批量派发工单'); }
    showAdvancedSearch() {
        // 创建高级搜索弹窗
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content advanced-search-modal">
                <div class="modal-header">
                    <h3>高级搜索</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>处理策略：</label>
                                <select id="advancedStrategyFilter">
                                    <option value="">全部</option>
                                    <option value="instant">可即时办结</option>
                                    <option value="dispatch">需要派单</option>
                                    <option value="complex">复杂协调</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>合并机会：</label>
                                <select id="advancedMergeFilter">
                                    <option value="">全部</option>
                                    <option value="similar">有相似工单</option>
                                    <option value="mergeable">可合并工单</option>
                                    <option value="independent">独立工单</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>创建时间：</label>
                                <input type="date" id="advancedStartDate">
                                <span>至</span>
                                <input type="date" id="advancedEndDate">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="ticketListController.applyAdvancedSearch()">搜索</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    applyAdvancedSearch() {
        // 应用高级搜索条件
        const strategy = DOM.$('#advancedStrategyFilter')?.value || '';
        const merge = DOM.$('#advancedMergeFilter')?.value || '';
        const startDate = DOM.$('#advancedStartDate')?.value || '';
        const endDate = DOM.$('#advancedEndDate')?.value || '';

        // 更新筛选条件
        this.filters.strategy = strategy;
        this.filters.merge = merge;
        this.filters.startDate = startDate;
        this.filters.endDate = endDate;

        // 重新加载数据
        this.loadData();

        // 关闭弹窗
        DOM.$('.modal-overlay')?.remove();

        console.log('应用高级搜索:', { strategy, merge, startDate, endDate });
    }
    showColumnSettings() { console.log('显示列设置'); }
    exportData() { console.log('导出数据'); }
}

// 导出控制器（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TicketListController;
}
