/* 响应式设计 */

/* 移动端样式 */
@media (max-width: 768px) {
    .container {
        max-width: 98vw;
        margin: 10px auto;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .content {
        padding: 10px 15px;
    }
    
    .stage-btn {
        font-size: 12px;
        padding: 8px 15px;
    }
    
    .mermaid-container {
        padding: 15px;
        margin: 15px 0;
    }

    .diagram-controls {
        top: 5px;
        right: 5px;
        gap: 5px;
    }

    .control-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .help-content {
        width: 95%;
        max-height: 95vh;
        max-width: 400px;
    }

    .help-header {
        padding: 12px 15px 8px;
    }

    .help-header h3 {
        font-size: 1.1em;
    }

    .help-body {
        padding: 12px 15px;
    }

    .help-body h4 {
        font-size: 0.95em;
        margin-bottom: 8px;
    }

    .export-option {
        margin-bottom: 15px;
    }

    .export-radio {
        padding: 8px;
        gap: 6px;
    }

    .export-radio span {
        font-size: 0.9em;
    }

    .export-radio small {
        font-size: 0.8em;
    }

    .export-actions {
        flex-direction: column;
        gap: 8px;
        margin-top: 15px;
        padding-top: 12px;
    }

    .export-btn {
        width: 100%;
        justify-content: center;
        padding: 12px 16px;
        font-size: 14px;
    }
    
    .legend {
        gap: 10px;
        padding: 15px;
    }
    
    .legend-item {
        font-size: 14px;
    }
    
    .stage-details {
        padding: 15px;
    }

    .collaborative-selector {
        margin: 20px 0;
        padding: 15px;
    }

    .collaborative-buttons {
        gap: 10px;
    }

    .collab-btn {
        font-size: 12px;
        padding: 10px 18px;
    }
}

/* 超小屏幕 (480px以下) */
@media (max-width: 480px) {
    .help-modal {
        padding: 5px;
    }

    .help-content {
        width: 100%;
        max-width: none;
        max-height: 98vh;
        margin: 0;
        border-radius: 8px;
    }

    .help-header {
        padding: 10px 12px 6px;
    }

    .help-header h3 {
        font-size: 1em;
    }

    .help-body {
        padding: 10px 12px;
    }

    .export-option h4 {
        font-size: 0.9em;
        margin-bottom: 6px;
    }

    .export-radio {
        padding: 6px;
        border-radius: 4px;
    }

    .export-radio span {
        font-size: 0.85em;
    }

    .export-radio small {
        font-size: 0.75em;
    }

    .export-actions {
        margin-top: 12px;
        padding-top: 10px;
    }

    .export-btn {
        padding: 10px 14px;
        font-size: 13px;
    }
}

/* 平板端样式 */
@media (min-width: 769px) and (max-width: 1199px) {
    .container {
        max-width: 96vw;
    }
    
    .mermaid-container {
        padding: 25px;
        min-height: 550px;
    }
}

/* 大屏幕样式 */
@media (min-width: 1200px) {
    .container {
        max-width: 98vw;
    }
    
    .mermaid-container {
        padding: 40px;
        min-height: 700px;
    }

    .control-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
    
    .stage-selector {
        gap: 15px;
    }
    
    .stage-btn {
        padding: 15px 25px;
        font-size: 15px;
    }
}

/* 超大屏幕样式 */
@media (min-width: 1600px) {
    .container {
        max-width: 95vw;
    }
    
    .mermaid-container {
        padding: 50px;
        min-height: 800px;
    }
    
    .content {
        padding: 30px 50px;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
        padding: 0;
    }
    
    .container {
        box-shadow: none;
        border-radius: 0;
        max-width: 100%;
    }
    
    .header {
        background: #2c3e50 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .stage-selector {
        display: none;
    }
    
    .footer {
        background: #34495e !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .mermaid-container {
        background: white !important;
        border: 1px solid #ddd;
    }
}
