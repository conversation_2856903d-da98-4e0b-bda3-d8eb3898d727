/**
 * 动画效果样式
 * 包含各种动画效果和过渡效果
 */

/* 基础动画效果 */
@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 缩放动画 */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* 旋转动画 */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

/* 弹跳动画 */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 摇摆动画 */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 呼吸灯效果 */
@keyframes breathe {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.4;
    }
}

/* 闪烁效果 */
@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0;
    }
}

/* 渐变背景动画 */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 打字机效果 */
@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

/* 光晕效果 */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
    }
}

/* 浮动效果 */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* 翻转效果 */
@keyframes flip {
    0% {
        transform: perspective(400px) rotateY(0);
    }
    40% {
        transform: perspective(400px) rotateY(-90deg);
    }
    60% {
        transform: perspective(400px) rotateY(-90deg);
    }
    100% {
        transform: perspective(400px) rotateY(0);
    }
}

/* 工具类 */
.animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
}

.animate-slideInLeft {
    animation: slideInLeft 0.5s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.5s ease-out;
}

.animate-slideInUp {
    animation: slideInUp 0.5s ease-out;
}

.animate-slideInDown {
    animation: slideInDown 0.5s ease-out;
}

.animate-scaleIn {
    animation: scaleIn 0.3s ease-out;
}

.animate-bounceIn {
    animation: bounceIn 0.6s ease-out;
}

.animate-rotateIn {
    animation: rotateIn 0.5s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 2s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

.animate-breathe {
    animation: breathe 3s ease-in-out infinite;
}

.animate-blink {
    animation: blink 1s linear infinite;
}

/* 延迟类 */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

/* 持续时间类 */
.duration-fast { animation-duration: 0.3s; }
.duration-normal { animation-duration: 0.5s; }
.duration-slow { animation-duration: 1s; }
.duration-slower { animation-duration: 2s; }
