/**
 * 右栏智能辅助与知识库样式
 * 包含AI辅助、知识库、话术推荐、相似工单等
 */

.ai-assistance {
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.ai-assistance::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.ai-assistance h3 {
    margin-bottom: 15px;
    font-size: 16px;
}

.keywords, .intent {
    margin-bottom: 10px;
}

.keywords label, .intent label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.keyword {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 2px 8px;
    border-radius: 12px;
    margin: 2px;
    font-size: 12px;
}

.intent-result {
    background: rgba(255,255,255,0.2);
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
}

.knowledge-base, .standard-scripts, .similar-tickets {
    margin-bottom: 20px;
}

.knowledge-base h4, .standard-scripts h4, .similar-tickets h4 {
    margin-bottom: 10px;
    color: #495057;
    font-size: 14px;
}

.kb-item {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.kb-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateX(4px);
}

.kb-item.best-match {
    border-color: #28a745;
    background-color: #d4edda;
    position: relative;
}

.kb-item.best-match::before {
    content: '🎯';
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 16px;
    animation: bounce 2s infinite;
}

.match-label {
    color: #28a745;
    font-weight: bold;
    font-size: 12px;
}

.kb-type {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-left: 5px;
}

.kb-title {
    margin-top: 5px;
    font-weight: bold;
}

.script-item {
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.script-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.script-item label {
    font-weight: bold;
    color: #495057;
    display: block;
    margin-bottom: 5px;
}

.script-item p {
    margin: 0;
    font-style: italic;
    color: #6c757d;
    position: relative;
    cursor: pointer;
}

.script-item p::after {
    content: '点击复制';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: #6c757d;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.script-item:hover p::after {
    opacity: 1;
}

.similar-info {
    padding: 10px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    font-size: 13px;
}

/* 占位内容样式 */
.ai-placeholder,
.kb-placeholder,
.scripts-placeholder,
.similar-placeholder,
.transcript-placeholder,
.summary-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px dashed rgba(255, 255, 255, 0.2);
    margin: 16px 0;
}

/* 双核信息区域的占位样式 */
.transcript-placeholder,
.summary-placeholder {
    padding: 60px 20px;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.placeholder-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: rgba(102, 102, 102, 0.8);
}

.placeholder-desc {
    font-size: 14px;
    line-height: 1.5;
    color: rgba(136, 136, 136, 0.7);
}

/* 紫色背景区域的占位符文字颜色 */
.ai-placeholder .placeholder-text,
.summary-placeholder .placeholder-text {
    color: rgba(255, 255, 255, 0.8);
}

.ai-placeholder .placeholder-desc,
.summary-placeholder .placeholder-desc {
    color: rgba(255, 255, 255, 0.6);
}

/* 实时语音转写区域的占位符文字颜色（浅色背景） */
.transcript-placeholder .placeholder-text {
    color: rgba(102, 102, 102, 0.9);
}

.transcript-placeholder .placeholder-desc {
    color: rgba(136, 136, 136, 0.8);
}
