/**
 * 主入口文件
 */

// 全局变量
let ticketListController = null;

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('开始初始化12345工单列表系统...');
        
        // 检查浏览器兼容性
        if (!checkBrowserCompatibility()) {
            showBrowserCompatibilityWarning();
            return;
        }
        
        // 添加全局样式
        addGlobalStyles();
        
        // 初始化全局错误处理
        initGlobalErrorHandling();
        
        // 初始化工单列表控制器
        ticketListController = new TicketListController();
        
        // 添加页面可见性变化监听
        initPageVisibilityHandler();
        
        // 添加网络状态监听
        initNetworkStatusHandler();
        
        console.log('12345工单列表系统初始化完成');
        
    } catch (error) {
        console.error('系统初始化失败:', error);
        showInitializationError(error);
    }
});

/**
 * 页面卸载时清理
 */
window.addEventListener('beforeunload', function() {
    if (ticketListController) {
        ticketListController.destroy();
    }
});

/**
 * 检查浏览器兼容性
 */
function checkBrowserCompatibility() {
    // 检查必要的API支持
    const requiredFeatures = [
        'fetch',
        'Promise',
        'localStorage',
        'addEventListener',
        'querySelector',
        'classList'
    ];
    
    for (const feature of requiredFeatures) {
        if (!(feature in window) && !(feature in document) && !(feature in Element.prototype)) {
            console.error(`浏览器不支持 ${feature}`);
            return false;
        }
    }
    
    return true;
}

/**
 * 显示浏览器兼容性警告
 */
function showBrowserCompatibilityWarning() {
    const warningHtml = `
        <div class="browser-warning">
            <div class="browser-warning-content">
                <h3>浏览器兼容性提示</h3>
                <p>您的浏览器版本过低，可能无法正常使用本系统的所有功能。</p>
                <p>建议您升级到以下浏览器的最新版本：</p>
                <ul>
                    <li>Chrome 60+</li>
                    <li>Firefox 55+</li>
                    <li>Safari 11+</li>
                    <li>Edge 79+</li>
                </ul>
                <button onclick="this.parentElement.parentElement.style.display='none'">
                    我知道了
                </button>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('afterbegin', warningHtml);
}

/**
 * 添加全局样式
 */
function addGlobalStyles() {
    const styles = `
        /* 浏览器兼容性警告样式 */
        .browser-warning {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
        
        .browser-warning-content {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            max-width: 500px;
            text-align: center;
        }
        
        .browser-warning-content h3 {
            color: #ff4d4f;
            margin-bottom: 1rem;
        }
        
        .browser-warning-content ul {
            text-align: left;
            margin: 1rem 0;
        }
        
        .browser-warning-content button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 1rem;
        }
        
        /* 初始化错误样式 */
        .init-error {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-align: center;
            z-index: 9999;
        }
        
        .init-error h3 {
            color: #ff4d4f;
            margin-bottom: 1rem;
        }
        
        .init-error button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        
        /* 网络状态提示样式 */
        .network-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            z-index: 9998;
            transition: all 0.3s ease;
        }
        
        .network-status.online {
            background-color: #52c41a;
        }
        
        .network-status.offline {
            background-color: #ff4d4f;
        }
        
        /* 消息提示样式 */
        .message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 16px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 9997;
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 400px;
        }
        
        .message.show {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        
        .message-success {
            background-color: #52c41a;
        }
        
        .message-error {
            background-color: #ff4d4f;
        }
        
        .message-warning {
            background-color: #faad14;
        }
        
        .message-info {
            background-color: #1890ff;
        }
        
        .message-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 16px;
            padding: 0;
            margin-left: auto;
        }
        
        /* 确认对话框样式 */
        .confirm-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.45);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9996;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .confirm-modal.show {
            opacity: 1;
        }
        
        .confirm-content {
            background: white;
            border-radius: 8px;
            padding: 0;
            min-width: 400px;
            max-width: 90vw;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }
        
        .confirm-modal.show .confirm-content {
            transform: scale(1);
        }
        
        .confirm-header {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .confirm-icon {
            font-size: 24px;
        }
        
        .confirm-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .confirm-body {
            padding: 24px;
        }
        
        .confirm-message {
            margin: 0;
            color: #595959;
            line-height: 1.5;
        }
        
        .confirm-footer {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            padding: 10px 16px;
            border-top: 1px solid #f0f0f0;
        }
        
        /* 加载覆盖层样式 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .loading-overlay.show {
            opacity: 1;
        }
        
        .loading-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
        }
        
        /* 移动端卡片视图 */
        .mobile-card-view {
            display: none;
        }
        
        @media (max-width: 767px) {
            .mobile-card-view {
                display: block;
            }
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = styles;
    document.head.appendChild(styleSheet);
}

/**
 * 初始化全局错误处理
 */
function initGlobalErrorHandling() {
    // 捕获未处理的Promise错误
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise错误:', event.reason);
        
        // 如果是网络错误，显示友好提示
        if (event.reason && event.reason.message && event.reason.message.includes('fetch')) {
            Message.error('网络连接失败，请检查网络设置');
        } else {
            Message.error('系统发生错误，请稍后重试');
        }
        
        // 阻止错误在控制台显示
        event.preventDefault();
    });
    
    // 捕获JavaScript运行时错误
    window.addEventListener('error', function(event) {
        console.error('JavaScript错误:', event.error);
        
        // 对于关键错误，显示用户友好的提示
        if (event.error && event.error.stack && event.error.stack.includes('TicketListController')) {
            Message.error('工单列表功能异常，请刷新页面重试');
        }
    });
}

/**
 * 显示初始化错误
 */
function showInitializationError(error) {
    const errorHtml = `
        <div class="init-error">
            <h3>系统初始化失败</h3>
            <p>抱歉，系统无法正常启动。请尝试以下解决方案：</p>
            <div style="margin: 1rem 0;">
                <button onclick="location.reload()">刷新页面</button>
                <button onclick="localStorage.clear(); location.reload()">清除缓存并刷新</button>
            </div>
            <details style="margin-top: 1rem; text-align: left;">
                <summary>错误详情</summary>
                <pre style="margin-top: 0.5rem; font-size: 12px; color: #666;">${error.stack || error.message}</pre>
            </details>
        </div>
    `;
    
    document.body.insertAdjacentHTML('afterbegin', errorHtml);
}

/**
 * 初始化页面可见性处理
 */
function initPageVisibilityHandler() {
    document.addEventListener('visibilitychange', function() {
        if (ticketListController) {
            if (document.hidden) {
                // 页面隐藏时停止自动刷新
                ticketListController.stopAutoRefresh();
            } else {
                // 页面显示时恢复自动刷新并立即刷新一次
                ticketListController.startAutoRefresh();
                ticketListController.refresh();
            }
        }
    });
}

/**
 * 初始化网络状态处理
 */
function initNetworkStatusHandler() {
    let networkStatusEl = null;
    
    function showNetworkStatus(isOnline) {
        if (!networkStatusEl) {
            networkStatusEl = document.createElement('div');
            networkStatusEl.className = 'network-status';
            document.body.appendChild(networkStatusEl);
        }
        
        networkStatusEl.className = `network-status ${isOnline ? 'online' : 'offline'}`;
        networkStatusEl.textContent = isOnline ? '网络已连接' : '网络已断开';
        
        // 3秒后隐藏在线状态提示
        if (isOnline) {
            setTimeout(() => {
                if (networkStatusEl) {
                    networkStatusEl.style.display = 'none';
                }
            }, 3000);
        } else {
            networkStatusEl.style.display = 'block';
        }
    }
    
    // 监听网络状态变化
    window.addEventListener('online', () => {
        showNetworkStatus(true);
        // 网络恢复时刷新数据
        if (ticketListController) {
            ticketListController.refresh();
        }
    });
    
    window.addEventListener('offline', () => {
        showNetworkStatus(false);
    });
}

/**
 * 工具函数：格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 工具函数：下载文件
 */
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 工具函数：复制到剪贴板
 */
async function copyToClipboard(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            document.execCommand('copy');
            textArea.remove();
        }
        
        Message.success('已复制到剪贴板');
        return true;
    } catch (error) {
        console.error('复制失败:', error);
        Message.error('复制失败，请手动复制');
        return false;
    }
}

/**
 * 导出全局函数供HTML使用
 */
window.TicketListApp = {
    formatFileSize,
    downloadFile,
    copyToClipboard,
    getController: () => ticketListController
};

// 开发模式下的调试工具
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.DEBUG = {
        config: CONFIG,
        utils: UTILS,
        api: API,
        mockData: MockData,
        controller: () => ticketListController
    };
    
    console.log('开发模式已启用，调试工具可通过 window.DEBUG 访问');
}
