/**
 * 组件样式文件 - 状态标识、标签、徽章等组件样式
 */

/* 状态标识组件 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

/* 工单状态样式 */
.status-badge.draft {
    background: rgba(140, 140, 140, 0.1);
    color: var(--draft-color);
    border: 1px solid rgba(140, 140, 140, 0.2);
}

.status-badge.pending {
    background: rgba(250, 140, 22, 0.1);
    color: var(--pending-color);
    border: 1px solid rgba(250, 140, 22, 0.2);
}

.status-badge.processing {
    background: rgba(24, 144, 255, 0.1);
    color: var(--processing-color);
    border: 1px solid rgba(24, 144, 255, 0.2);
}

.status-badge.reviewing {
    background: rgba(114, 46, 209, 0.1);
    color: var(--reviewing-color);
    border: 1px solid rgba(114, 46, 209, 0.2);
}

.status-badge.callback {
    background: rgba(82, 196, 26, 0.1);
    color: var(--callback-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-badge.closed {
    background: rgba(140, 140, 140, 0.1);
    color: var(--closed-color);
    border: 1px solid rgba(140, 140, 140, 0.2);
}

/* 紧急程度样式 */
.urgency-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.urgency-badge.normal {
    background: rgba(140, 140, 140, 0.1);
    color: var(--normal-color);
    border: 1px solid rgba(140, 140, 140, 0.2);
}

.urgency-badge.urgent {
    background: rgba(250, 173, 20, 0.1);
    color: var(--urgent-color);
    border: 1px solid rgba(250, 173, 20, 0.2);
}

.urgency-badge.critical {
    background: rgba(255, 77, 79, 0.1);
    color: var(--critical-color);
    border: 1px solid rgba(255, 77, 79, 0.2);
    animation: pulse 2s infinite;
}

/* 特急工单闪烁动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 处理模式样式 */
.mode-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.mode-badge.instant {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #8b6914;
    border: 1px solid #d4af37;
}

.mode-badge.normal {
    background: rgba(24, 144, 255, 0.1);
    color: var(--processing-color);
    border: 1px solid rgba(24, 144, 255, 0.2);
}

.mode-badge.cooperation {
    background: rgba(82, 196, 26, 0.1);
    color: var(--callback-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

/* 督办标识样式 */
.supervise-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.supervise-badge.none {
    display: none;
}

.supervise-badge.general {
    background: rgba(24, 144, 255, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(24, 144, 255, 0.2);
}

.supervise-badge.important {
    background: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 77, 79, 0.2);
}

.supervise-badge.leader {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #8b6914;
    border: 1px solid #d4af37;
}

/* VIP标识 */
.vip-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #8b6914;
    border: 1px solid #d4af37;
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

/* 时限状态样式 */
.time-limit-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.time-limit-badge.normal {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

.time-limit-badge.warning {
    background: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(250, 173, 20, 0.2);
}

.time-limit-badge.danger {
    background: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 77, 79, 0.2);
    animation: blink 1s infinite;
}

.time-limit-badge.success {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

/* 超时闪烁动画 */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* 标签组件 */
.tag {
    display: inline-block;
    padding: 2px 6px;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    margin-right: var(--spacing-xs);
    margin-bottom: 2px;
}

.tag.primary {
    background: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

.tag.success {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
}

.tag.warning {
    background: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
}

.tag.error {
    background: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
}

/* 满意度显示 */
.satisfaction-display {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
}

.satisfaction-stars {
    color: #faad14;
}

.satisfaction-text {
    color: var(--text-secondary);
}

.satisfaction-text.satisfied {
    color: var(--success-color);
}

.satisfaction-text.unsatisfied {
    color: var(--error-color);
}

/* 协办进度显示 */
.cooperation-progress {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.cooperation-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
}

.cooperation-role {
    font-weight: 500;
    min-width: 40px;
}

.cooperation-unit {
    color: var(--text-secondary);
    flex: 1;
}

.cooperation-status {
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 10px;
}

.cooperation-status.pending {
    background: rgba(250, 140, 22, 0.1);
    color: var(--pending-color);
}

.cooperation-status.processing {
    background: rgba(24, 144, 255, 0.1);
    color: var(--processing-color);
}

.cooperation-status.completed {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
}

/* 工单关联显示 */
.relation-display {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 6px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.relation-display.main {
    background: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

.relation-display.merged {
    background: rgba(140, 140, 140, 0.1);
    color: var(--text-tertiary);
}

.relation-display.parent {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
}

.relation-display.child {
    background: rgba(114, 46, 209, 0.1);
    color: var(--reviewing-color);
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.action-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 2px;
    white-space: nowrap;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.action-btn.primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-hover);
}

.action-btn.success {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.action-btn.success:hover {
    opacity: 0.8;
}

.action-btn.warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.action-btn.warning:hover {
    opacity: 0.8;
}

.action-btn.danger {
    background: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

.action-btn.danger:hover {
    opacity: 0.8;
}

/* 更多操作下拉菜单 */
.more-actions {
    position: relative;
    display: inline-block;
}

.more-actions-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-medium);
    z-index: 10;
    min-width: 120px;
    display: none;
}

.more-actions-menu.show {
    display: block;
}

.more-actions-item {
    display: block;
    width: 100%;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    transition: background-color var(--transition-fast);
}

.more-actions-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.more-actions-item:first-child {
    border-top-left-radius: var(--border-radius-sm);
    border-top-right-radius: var(--border-radius-sm);
}

.more-actions-item:last-child {
    border-bottom-left-radius: var(--border-radius-sm);
    border-bottom-right-radius: var(--border-radius-sm);
}

/* 加载状态 */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-fast);
    z-index: 100;
}

.tooltip:hover::after {
    opacity: 1;
}
