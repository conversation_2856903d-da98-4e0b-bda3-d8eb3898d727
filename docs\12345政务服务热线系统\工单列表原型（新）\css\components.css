/**
 * 组件样式文件 - 状态标识、标签、徽章等组件样式
 */

/* 状态标识组件 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

/* 工单状态样式 */
.status-badge.draft {
    background: rgba(140, 140, 140, 0.1);
    color: var(--draft-color);
    border: 1px solid rgba(140, 140, 140, 0.2);
}

.status-badge.pending {
    background: rgba(250, 140, 22, 0.1);
    color: var(--pending-color);
    border: 1px solid rgba(250, 140, 22, 0.2);
}

.status-badge.processing {
    background: rgba(24, 144, 255, 0.1);
    color: var(--processing-color);
    border: 1px solid rgba(24, 144, 255, 0.2);
}

.status-badge.reviewing {
    background: rgba(114, 46, 209, 0.1);
    color: var(--reviewing-color);
    border: 1px solid rgba(114, 46, 209, 0.2);
}

.status-badge.callback {
    background: rgba(82, 196, 26, 0.1);
    color: var(--callback-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-badge.closed {
    background: rgba(140, 140, 140, 0.1);
    color: var(--closed-color);
    border: 1px solid rgba(140, 140, 140, 0.2);
}

/* 紧急程度样式 */
.urgency-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.urgency-badge.normal {
    background: rgba(140, 140, 140, 0.1);
    color: var(--normal-color);
    border: 1px solid rgba(140, 140, 140, 0.2);
}

.urgency-badge.urgent {
    background: rgba(250, 173, 20, 0.1);
    color: var(--urgent-color);
    border: 1px solid rgba(250, 173, 20, 0.2);
}

.urgency-badge.critical {
    background: rgba(255, 77, 79, 0.1);
    color: var(--critical-color);
    border: 1px solid rgba(255, 77, 79, 0.2);
    animation: pulse 2s infinite;
}

/* 特急工单闪烁动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 处理模式样式 */
.mode-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.mode-badge.instant {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #8b6914;
    border: 1px solid #d4af37;
}

.mode-badge.normal {
    background: rgba(24, 144, 255, 0.1);
    color: var(--processing-color);
    border: 1px solid rgba(24, 144, 255, 0.2);
}

.mode-badge.cooperation {
    background: rgba(82, 196, 26, 0.1);
    color: var(--callback-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

/* 督办标识样式 */
.supervise-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.supervise-badge.none {
    display: none;
}

.supervise-badge.general {
    background: rgba(24, 144, 255, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(24, 144, 255, 0.2);
}

.supervise-badge.important {
    background: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 77, 79, 0.2);
}

.supervise-badge.leader {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #8b6914;
    border: 1px solid #d4af37;
}

/* VIP标识 */
.vip-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #8b6914;
    border: 1px solid #d4af37;
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

/* 时限状态样式 */
.time-limit-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.time-limit-badge.normal {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

.time-limit-badge.warning {
    background: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(250, 173, 20, 0.2);
}

.time-limit-badge.danger {
    background: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 77, 79, 0.2);
    animation: blink 1s infinite;
}

.time-limit-badge.success {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
}

/* 超时闪烁动画 */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* 标签组件 */
.tag {
    display: inline-block;
    padding: 2px 6px;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    margin-right: var(--spacing-xs);
    margin-bottom: 2px;
}

.tag.primary {
    background: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

.tag.success {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
}

.tag.warning {
    background: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
}

.tag.error {
    background: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
}

/* 满意度显示 */
.satisfaction-display {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
}

.satisfaction-stars {
    color: #faad14;
}

.satisfaction-text {
    color: var(--text-secondary);
}

.satisfaction-text.satisfied {
    color: var(--success-color);
}

.satisfaction-text.unsatisfied {
    color: var(--error-color);
}

/* 协办进度显示 */
.cooperation-progress {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.cooperation-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
}

.cooperation-role {
    font-weight: 500;
    min-width: 40px;
}

.cooperation-unit {
    color: var(--text-secondary);
    flex: 1;
}

.cooperation-status {
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 10px;
}

.cooperation-status.pending {
    background: rgba(250, 140, 22, 0.1);
    color: var(--pending-color);
}

.cooperation-status.processing {
    background: rgba(24, 144, 255, 0.1);
    color: var(--processing-color);
}

.cooperation-status.completed {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
}

/* 工单关联显示 */
.relation-display {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px 6px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.relation-display.main {
    background: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

.relation-display.merged {
    background: rgba(140, 140, 140, 0.1);
    color: var(--text-tertiary);
}

.relation-display.parent {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
}

.relation-display.child {
    background: rgba(114, 46, 209, 0.1);
    color: var(--reviewing-color);
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.action-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 2px;
    white-space: nowrap;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.action-btn.primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-hover);
}

.action-btn.success {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.action-btn.success:hover {
    opacity: 0.8;
}

.action-btn.warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.action-btn.warning:hover {
    opacity: 0.8;
}

.action-btn.danger {
    background: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

.action-btn.danger:hover {
    opacity: 0.8;
}

/* 更多操作下拉菜单 */
.more-actions {
    position: relative;
    display: inline-block;
}

.more-actions-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-medium);
    z-index: 10;
    min-width: 120px;
    display: none;
}

.more-actions-menu.show {
    display: block;
}

.more-actions-item {
    display: block;
    width: 100%;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    transition: background-color var(--transition-fast);
}

.more-actions-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.more-actions-item:first-child {
    border-top-left-radius: var(--border-radius-sm);
    border-top-right-radius: var(--border-radius-sm);
}

.more-actions-item:last-child {
    border-bottom-left-radius: var(--border-radius-sm);
    border-bottom-right-radius: var(--border-radius-sm);
}

/* 加载状态 */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-fast);
    z-index: 100;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 对话框样式 */
.dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-lg);
}

.dialog.active {
    display: flex;
}

.dialog-content {
    background: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: dialogSlideIn 0.3s ease;
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dialog-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
}

.dialog-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.dialog-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.dialog-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.dialog-body {
    padding: var(--spacing-lg);
    flex: 1;
    overflow-y: auto;
}

.dialog-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    background: var(--bg-secondary);
}

.dialog-footer button {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.btn-cancel {
    background: white;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.btn-cancel:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
}

.btn-confirm {
    background: var(--primary-color);
    border: 1px solid var(--primary-color);
    color: white;
}

.btn-confirm:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-danger {
    background: var(--error-color);
    border: 1px solid var(--error-color);
    color: white;
}

.btn-danger:hover {
    background: #ff7875;
    border-color: #ff7875;
}

/* 表单组件 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.form-group select,
.form-group textarea,
.form-group input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.form-group select:focus,
.form-group textarea:focus,
.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

.form-group select {
    background: white;
    cursor: pointer;
}

/* 选中的工单列表 */
.selected-tickets-list {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    max-height: 200px;
    overflow-y: auto;
}

.selected-tickets-list h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.selected-ticket-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-light);
    font-size: var(--font-size-xs);
}

.selected-ticket-item:last-child {
    border-bottom: none;
}

.ticket-info {
    flex: 1;
}

.ticket-no {
    font-weight: 600;
    color: var(--primary-color);
}

.ticket-title {
    color: var(--text-secondary);
    margin-top: 2px;
}

.ticket-status {
    font-size: 10px;
}

/* 删除警告 */
.delete-warning {
    background: rgba(255, 77, 79, 0.1);
    border: 1px solid rgba(255, 77, 79, 0.3);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    color: var(--error-color);
}

.delete-warning i {
    margin-right: var(--spacing-xs);
    font-size: var(--font-size-lg);
}

.delete-warning h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
}

.delete-warning p {
    margin: 0;
    font-size: var(--font-size-xs);
}

/* 遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}
