/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 主体样式 */
body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

/* 容器样式 */
.container {
    max-width: 95vw;
    width: 100%;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 300;
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 内容区域 */
.content {
    padding: 20px 30px;
}

/* 阶段选择器 */
.stage-selector {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 10px;
}

.stage-btn {
    background: #ecf0f1;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

.stage-btn:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

.stage-btn.active {
    background: #e74c3c;
    color: white;
}

/* 图例样式 */
.legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px 0;
    padding: 20px;
    background: #ecf0f1;
    border-radius: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 3px;
}

/* 流程图容器 */
.mermaid-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 30px;
    margin: 20px 0;
    overflow: hidden;
    min-height: 600px;
    width: 100%;
    position: relative;
}

.mermaid-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    border-radius: 0;
    margin: 0;
    padding: 20px;
    background: #f8f9fa;
}

.diagram-wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    cursor: grab;
}

.diagram-wrapper:active {
    cursor: grabbing;
}

.diagram-wrapper #mermaid-diagram {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.diagram-wrapper svg {
    transform-origin: 0 0;
    transition: transform 0.3s ease;
}

/* 控制按钮组 */
.diagram-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
    z-index: 1000;
}

.control-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    color: #495057;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.control-btn:hover {
    background: white;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.control-btn:active {
    transform: translateY(0);
}

/* 导出按钮特殊样式 */
.control-btn[onclick="exportToPNG()"] {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border-color: #27ae60;
}

.control-btn[onclick="exportToPNG()"]:hover {
    background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
    color: white;
    border-color: #229954;
}

/* 缩放信息显示 */
.zoom-info {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    color: #6c757d;
    border: 1px solid #dee2e6;
    z-index: 1000;
}

/* 阶段详情 */
.stage-details {
    margin-top: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 5px solid #3498db;
}

.stage-details h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.stage-details ul {
    list-style: none;
    padding-left: 0;
}

.stage-details li {
    margin-bottom: 10px;
    padding: 10px;
    background: white;
    border-radius: 5px;
    border-left: 3px solid #3498db;
}

/* 场景样式 */
.scenario {
    margin: 15px 0;
    padding: 15px;
    border-radius: 8px;
}

.scenario-a { 
    background: #d5f4e6; 
    border-left: 4px solid #27ae60; 
}

.scenario-b { 
    background: #fef9e7; 
    border-left: 4px solid #f39c12; 
}

.scenario-c { 
    background: #ebf3fd; 
    border-left: 4px solid #3498db; 
}

.scenario-d { 
    background: #fdeaea; 
    border-left: 4px solid #e74c3c; 
}

.scenario h4 {
    margin-bottom: 10px;
    color: #2c3e50;
}

/* 主协办协同选择器 */
.collaborative-selector {
    margin: 30px 0;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    border: 2px solid #dee2e6;
}

.collaborative-selector h3 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3em;
    font-weight: 600;
}

.collaborative-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
}

.collab-btn {
    background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(142, 68, 173, 0.3);
}

.collab-btn:hover {
    background: linear-gradient(135deg, #7d3c98 0%, #8e44ad 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(142, 68, 173, 0.4);
}

.collab-btn.active {
    background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
    box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
}

/* 帮助弹窗样式 */
.help-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.help-content {
    background: white;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.help-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px 12px;
    border-bottom: 1px solid #dee2e6;
    flex-shrink: 0;
}

.help-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3em;
}

.close-help {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-help:hover {
    background: #f8f9fa;
    color: #495057;
}

.help-body {
    padding: 15px 20px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

/* 自定义滚动条样式 */
.help-body::-webkit-scrollbar {
    width: 6px;
}

.help-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.help-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.help-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.help-body h4 {
    color: #495057;
    margin: 15px 0 10px 0;
    font-size: 1.1em;
}

.help-body ul {
    margin: 0 0 15px 0;
    padding-left: 20px;
}

.help-body li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.help-body strong {
    color: #2c3e50;
}

/* 导出选项样式 */
.export-option {
    margin-bottom: 20px;
}

.export-option h4 {
    margin-bottom: 12px;
    color: #2c3e50;
    font-size: 1em;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 6px;
}

.export-format-group,
.export-quality-group,
.export-bg-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.export-radio {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-radio:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

.export-radio input[type="radio"] {
    margin: 0;
    margin-top: 2px;
}

.export-radio input[type="radio"]:checked + span {
    color: #3498db;
    font-weight: 600;
}

.export-radio span {
    font-weight: 500;
    color: #2c3e50;
}

.export-radio small {
    display: block;
    color: #6c757d;
    font-size: 0.85em;
    margin-top: 2px;
}

.export-radio:has(input:checked) {
    border-color: #3498db;
    background: #e3f2fd;
}

.export-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
    flex-shrink: 0;
}

.export-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.export-btn-primary {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.export-btn-primary:hover {
    background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.export-btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.export-btn-secondary:hover {
    background: #e9ecef;
    color: #495057;
}

/* 页脚样式 */
.footer {
    text-align: center;
    padding: 20px;
    background: #34495e;
    color: white;
    font-size: 14px;
}
