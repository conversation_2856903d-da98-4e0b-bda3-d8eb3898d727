/**
 * 双核信息显示模块
 * 负责管理实时语音转写和对话摘要功能
 */

class DualInfoManager {
    constructor() {
        this.transcriptMessages = [];
        this.activeTimers = []; // 跟踪所有活动的定时器
    }

    /**
     * 初始化双核信息显示
     */
    init() {
        // 初始化时隐藏实时功能
        this.hideRealTimeFeatures();
    }

    /**
     * 创建一个可管理的定时器
     * @param {Function} callback - 回调函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {number} 定时器ID
     */
    createManagedTimer(callback, delay) {
        const timerId = setTimeout(() => {
            // 执行回调
            callback();
            // 从活动定时器列表中移除
            this.removeTimer(timerId);
        }, delay);

        // 添加到活动定时器列表
        this.activeTimers.push(timerId);
        return timerId;
    }

    /**
     * 从活动定时器列表中移除定时器
     * @param {number} timerId - 定时器ID
     */
    removeTimer(timerId) {
        const index = this.activeTimers.indexOf(timerId);
        if (index > -1) {
            this.activeTimers.splice(index, 1);
        }
    }

    /**
     * 清除所有活动的定时器
     */
    clearAllTimers() {
        this.activeTimers.forEach(timerId => {
            clearTimeout(timerId);
        });
        this.activeTimers = [];
    }

    /**
     * 显示实时功能（电话接通后）
     */
    showRealTimeFeatures() {
        // 隐藏右侧待机显示
        const rightStandby = document.getElementById('rightStandby');
        if (rightStandby) {
            rightStandby.style.display = 'none';
        }

        // 显示双核信息呈现区域的实时内容
        const transcriptPlaceholder = document.getElementById('transcriptPlaceholder');
        const transcriptContent = document.getElementById('transcriptContent');

        if (transcriptPlaceholder) transcriptPlaceholder.style.display = 'none';
        if (transcriptContent) transcriptContent.style.display = 'block';

        // 显示双核信息呈现区域
        const dualInfoDisplay = document.getElementById('dualInfoDisplay');
        if (dualInfoDisplay) {
            dualInfoDisplay.style.display = 'grid';
        }

        // 更新状态文本为工作状态
        this.updateStatusTexts();

        // 注意：右侧区域的内容（AI辅助、知识库、话术等）将根据语音转写内容逐步显示
        // 不在这里立即显示，而是等待语音转写触发相应的分析
    }

    /**
     * 更新状态文本为工作状态
     */
    updateStatusTexts() {
        // 更新语音转写状态
        const transcriptStatusText = document.querySelector('.voice-transcript-box .status-text');
        const transcriptStatusIndicator = document.querySelector('.voice-transcript-box .status-indicator');
        if (transcriptStatusText) {
            transcriptStatusText.textContent = '逐字逐句记录中...';
        }
        if (transcriptStatusIndicator) {
            transcriptStatusIndicator.classList.add('active');
        }

        // 更新对话摘要状态
        const summaryStatusText = document.querySelector('.conversation-summary-box .status-text');
        const summaryStatusIndicator = document.querySelector('.conversation-summary-box .status-indicator');
        if (summaryStatusText) {
            summaryStatusText.textContent = '智能提炼中...';
        }
        if (summaryStatusIndicator) {
            summaryStatusIndicator.classList.add('active');
        }
    }

    /**
     * 隐藏实时功能（通话结束后）
     */
    hideRealTimeFeatures() {
        // 显示右侧待机界面
        const rightStandby = document.getElementById('rightStandby');
        if (rightStandby) {
            rightStandby.style.display = 'block';
        }

        // 隐藏双核信息呈现区域的实时内容，显示占位内容
        const transcriptContent = document.getElementById('transcriptContent');
        const transcriptPlaceholder = document.getElementById('transcriptPlaceholder');

        if (transcriptContent) transcriptContent.style.display = 'none';
        if (transcriptPlaceholder) transcriptPlaceholder.style.display = 'flex';

        // 清空对话摘要数据
        this.clearSummaryData();

        // 重置状态文本
        this.resetStatusTexts();

        // 隐藏实时内容，显示占位内容
        const keywordsContent = document.getElementById('keywordsContent');
        const intentContent = document.getElementById('intentContent');
        const aiPlaceholder = document.getElementById('aiPlaceholder');

        if (keywordsContent) keywordsContent.style.display = 'none';
        if (intentContent) intentContent.style.display = 'none';
        if (aiPlaceholder) aiPlaceholder.style.display = 'block';

        const kbContent = document.getElementById('kbContent');
        const kbPlaceholder = document.getElementById('kbPlaceholder');

        if (kbContent) kbContent.style.display = 'none';
        if (kbPlaceholder) kbPlaceholder.style.display = 'block';

        const scriptsContent = document.getElementById('scriptsContent');
        const scriptsPlaceholder = document.getElementById('scriptsPlaceholder');

        if (scriptsContent) scriptsContent.style.display = 'none';
        if (scriptsPlaceholder) scriptsPlaceholder.style.display = 'block';

        const similarContent = document.getElementById('similarContent');
        const similarPlaceholder = document.getElementById('similarPlaceholder');

        if (similarContent) similarContent.style.display = 'none';
        if (similarPlaceholder) similarPlaceholder.style.display = 'block';
    }

    /**
     * 重置状态文本为等待状态
     */
    resetStatusTexts() {
        // 重置语音转写状态
        const transcriptStatusText = document.querySelector('.voice-transcript-box .status-text');
        const transcriptStatusIndicator = document.querySelector('.voice-transcript-box .status-indicator');
        if (transcriptStatusText) {
            transcriptStatusText.textContent = '等待通话开始';
        }
        if (transcriptStatusIndicator) {
            transcriptStatusIndicator.classList.remove('active');
        }

        // 重置对话摘要状态
        const summaryStatusText = document.querySelector('.conversation-summary-box .status-text');
        const summaryStatusIndicator = document.querySelector('.conversation-summary-box .status-indicator');
        if (summaryStatusText) {
            summaryStatusText.textContent = '等待通话开始';
        }
        if (summaryStatusIndicator) {
            summaryStatusIndicator.classList.remove('active');
        }
    }

    /**
     * 开始双核信息呈现更新
     */
    startDisplay() {
        // 先启动语音转写，然后基于转写内容生成摘要和推荐
        this.startVoiceTranscript();
    }

    /**
     * 开始实时语音转写
     */
    startVoiceTranscript() {
        // 模拟对话消息的逐步添加，每条消息都会触发相应的分析
        const messages = [
            {
                speaker: 'agent',
                text: '好的，请您详细说明一下具体情况。',
                delay: 3000,
                triggerAnalysis: false // 开场白不触发分析
            },
            {
                speaker: 'citizen',
                text: '就是阳光小区南门那里，每次下雨就积水很严重，都没法走路了。',
                delay: 6000,
                triggerAnalysis: true, // 第一次市民描述，开始分析
                analysisType: 'initial'
            },
            {
                speaker: 'agent',
                text: '我了解了，请问积水大概有多深？是否影响到了居民楼？',
                delay: 9000,
                triggerAnalysis: false
            },
            {
                speaker: 'citizen',
                text: '有二三十厘米深吧，主要是影响出行，希望能尽快处理。',
                delay: 12000,
                triggerAnalysis: true, // 补充信息，更新分析
                analysisType: 'update'
            },
            {
                speaker: 'agent',
                text: '好的，我已经记录下您的情况，我们会立即为您处理这个问题。',
                delay: 15000,
                triggerAnalysis: true, // 对话结束，生成最终决策
                analysisType: 'final'
            }
        ];

        messages.forEach(msg => {
            this.createManagedTimer(() => {
                this.addTranscriptMessage(msg.speaker, msg.text);

                // 根据消息内容触发相应的分析
                if (msg.triggerAnalysis) {
                    this.triggerAnalysis(msg.analysisType, msg.text);
                }
            }, msg.delay);
        });
    }

    /**
     * 触发基于语音内容的分析
     * @param {string} analysisType - 分析类型 (initial/update/final)
     * @param {string} text - 触发分析的文本内容
     */
    triggerAnalysis(analysisType, text) {
        switch (analysisType) {
            case 'initial':
                // 第一次分析：基础信息识别
                this.createManagedTimer(() => {
                    this.updateSummaryContent({
                        '核心诉求': '反映小区门口积水问题',
                        '情绪状态': '理性沟通，配合度高'
                    });
                    this.showAIAssistance(); // 显示AI辅助内容
                }, 1000);
                break;

            case 'update':
                // 更新分析：补充详细信息
                this.createManagedTimer(() => {
                    this.updateSummaryContent({
                        '问题详情': '阳光小区南门积水严重，深度约20-30厘米',
                        '紧急程度': '中等 - 影响出行但无安全隐患',
                        '关键信息': '雨后积水、持续性问题'
                    });
                    this.showKnowledgeBase(); // 显示知识库推荐
                }, 1000);
                break;

            case 'final':
                // 最终分析：生成决策建议
                this.createManagedTimer(() => {
                    this.updateSummaryContent({
                        '问题详情': '阳光小区南门积水严重，深度约20-30厘米，影响居民出行',
                        '建议分类': '市政设施 -> 排水设施',
                        '推荐承办': '市政排水管理处',
                        '关键信息': '雨后积水、持续性问题、需现场勘查',
                        '决策建议': '建议立即派单，安排现场勘查，预计处理时间3-5个工作日'
                    });
                    this.showScriptsAndSimilar(); // 显示话术和相似工单
                    this.notifyTicketReady(); // 通知可以创建工单
                }, 1000);
                break;
        }
    }

    /**
     * 添加转写消息
     * @param {string} speaker - 说话人类型 (agent/citizen)
     * @param {string} text - 消息内容
     */
    addTranscriptMessage(speaker, text) {
        const transcriptContent = document.getElementById('transcriptContent');
        if (!transcriptContent) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `transcript-message ${speaker}`;

        const now = new Date();
        const timestamp = now.toTimeString().slice(0, 8);

        messageDiv.innerHTML = `
            <span class="speaker">${speaker === 'agent' ? '坐席' : '市民'}：</span>
            <span class="message">${text}</span>
            <span class="timestamp">${timestamp}</span>
        `;

        transcriptContent.appendChild(messageDiv);
        transcriptContent.scrollTop = transcriptContent.scrollHeight;

        // 添加到消息数组
        this.transcriptMessages.push({ speaker, text, timestamp });
    }

    /**
     * 显示AI辅助内容
     */
    showAIAssistance() {
        // 显示智能辅助内容
        const aiPlaceholder = document.getElementById('aiPlaceholder');
        const keywordsContent = document.getElementById('keywordsContent');
        const intentContent = document.getElementById('intentContent');

        if (aiPlaceholder) aiPlaceholder.style.display = 'none';
        if (keywordsContent) keywordsContent.style.display = 'block';
        if (intentContent) intentContent.style.display = 'block';
    }

    /**
     * 显示知识库推荐
     */
    showKnowledgeBase() {
        const kbPlaceholder = document.getElementById('kbPlaceholder');
        const kbContent = document.getElementById('kbContent');

        if (kbPlaceholder) kbPlaceholder.style.display = 'none';
        if (kbContent) kbContent.style.display = 'block';
    }

    /**
     * 显示话术推荐和相似工单
     */
    showScriptsAndSimilar() {
        // 显示话术推荐内容
        const scriptsPlaceholder = document.getElementById('scriptsPlaceholder');
        const scriptsContent = document.getElementById('scriptsContent');

        if (scriptsPlaceholder) scriptsPlaceholder.style.display = 'none';
        if (scriptsContent) scriptsContent.style.display = 'block';

        // 显示相似工单内容
        const similarPlaceholder = document.getElementById('similarPlaceholder');
        const similarContent = document.getElementById('similarContent');

        if (similarPlaceholder) similarPlaceholder.style.display = 'none';
        if (similarContent) similarContent.style.display = 'block';
    }

    /**
     * 通知可以创建工单
     */
    notifyTicketReady() {
        // 2秒后显示新建工单按钮的提示
        this.createManagedTimer(() => {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show(
                    'AI分析完成，已生成决策建议，可点击"新建工单"进行智能填单',
                    'info'
                );
            }
        }, 2000);
    }



    /**
     * 更新摘要内容
     * @param {Object} updates - 要更新的内容
     */
    updateSummaryContent(updates) {
        const summaryContent = document.querySelector('#summaryContent');
        const summaryItems = summaryContent.querySelectorAll('.summary-item');

        summaryItems.forEach(item => {
            const label = item.querySelector('.summary-label').textContent.replace('：', '');
            const valueElement = item.querySelector('.summary-value');

            if (updates[label]) {
                valueElement.textContent = updates[label];
                valueElement.removeAttribute('data-empty');
                // 添加更新动画
                valueElement.style.background = '#fff3cd';
                this.createManagedTimer(() => {
                    valueElement.style.background = 'transparent';
                }, 1000);
            }
        });

        // 添加 has-content 类来显示滚动条
        if (summaryContent) {
            summaryContent.classList.add('has-content');
        }
    }

    /**
     * 初始化对话摘要数据（清空状态）
     */
    initializeSummaryData() {
        // 确保所有摘要字段都是空状态，等待语音转写触发分析
        this.clearSummaryData();
    }

    /**
     * 清空对话摘要数据
     */
    clearSummaryData() {
        // 首先清除所有活动的定时器
        this.clearAllTimers();

        const fields = [
            'coreRequest', 'problemDetails', 'emotionState', 'urgencyLevel',
            'suggestedCategory', 'recommendedDept', 'keyInfo', 'decisionSuggestion'
        ];

        fields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.textContent = '--';
                element.setAttribute('data-empty', 'true');
                element.setAttribute('data-filled', 'false');

                // 清除所有内联样式，让CSS规则生效
                element.style.color = '';
                element.style.fontStyle = '';
                element.style.fontWeight = '';
                element.style.textShadow = '';
                element.style.background = '';
                element.style.padding = '';
                element.style.borderRadius = '';
                element.style.border = '';
                element.style.transition = '';
            }
        });

        // 移除摘要内容区域的滚动条
        const summaryContent = document.querySelector('.summary-content');
        if (summaryContent) {
            summaryContent.classList.remove('has-content');
        }

        // 清空转写消息
        this.transcriptMessages = [];
        const transcriptContent = document.getElementById('transcriptContent');
        if (transcriptContent) {
            transcriptContent.innerHTML = '';
        }
    }

    /**
     * 获取转写消息
     */
    getTranscriptMessages() {
        return this.transcriptMessages;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DualInfoManager;
}
