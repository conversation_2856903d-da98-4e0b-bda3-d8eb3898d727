<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单表单 - 12345智慧政务平台</title>
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/form.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <div class="container">
        <header class="form-header">
            <h1>12345热线工单表单</h1>
            <div class="form-status">
                <span class="status-indicator">草稿</span>
                <span class="auto-save-status">自动保存中...</span>
            </div>
        </header>

        <form id="ticketForm" class="ticket-form">
            <!-- 一、工单基础信息表单区域 -->
            <section class="form-section" id="basicInfo">
                <h2 class="section-title">一、工单基础信息</h2>
                
                <!-- 1.1 工单标识信息 -->
                <div class="form-group-section">
                    <h3 class="subsection-title">工单标识信息</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">工单编号</label>
                            <div class="input-group">
                                <input type="text" id="ticketNumber" class="form-input" readonly value="WD20241201000001">
                                <button type="button" class="btn btn-outline btn-small copy-btn" data-target="ticketNumber">
                                    <span class="btn-icon">📋</span>
                                    <span class="btn-text">复制</span>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">创建时间</label>
                            <input type="text" id="createTime" class="form-input" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">创建人</label>
                            <input type="text" id="creator" class="form-input" readonly value="张三 (001)">
                        </div>
                    </div>
                </div>

                <!-- 1.2 工单来源信息 -->
                <div class="form-group-section">
                    <h3 class="subsection-title">工单来源信息</h3>
                    <div class="form-row">
                        <div class="form-group required">
                            <label class="form-label">工单来源</label>
                            <select id="ticketSource" class="form-select" required>
                                <option value="电话来电" selected>电话来电</option>
                                <option value="网站留言">网站留言</option>
                                <option value="手机APP">手机APP</option>
                                <option value="微信公众号">微信公众号</option>
                                <option value="现场接待">现场接待</option>
                                <option value="领导批示">领导批示</option>
                                <option value="媒体曝光">媒体曝光</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">来电号码</label>
                            <input type="tel" id="phoneNumber" class="form-input" placeholder="请输入来电号码">
                            <div class="form-error" id="phoneNumberError"></div>
                        </div>
                    </div>
                </div>

                <!-- 1.3 工单类型分类 -->
                <div class="form-group-section">
                    <h3 class="subsection-title">工单类型分类</h3>
                    <div class="form-group required">
                        <label class="form-label">工单类型</label>
                        <div class="radio-button-group" id="ticketType">
                            <label class="radio-button">
                                <input type="radio" name="ticketType" value="投诉">
                                <span class="radio-icon">📢</span>
                                <span class="radio-text">投诉</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="ticketType" value="建议">
                                <span class="radio-icon">💡</span>
                                <span class="radio-text">建议</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="ticketType" value="咨询">
                                <span class="radio-icon">❓</span>
                                <span class="radio-text">咨询</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="ticketType" value="求助">
                                <span class="radio-icon">🆘</span>
                                <span class="radio-text">求助</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="ticketType" value="表扬">
                                <span class="radio-icon">👍</span>
                                <span class="radio-text">表扬</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="ticketType" value="举报">
                                <span class="radio-icon">🚨</span>
                                <span class="radio-text">举报</span>
                            </label>
                        </div>
                        <div class="form-error" id="ticketTypeError"></div>
                    </div>

                    <div class="form-group required">
                        <label class="form-label">紧急程度</label>
                        <div class="radio-button-group priority-group" id="priority">
                            <label class="radio-button priority-normal">
                                <input type="radio" name="priority" value="普通" checked>
                                <span class="radio-text">普通</span>
                            </label>
                            <label class="radio-button priority-urgent">
                                <input type="radio" name="priority" value="紧急">
                                <span class="radio-text">紧急</span>
                            </label>
                            <label class="radio-button priority-critical">
                                <input type="radio" name="priority" value="特急">
                                <span class="radio-text">特急</span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 二、市民信息表单区域 -->
            <section class="form-section" id="citizenInfo">
                <h2 class="section-title">二、市民信息</h2>

                <div class="form-group-section">
                    <h3 class="subsection-title">市民基本信息</h3>
                    <div class="form-row">
                        <div class="form-group required">
                            <label class="form-label">姓名</label>
                            <input type="text" id="citizenName" class="form-input" minlength="2" maxlength="20" placeholder="请输入真实姓名" required>
                            <div class="form-error" id="citizenNameError"></div>
                        </div>
                        <div class="form-group required">
                            <label class="form-label">联系电话</label>
                            <input type="tel" id="citizenPhone" class="form-input" placeholder="请输入11位手机号码" required>
                            <div class="repeat-phone-warning" id="repeatPhoneWarning" style="display: none;">
                                <span class="warning-icon">⚠️</span>
                                <span>检测到重复来电号码</span>
                            </div>
                            <div class="form-error" id="citizenPhoneError"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">身份证号</label>
                            <input type="text" id="idCard" class="form-input" placeholder="选填，用于身份验证" maxlength="18">
                            <div class="form-error" id="idCardError"></div>
                        </div>
                    </div>
                </div>

                <div class="form-group-section">
                    <h3 class="subsection-title">联系方式</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">备用电话</label>
                            <input type="tel" id="backupPhone" class="form-input" placeholder="手机号或固话号码">
                        </div>
                        <div class="form-group">
                            <label class="form-label">电子邮箱</label>
                            <input type="email" id="email" class="form-input" placeholder="请输入邮箱地址">
                        </div>
                        <div class="form-group">
                            <label class="form-label">微信号</label>
                            <input type="text" id="wechat" class="form-input" placeholder="请输入微信号">
                        </div>
                    </div>
                </div>

                <div class="form-group-section">
                    <h3 class="subsection-title">地址信息</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">详细地址</label>
                            <input type="text" id="citizenAddress" class="form-input" maxlength="200" placeholder="请输入详细地址">
                        </div>
                        <div class="form-group">
                            <label class="form-label">邮政编码</label>
                            <input type="text" id="zipCode" class="form-input" pattern="[0-9]{6}" placeholder="请输入6位邮政编码">
                        </div>
                    </div>
                </div>

                <div class="form-group-section">
                    <h3 class="subsection-title">客户类型</h3>
                    <div class="form-group required">
                        <label class="form-label">客户类型</label>
                        <div class="radio-button-group" id="citizenType">
                            <label class="radio-button">
                                <input type="radio" name="citizenType" value="个人" checked>
                                <span class="radio-text">个人</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="citizenType" value="企业">
                                <span class="radio-text">企业</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="citizenType" value="社会组织">
                                <span class="radio-text">社会组织</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="citizenType" value="政府机关">
                                <span class="radio-text">政府机关</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">特殊标识</label>
                        <div class="checkbox-group" id="specialIdentity">
                            <label class="checkbox-label">
                                <input type="checkbox" value="人大代表">
                                <span class="checkbox-text">人大代表</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="政协委员">
                                <span class="checkbox-text">政协委员</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="劳动模范">
                                <span class="checkbox-text">劳动模范</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="退役军人">
                                <span class="checkbox-text">退役军人</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="残疾人">
                                <span class="checkbox-text">残疾人</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="低保户">
                                <span class="checkbox-text">低保户</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="其他">
                                <span class="checkbox-text">其他</span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 三、诉求内容表单区域 -->
            <section class="form-section" id="contentInfo">
                <h2 class="section-title">三、诉求内容</h2>

                <div class="form-group required">
                    <label class="form-label">诉求标题</label>
                    <input type="text" id="title" class="form-input" minlength="5" maxlength="100" placeholder="请简要描述问题的核心内容">
                    <div class="char-counter">
                        <span id="titleCounter">0</span>/100
                    </div>
                    <div class="form-error" id="titleError"></div>
                </div>

                <div class="form-group required">
                    <label class="form-label">详细描述</label>
                    <textarea id="description" class="form-textarea" minlength="20" maxlength="2000" rows="6" placeholder="请详细描述问题的具体情况、发生时间、地点等信息"></textarea>
                    <div class="char-counter">
                        <span id="descriptionCounter">0</span>/2000
                    </div>
                    <div class="form-error" id="descriptionError"></div>
                </div>

                <div class="form-group-section">
                    <h3 class="subsection-title">问题分类</h3>
                    <div class="form-row">
                        <div class="form-group required">
                            <label class="form-label">一级分类</label>
                            <select id="category1" class="form-select" required>
                                <option value="">请选择一级分类</option>
                            </select>
                        </div>
                        <div class="form-group required">
                            <label class="form-label">二级分类</label>
                            <select id="category2" class="form-select" required disabled>
                                <option value="">请先选择一级分类</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">三级分类</label>
                            <select id="category3" class="form-select" disabled>
                                <option value="">请先选择二级分类</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">业务标签</label>
                    <div class="tag-cloud" id="businessTags">
                        <!-- 标签将通过JS动态生成 -->
                    </div>
                    <div class="form-help">最多选择5个标签</div>
                </div>

                <div class="form-group">
                    <label class="form-label">自定义标签</label>
                    <div class="tag-input-container">
                        <input type="text" id="customTagInput" class="form-input" placeholder="输入自定义标签，按回车添加" maxlength="10">
                        <button type="button" class="btn btn-primary btn-small add-tag-btn">
                            <span class="btn-icon">+</span>
                            <span class="btn-text">添加</span>
                        </button>
                    </div>
                    <div class="custom-tags" id="customTags"></div>
                    <div class="form-help">最多添加3个自定义标签，每个标签2-10字符</div>
                </div>

                <div class="form-group-section">
                    <h3 class="subsection-title">涉及地点</h3>

                    <!-- 行政区划选择 -->
                    <div class="form-row">
                        <div class="form-group required">
                            <label class="form-label">省份</label>
                            <select id="province" class="form-select" required>
                                <option value="">请选择省份</option>
                            </select>
                            <div class="form-error" id="provinceError"></div>
                        </div>
                        <div class="form-group required">
                            <label class="form-label">城市</label>
                            <select id="city" class="form-select" required disabled>
                                <option value="">请先选择省份</option>
                            </select>
                            <div class="form-error" id="cityError"></div>
                        </div>
                        <div class="form-group required">
                            <label class="form-label">区/县</label>
                            <select id="district" class="form-select" required disabled>
                                <option value="">请先选择城市</option>
                            </select>
                            <div class="form-error" id="districtError"></div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">街道/乡镇</label>
                            <select id="street" class="form-select" disabled>
                                <option value="">请先选择区/县</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">社区/村</label>
                            <select id="community" class="form-select" disabled>
                                <option value="">请先选择街道/乡镇</option>
                            </select>
                        </div>
                    </div>

                    <!-- 详细地址 -->
                    <div class="form-group">
                        <label class="form-label">详细地址</label>
                        <div class="address-input-container">
                            <input type="text" id="detailAddress" class="form-input" maxlength="200" placeholder="请输入详细地址（如门牌号、楼栋号等）">
                            <button type="button" class="btn btn-secondary btn-small map-btn">
                                <span class="btn-icon">🗺️</span>
                                <span class="btn-text">地图选择</span>
                            </button>
                        </div>
                        <div class="address-suggestions" id="addressSuggestions"></div>
                        <div class="form-help">补充具体的门牌号、楼栋号等详细信息，最多200字符</div>
                        <div class="char-counter">
                            <span id="detailAddressCounter">0</span>/200
                        </div>
                    </div>

                    <!-- 地图组件 -->
                    <div class="form-group">
                        <div class="map-container" id="mapContainer" style="display: none;">
                            <div class="map-placeholder">
                                <div class="map-icon">🗺️</div>
                                <div class="map-text">
                                    <p>地图组件</p>
                                    <p class="map-tips">支持点击选择位置、GPS定位、拖拽调整</p>
                                </div>
                                <div class="map-coordinates" id="mapCoordinates">
                                    <span>经度：--</span>
                                    <span>纬度：--</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 历史地址快速选择 -->
                    <div class="form-group">
                        <label class="form-label">历史地址</label>
                        <div class="history-addresses" id="historyAddresses">
                            <!-- 历史地址将通过JS动态生成 -->
                        </div>
                        <div class="form-help">点击快速选择常用地址</div>
                    </div>
                </div>
            </section>

            <!-- 四、附件上传区域 -->
            <section class="form-section" id="attachmentInfo">
                <h2 class="section-title">四、附件上传</h2>

                <div class="upload-area" id="uploadArea">
                    <div class="upload-placeholder">
                        <div class="upload-icon">📎</div>
                        <div class="upload-text">
                            <p>拖拽文件到此处或<button type="button" class="btn-link" id="selectFiles">点击选择文件</button></p>
                            <p class="upload-tips">支持图片、文档、音频、视频格式，最多10个文件，总大小不超过200MB</p>
                        </div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.doc,.docx,.txt,.mp3,.wav,.m4a,.mp4,.avi,.mov" style="display: none;">
                </div>

                <div class="attachment-list" id="attachmentList">
                    <!-- 附件列表将通过JS动态生成 -->
                </div>
            </section>

            <!-- 五、处理模式区域 -->
            <section class="form-section" id="processModeInfo">
                <h2 class="section-title">五、处理模式</h2>

                <div class="form-group-section">
                    <h3 class="subsection-title">处理模式选择</h3>
                    <div class="form-group required">
                        <label class="form-label">处理模式</label>
                        <div class="radio-button-group" id="processMode">
                            <label class="radio-button">
                                <input type="radio" name="processMode" value="即时办结">
                                <span class="radio-text">即时办结</span>
                                <span class="radio-desc">适用于简单咨询类问题</span>
                            </label>
                            <label class="radio-button">
                                <input type="radio" name="processMode" value="派单处理" checked>
                                <span class="radio-text">派单处理</span>
                                <span class="radio-desc">需要指定承办单位</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group dispatch-only" id="departmentGroup">
                        <label class="form-label required">承办单位</label>
                        <div class="department-selection">
                            <div class="selected-departments" id="selectedDepartments">
                                <div class="department-placeholder">请选择承办单位</div>
                            </div>
                            <div class="department-dropdown" id="departmentDropdown">
                                <input type="text" class="form-input" id="departmentSearch" placeholder="搜索部门名称...">
                                <div class="department-list" id="departmentList">
                                    <!-- 部门列表将通过JS动态生成 -->
                                </div>
                            </div>
                        </div>
                        <div class="form-help">可选择多个承办单位，支持跨部门协同处理</div>
                        <div class="form-error" id="departmentError"></div>
                    </div>

                    <div class="form-group dispatch-only multi-dept-only" id="mainDepartmentGroup">
                        <label class="form-label required">主办单位</label>
                        <select id="mainDepartment" class="form-select">
                            <option value="">请选择主办单位</option>
                        </select>
                        <div class="form-help">负责统筹协调和最终办结的单位</div>
                        <div class="form-error" id="mainDepartmentError"></div>
                    </div>

                    <div class="form-group dispatch-only multi-dept-only" id="coDepartmentGroup">
                        <label class="form-label">协办单位</label>
                        <div id="coDepartmentList" class="tag-list">
                            <div class="empty-co-departments">暂无协办单位</div>
                        </div>
                        <div class="form-help">配合主办单位共同处理工单的其他单位</div>
                    </div>
                </div>

                <!-- 时限设置（仅派单处理模式显示） -->
                <div class="form-group-section dispatch-only" id="deadlineSection">
                    <h3 class="subsection-title">时限设置</h3>
                    <div class="form-row">
                        <div class="form-group required">
                            <label class="form-label">接单时限（工作日）</label>
                            <input type="number" id="acceptDeadline" class="form-input" min="0.5" max="5" step="0.5" placeholder="请输入接单时限（工作日）">
                            <div class="form-help">承办单位必须在此时限内接收工单。范围：0.5-5个工作日</div>
                            <div class="deadline-defaults" style="font-size: 12px; color: #666; margin-top: 4px;">
                                默认值：投诉1天，建议2天，咨询0.5天，求助1天，表扬1天，举报1天
                            </div>
                            <div class="form-error" id="acceptDeadlineError"></div>
                        </div>
                        <div class="form-group required">
                            <label class="form-label">办结时限（工作日）</label>
                            <input type="number" id="finishDeadline" class="form-input" min="1" max="60" step="1" placeholder="请输入办结时限（工作日）">
                            <div class="form-help">承办单位必须在此时限内完成工单处理并办结。范围：1-60个工作日</div>
                            <div class="deadline-defaults" style="font-size: 12px; color: #666; margin-top: 4px;">
                                默认值：投诉15天，建议30天，咨询3天，求助10天，表扬5天，举报20天
                            </div>
                            <div class="form-error" id="finishDeadlineError"></div>
                        </div>
                    </div>
                    <div class="form-group" id="specialDeadlineGroup" style="display: none;">
                        <label class="form-label">特殊时限说明</label>
                        <textarea id="specialDeadlineNote" class="form-textarea" maxlength="200" placeholder="如有特殊时限要求，请说明原因"></textarea>
                        <div class="char-counter">
                            <span id="specialDeadlineCounter">0</span>/200
                        </div>
                    </div>
                </div>
            </section>

            <!-- 即时办结说明区域 -->
            <section class="form-section instant-finish-only" id="instantFinishSection" style="display: none;">
                <h2 class="section-title">办结说明</h2>
                <div class="form-group required">
                    <label class="form-label">办结说明</label>
                    <textarea id="finishNote" class="form-textarea" minlength="10" maxlength="500" rows="4" placeholder="请填写即时办结的处理说明"></textarea>
                    <div class="char-counter">
                        <span id="finishNoteCounter">0</span>/500
                    </div>
                    <div class="form-error" id="finishNoteError"></div>
                </div>
            </section>

            <!-- 表单操作区域 -->
            <section class="form-actions">
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary" id="resetForm">重置表单</button>
                    <button type="button" class="btn btn-secondary" id="saveDraft">暂存草稿</button>
                    <button type="submit" class="btn btn-primary" id="submitTicket">提交工单</button>
                </div>
            </section>
        </form>
    </div>



    <!-- 确认对话框 -->
    <div class="modal" id="confirmModal" style="display: none;">
        <div class="modal-content modal-small">
            <div class="modal-header">
                <h3 id="confirmTitle">确认操作</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="confirmCancel">取消</button>
                <button type="button" class="btn btn-primary" id="confirmOk">确定</button>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/form-validation.js"></script>
    <script src="js/ui-components.js"></script>
    <script src="js/form-handler.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
